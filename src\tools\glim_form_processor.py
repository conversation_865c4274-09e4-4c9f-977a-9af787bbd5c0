#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLIM表单处理器
将用户填写的GLIM表单转换为结构化数据供分析使用
支持新的简化表单格式
"""
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger


class GLIMFormProcessor:
    """GLIM表单处理器"""
    
    def __init__(self):
        """初始化GLIM表单处理器"""
        pass
    
    def process_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理GLIM表单数据（支持简化格式）
        
        Args:
            form_data: 前端提交的表单数据
        
        Returns:
            处理结果，包含分析结果和原始数据
        """
        try:
            logger.info("开始处理GLIM表单数据")
            
            # 检查表单格式并处理
            if self._is_simplified_format(form_data):
                return self._process_simplified_form_data(form_data)
            else:
                return self._process_legacy_form_data(form_data)
            
        except Exception as e:
            logger.error(f"GLIM表单处理失败: {e}")
            return {"error": str(e)}
    
    def _is_simplified_format(self, form_data: Dict[str, Any]) -> bool:
        """检查是否为简化格式"""
        # 简化格式的特征：phenotypic_criteria直接包含布尔值
        phenotypic = form_data.get("phenotypic_criteria", {})
        return isinstance(phenotypic.get("weight_loss"), bool)
    
    def _process_simplified_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理简化格式的表单数据"""
        try:
            # 提取基本信息
            patient_info = form_data.get("patient_info", {})
            
            # 简化的标准评估
            phenotypic = form_data.get("phenotypic_criteria", {})
            etiologic = form_data.get("etiologic_criteria", {})
            severity = form_data.get("severity_criteria", {})
            
            # 计算满足的标准数量
            phenotypic_criteria_met = []
            if phenotypic.get("weight_loss", False):
                phenotypic_criteria_met.append("非自主性体重减轻")
            if phenotypic.get("low_bmi", False):
                phenotypic_criteria_met.append("低BMI")
            if phenotypic.get("muscle_loss", False):
                phenotypic_criteria_met.append("肌肉质量减少")
            
            etiologic_criteria_met = []
            if etiologic.get("food_intake_reduction", False):
                etiologic_criteria_met.append("食物摄入减少或吸收障碍")
            if etiologic.get("disease_inflammation", False):
                etiologic_criteria_met.append("疾病负担或炎症")
            
            severity_criteria_met = []
            if severity.get("severe_weight_loss", False):
                severity_criteria_met.append("体重显著下降")
            if severity.get("severe_bmi", False):
                severity_criteria_met.append("低BMI（重度）")
            
            phenotypic_count = len(phenotypic_criteria_met)
            etiologic_count = len(etiologic_criteria_met)
            severity_count = len(severity_criteria_met)
            
            # 诊断逻辑
            has_phenotypic = phenotypic_count > 0
            has_etiologic = etiologic_count > 0
            is_malnutrition = has_phenotypic and has_etiologic
            
            if not is_malnutrition:
                diagnosis = "未诊断为营养不良"
                confidence = 0.9
                severity_level = "none"
                severity_description = "无营养不良"
            elif severity_count > 0:
                diagnosis = "重度营养不良"
                confidence = 0.95
                severity_level = "severe"
                severity_description = "重度营养不良"
            else:
                diagnosis = "中度营养不良"
                confidence = 0.9
                severity_level = "moderate"
                severity_description = "中度营养不良"
            
            # 构建分析结果
            analysis_result = {
                "patient_info": patient_info,
                "phenotypic_criteria": {
                    "criteria_met": phenotypic_criteria_met,
                    "count": phenotypic_count,
                    "details": phenotypic
                },
                "etiologic_criteria": {
                    "criteria_met": etiologic_criteria_met,
                    "count": etiologic_count,
                    "details": etiologic
                },
                "severity": {
                    "level": severity_level,
                    "description": severity_description,
                    "criteria_met": severity_criteria_met,
                    "count": severity_count,
                    "details": severity
                },
                "diagnosis": diagnosis,
                "confidence": confidence,
                "criteria_summary": {
                    "phenotypic_met": phenotypic_count,
                    "etiologic_met": etiologic_count,
                    "severity_met": severity_count,
                    "diagnosis_criteria_satisfied": is_malnutrition
                },
                "analysis_summary": f"{diagnosis}（表型标准{phenotypic_count}项，病因标准{etiologic_count}项，严重程度标准{severity_count}项）",
                "timestamp": datetime.now().isoformat(),
                "form_notes": form_data.get("notes", "")
            }
            
            logger.info(f"简化GLIM表单处理完成，诊断结果: {diagnosis}")
            
            return {
                "success": True,
                "glim_analysis": analysis_result,
                "raw_form_data": form_data,
                "processing_metadata": {
                    "processed_at": datetime.now().isoformat(),
                    "form_version": "simplified_v1.0",
                    "format_type": "simplified"
                }
            }
            
        except Exception as e:
            logger.error(f"简化GLIM表单处理失败: {e}")
            return {"error": f"简化格式处理失败: {str(e)}"}
    
    def _process_legacy_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理传统格式的表单数据"""
        try:
            logger.info("处理传统格式GLIM表单")
            
            # 提取基本信息
            patient_info = form_data.get("patient_info", {})
            
            # 处理表型标准
            phenotypic_analysis = self._process_legacy_phenotypic_criteria(form_data)
            
            # 处理病因标准
            etiologic_analysis = self._process_legacy_etiologic_criteria(form_data)
            
            # 评估严重程度
            severity_analysis = self._process_legacy_severity_criteria(form_data)
            
            # 生成GLIM诊断
            glim_diagnosis = self._generate_legacy_glim_diagnosis(
                phenotypic_analysis, etiologic_analysis, severity_analysis
            )
            
            # 构建完整分析结果
            analysis_result = {
                "patient_info": patient_info,
                "phenotypic_criteria": phenotypic_analysis,
                "etiologic_criteria": etiologic_analysis, 
                "severity": severity_analysis,
                "diagnosis": glim_diagnosis["diagnosis"],
                "confidence": glim_diagnosis["confidence"],
                "criteria_met": glim_diagnosis["criteria_met"],
                "analysis_summary": glim_diagnosis["summary"],
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"传统GLIM表单处理完成，诊断结果: {glim_diagnosis['diagnosis']}")
            
            return {
                "success": True,
                "glim_analysis": analysis_result,
                "raw_form_data": form_data,
                "processing_metadata": {
                    "processed_at": datetime.now().isoformat(),
                    "form_version": "legacy_v2.0",
                    "format_type": "legacy"
                }
            }
            
        except Exception as e:
            logger.error(f"传统GLIM表单处理失败: {e}")
            return {"error": f"传统格式处理失败: {str(e)}"}
    
    def _process_legacy_phenotypic_criteria(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理传统格式的表型标准"""
        phenotypic = form_data.get("phenotypic_criteria", {})
        criteria_met = []
        
        # 检查体重减轻
        weight_loss = phenotypic.get("weight_loss", {})
        if weight_loss.get("selected", False):
            criteria_met.append("非自主性体重减轻")
        
        # 检查低BMI
        low_bmi = phenotypic.get("low_bmi", {})
        if low_bmi.get("selected", False):
            criteria_met.append("低BMI")
        
        # 检查肌肉质量减少
        muscle_mass = phenotypic.get("muscle_mass_reduction", {})
        if muscle_mass.get("selected", False):
            criteria_met.append("肌肉质量减少")
        
        return {
            "criteria_met": criteria_met,
            "count": len(criteria_met),
            "details": phenotypic
        }
    
    def _process_legacy_etiologic_criteria(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理传统格式的病因标准"""
        etiologic = form_data.get("etiologic_criteria", {})
        criteria_met = []
        
        # 检查食物摄入减少
        food_intake = etiologic.get("food_intake_reduction", {})
        if food_intake.get("selected", False):
            criteria_met.append("食物摄入减少或吸收障碍")
        
        # 检查疾病负担或炎症
        disease = etiologic.get("disease_inflammation", {})
        if disease.get("selected", False):
            criteria_met.append("疾病负担或炎症")
        
        return {
            "criteria_met": criteria_met,
            "count": len(criteria_met),
            "details": etiologic
        }
    
    def _process_legacy_severity_criteria(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理传统格式的严重程度标准"""
        severity_grading = form_data.get("severity_grading", {})
        severe_criteria = severity_grading.get("severe_criteria", {})
        
        criteria_met = []
        
        # 检查体重显著下降
        weight_loss = severe_criteria.get("weight_loss_severe", {})
        if weight_loss.get("selected", False):
            criteria_met.append("体重显著下降")
        
        # 检查低BMI
        low_bmi = severe_criteria.get("low_bmi_severe", {})
        if low_bmi.get("selected", False):
            criteria_met.append("低BMI（重度）")
        
        severity_count = len(criteria_met)
        
        if severity_count > 0:
            level = "severe"
            description = "重度营养不良"
        else:
            level = "moderate"
            description = "中度营养不良"
        
        return {
            "level": level,
            "description": description,
            "criteria_met": criteria_met,
            "count": severity_count
        }
    
    def _generate_legacy_glim_diagnosis(
        self, 
        phenotypic: Dict[str, Any], 
        etiologic: Dict[str, Any], 
        severity: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成传统格式的GLIM诊断"""
        
        has_phenotypic = phenotypic["count"] > 0
        has_etiologic = etiologic["count"] > 0
        is_malnutrition = has_phenotypic and has_etiologic
        
        if not is_malnutrition:
            diagnosis = "未诊断为营养不良"
            confidence = 0.9
        else:
            diagnosis = severity["description"]
            confidence = 0.9 if severity["level"] == "moderate" else 0.95
        
        return {
            "diagnosis": diagnosis,
            "confidence": confidence,
            "criteria_met": {
                "phenotypic": phenotypic["criteria_met"],
                "etiologic": etiologic["criteria_met"],
                "severity": severity["criteria_met"]
            },
            "summary": f"{diagnosis}（表型{phenotypic['count']}项，病因{etiologic['count']}项，严重程度{severity['count']}项）"
        }
    
    def validate_form_data(self, form_data: Dict[str, Any]) -> Dict[str, bool]:
        """验证表单数据"""
        try:
            # 基本验证
            if not isinstance(form_data, dict):
                return {"valid": False, "message": "表单数据格式无效"}
            
            # 检查是否有患者信息
            patient_info = form_data.get("patient_info", {})
            if not patient_info.get("name") or not patient_info.get("age"):
                return {"valid": False, "message": "缺少必要的患者信息"}
            
            return {"valid": True, "message": "表单验证通过"}
            
        except Exception as e:
            return {"valid": False, "message": f"验证失败: {str(e)}"}
    
    def export_to_json(self, analysis_result: Dict[str, Any], filename: Optional[str] = None) -> str:
        """导出分析结果为JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"glim_analysis_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"GLIM分析结果已导出到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"导出失败: {e}")
            return ""
    
    def get_form_template(self) -> Dict[str, Any]:
        """获取简化表单模板"""
        return {
            "patient_info": {
                "name": "",
                "age": 0,
                "gender": "",
                "height": 0.0,
                "current_weight": 0.0,
                "usual_weight": 0.0
            },
            "phenotypic_criteria": {
                "weight_loss": False,
                "low_bmi": False,
                "muscle_loss": False
            },
            "etiologic_criteria": {
                "food_intake_reduction": False,
                "disease_inflammation": False
            },
            "severity_criteria": {
                "severe_weight_loss": False,
                "severe_bmi": False
            },
            "nutrition_screening": False,
            "notes": ""
        }


# 测试函数
def test_glim_processor():
    """测试GLIM处理器"""
    processor = GLIMFormProcessor()
    
    # 测试简化格式
    test_data = {
        "patient_info": {
            "name": "测试患者",
            "age": 65,
            "gender": "男"
        },
        "phenotypic_criteria": {
            "weight_loss": True,
            "low_bmi": False,
            "muscle_loss": True
        },
        "etiologic_criteria": {
            "food_intake_reduction": True,
            "disease_inflammation": False
        },
        "severity_criteria": {
            "severe_weight_loss": False,
            "severe_bmi": False
        }
    }
    
    result = processor.process_form_data(test_data)
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    test_glim_processor()