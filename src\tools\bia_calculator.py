#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BIA数据计算和分析工具
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from loguru import logger


class BIACalculator:
    """BIA数据计算器"""
    
    def __init__(self):
        self.reference_ranges = self._load_reference_ranges()
    
    def _load_reference_ranges(self) -> Dict[str, Any]:
        """加载参考范围配置"""
        return {
            "phase_angle": {
                "男性": {
                    "18-39": {"normal": [5.0, 7.5], "low": [4.0, 5.0], "very_low": [0, 4.0]},
                    "40-59": {"normal": [4.8, 7.0], "low": [3.8, 4.8], "very_low": [0, 3.8]},
                    "60+": {"normal": [4.5, 6.5], "low": [3.5, 4.5], "very_low": [0, 3.5]}
                },
                "女性": {
                    "18-39": {"normal": [4.5, 6.8], "low": [3.5, 4.5], "very_low": [0, 3.5]},
                    "40-59": {"normal": [4.2, 6.5], "low": [3.2, 4.2], "very_low": [0, 3.2]},
                    "60+": {"normal": [4.0, 6.0], "low": [3.0, 4.0], "very_low": [0, 3.0]}
                }
            },
            "asmi": {  # 四肢骨骼肌质量指数
                "男性": {"normal": 7.0, "low": 5.7},
                "女性": {"normal": 5.4, "low": 4.2}
            },
            "body_fat_percentage": {
                "男性": {
                    "18-39": {"normal": [10, 20], "high": [20, 25], "very_high": [25, 100]},
                    "40-59": {"normal": [11, 22], "high": [22, 27], "very_high": [27, 100]},
                    "60+": {"normal": [13, 25], "high": [25, 30], "very_high": [30, 100]}
                },
                "女性": {
                    "18-39": {"normal": [16, 25], "high": [25, 30], "very_high": [30, 100]},
                    "40-59": {"normal": [19, 28], "high": [28, 33], "very_high": [33, 100]},
                    "60+": {"normal": [22, 30], "high": [30, 35], "very_high": [35, 100]}
                }
            }
        }
    
    def load_bia_data(self, file_path: Union[str, Path]) -> Optional[pd.DataFrame]:
        """加载BIA数据文件"""
        try:
            if isinstance(file_path, str):
                file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"BIA数据文件不存在: {file_path}")
                logger.error(f"当前工作目录: {os.getcwd()}")
                logger.error(f"文件绝对路径: {file_path.absolute()}")
                return None
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"成功加载BIA数据: {len(df)}行，{len(df.columns)}列")
            
            return df
            
        except Exception as e:
            logger.error(f"加载BIA数据失败: {e}")
            return None
    
    def calculate_patient_bia_analysis(self, patient_data: pd.Series) -> Dict[str, Any]:
        """计算单个患者的BIA分析结果"""
        try:
            analysis = {
                "patient_id": patient_data.get("病案号", "unknown"),
                "basic_info": self._extract_basic_info(patient_data),
                "nutritional_indicators": self._calculate_nutritional_indicators(patient_data),
                "body_composition": self._analyze_body_composition(patient_data),
                "muscle_assessment": self._assess_muscle_status(patient_data),
                "hydration_status": self._analyze_hydration(patient_data),
                "risk_assessment": self._assess_nutritional_risk(patient_data),
                "clinical_recommendations": []
            }
            
            # 生成临床建议
            analysis["clinical_recommendations"] = self._generate_recommendations(analysis)
            
            logger.info(f"完成患者 {analysis['patient_id']} 的BIA分析")
            return analysis
            
        except Exception as e:
            logger.error(f"BIA分析计算失败: {e}")
            return {"error": str(e)}
    
    def _extract_basic_info(self, data: pd.Series) -> Dict[str, Any]:
        """提取基本信息"""
        return {
            "姓名": data.get("姓名", ""),
            "性别": data.get("性别", ""),
            "年龄": data.get("年龄", 0),
            "身高": data.get("身高(cm)", 0),
            "体重": data.get("体重(kg)", 0),
            "BMI": data.get("身体质量指数(kg/m²)", 0),
            "诊断": data.get("诊断", ""),
            "NRS2002": data.get("nrs2002", 0),
            "GLIM": data.get("glim", "")
        }
    
    def _calculate_nutritional_indicators(self, data: pd.Series) -> Dict[str, Any]:
        """计算营养指标"""
        phase_angle = data.get("全身相位角(°)", 0)
        age = data.get("年龄", 0)
        gender = data.get("性别", "")
        
        # 相位角评估
        phase_angle_status = self._assess_phase_angle(phase_angle, age, gender)
        
        # BMI评估
        bmi = data.get("身体质量指数(kg/m²)", 0)
        bmi_status = self._assess_bmi(bmi)
        
        return {
            "相位角": {
                "值": phase_angle,
                "状态": phase_angle_status,
                "临床意义": self._get_phase_angle_meaning(phase_angle_status)
            },
            "BMI": {
                "值": bmi,
                "状态": bmi_status,
                "分类": self._get_bmi_classification(bmi)
            },
            "基础代谢": {
                "值": data.get("基础代谢(kcal)", 0),
                "评估": "需结合年龄性别评估"
            }
        }
    
    def _analyze_body_composition(self, data: pd.Series) -> Dict[str, Any]:
        """分析身体成分"""
        return {
            "体脂": {
                "百分比": data.get("体脂百分比（%)", 0),
                "正常范围": f"{data.get('体脂百分比正常下限(%)', 0)}-{data.get('体脂百分比正常上限(%)', 0)}%",
                "绝对量": data.get("体脂肪(kg)", 0),
                "状态": self._assess_body_fat(data)
            },
            "去脂体重": {
                "值": data.get("去脂体重(kg)", 0),
                "正常范围": f"{data.get('去脂体重正常下限(kg)', 0)}-{data.get('去脂体重正常上限(kg)', 0)}kg",
                "状态": self._assess_lean_mass(data)
            },
            "内脏脂肪": {
                "面积": data.get("内脏脂肪面积(cm²)", 0),
                "评估": self._assess_visceral_fat(data.get("内脏脂肪面积(cm²)", 0))
            },
            "腰臀比": {
                "值": data.get("腰臀比", 0),
                "正常范围": f"{data.get('腰臀比正常下限', 0)}-{data.get('腰臀比正常上限', 0)}",
                "状态": self._assess_waist_hip_ratio(data)
            }
        }
    
    def _assess_muscle_status(self, data: pd.Series) -> Dict[str, Any]:
        """评估肌肉状况"""
        asmi = data.get("四肢骨骼肌质量指数(kg/m²)", 0)
        gender = data.get("性别", "")
        
        # ASMI评估（肌少症诊断的重要指标）
        asmi_status = self._assess_asmi(asmi, gender)
        
        return {
            "ASMI": {
                "值": asmi,
                "状态": asmi_status,
                "临床意义": "四肢骨骼肌质量指数，用于肌少症诊断"
            },
            "骨骼肌": {
                "总量": data.get("骨骼肌(kg)", 0),
                "正常范围": f"{data.get('骨骼肌正常下限(kg)', 0)}-{data.get('骨骼肌正常上限(kg)', 0)}kg",
                "状态": self._assess_skeletal_muscle(data)
            },
            "肌肉分布": {
                "总肌肉量": data.get("肌肉量(kg)", 0),
                "右上肢": data.get("右上肢肌肉量(kg)", 0),
                "左上肢": data.get("左上肢肌肉量(kg)", 0),
                "躯干": data.get("躯干肌肉量(kg)", 0),
                "右下肢": data.get("右下肢肌肉量(kg)", 0),
                "左下肢": data.get("左下肢肌肉量(kg)", 0)
            },
            "肌肉功能": {
                "上臂围度": data.get("上臂围度(cm)", 0),
                "上臂肌肉围度": data.get("上臂肌肉围度(cm)", 0)
            }
        }
    
    def _analyze_hydration(self, data: pd.Series) -> Dict[str, Any]:
        """分析水分状况"""
        return {
            "总水分": {
                "值": data.get("总水分(L)", 0),
                "正常范围": f"{data.get('总水分正常下限(L)', 0)}-{data.get('总水分正常上限(L)', 0)}L",
                "状态": self._assess_total_water(data)
            },
            "细胞内水分": {
                "值": data.get("细胞内水分(L)", 0),
                "正常范围": f"{data.get('细胞内水分正常下限(L)', 0)}-{data.get('细胞内水分正常上限(L)', 0)}L"
            },
            "细胞外水分": {
                "值": data.get("细胞外水分(L)", 0),
                "正常范围": f"{data.get('细胞外水分正常下限(L)', 0)}-{data.get('细胞外水分正常上限(L)', 0)}L"
            },
            "浮肿评估": {
                "浮肿指数": data.get("浮肿指数", 0),
                "状态": self._assess_edema(data.get("浮肿指数", 0))
            }
        }
    
    def _assess_nutritional_risk(self, data: pd.Series) -> Dict[str, Any]:
        """评估营养风险"""
        nrs2002 = data.get("nrs2002", 0)
        glim = data.get("glim", "")
        phase_angle = data.get("全身相位角(°)", 0)
        asmi = data.get("四肢骨骼肌质量指数(kg/m²)", 0)
        gender = data.get("性别", "")
        
        # 综合风险评估
        risk_factors = []
        
        if nrs2002 >= 3:
            risk_factors.append("NRS2002评分≥3分，存在营养风险")
        
        if glim and "营养不良" in str(glim):
            risk_factors.append("GLIM诊断为营养不良")
        
        if self._assess_phase_angle(phase_angle, data.get("年龄", 0), gender) in ["低", "极低"]:
            risk_factors.append("相位角偏低，细胞膜完整性受损")
        
        if self._assess_asmi(asmi, gender) in ["肌少症", "肌少症前期"]:
            risk_factors.append("存在肌少症或肌少症前期")
        
        # 风险等级
        if len(risk_factors) >= 3:
            risk_level = "高风险"
        elif len(risk_factors) >= 2:
            risk_level = "中风险"
        elif len(risk_factors) >= 1:
            risk_level = "低风险"
        else:
            risk_level = "无明显风险"
        
        return {
            "风险等级": risk_level,
            "风险因素": risk_factors,
            "NRS2002评分": nrs2002,
            "GLIM诊断": glim,
            "健康评估分数": data.get("健康评估(分)", 0)
        }
    
    def _assess_phase_angle(self, phase_angle: float, age: int, gender: str) -> str:
        """评估相位角"""
        try:
            gender_key = "男性" if gender == "男" else "女性"
            age_group = "18-39" if age < 40 else "40-59" if age < 60 else "60+"
            
            ranges = self.reference_ranges["phase_angle"][gender_key][age_group]
            
            if ranges["normal"][0] <= phase_angle <= ranges["normal"][1]:
                return "正常"
            elif ranges["low"][0] <= phase_angle < ranges["low"][1]:
                return "低"
            elif phase_angle < ranges["very_low"][1]:
                return "极低"
            else:
                return "高"
        except:
            return "无法评估"
    
    def _assess_asmi(self, asmi: float, gender: str) -> str:
        """评估ASMI（四肢骨骼肌质量指数）"""
        try:
            gender_key = "男性" if gender == "男" else "女性"
            thresholds = self.reference_ranges["asmi"][gender_key]
            
            if asmi >= thresholds["normal"]:
                return "正常"
            elif asmi >= thresholds["low"]:
                return "肌少症前期"
            else:
                return "肌少症"
        except:
            return "无法评估"
    
    def _assess_bmi(self, bmi: float) -> str:
        """评估BMI"""
        if bmi < 18.5:
            return "体重不足"
        elif bmi < 24:
            return "正常"
        elif bmi < 28:
            return "超重"
        else:
            return "肥胖"
    
    def _get_bmi_classification(self, bmi: float) -> str:
        """获取BMI分类"""
        if bmi < 16:
            return "重度消瘦"
        elif bmi < 17:
            return "中度消瘦"
        elif bmi < 18.5:
            return "轻度消瘦"
        elif bmi < 24:
            return "正常范围"
        elif bmi < 28:
            return "超重"
        elif bmi < 30:
            return "I度肥胖"
        elif bmi < 35:
            return "II度肥胖"
        else:
            return "III度肥胖"
    
    def _assess_body_fat(self, data: pd.Series) -> str:
        """评估体脂状况"""
        fat_pct = data.get("体脂百分比（%)", 0)
        normal_min = data.get("体脂百分比正常下限(%)", 0)
        normal_max = data.get("体脂百分比正常上限(%)", 0)
        
        if normal_min <= fat_pct <= normal_max:
            return "正常"
        elif fat_pct < normal_min:
            return "偏低"
        else:
            return "偏高"
    
    def _assess_lean_mass(self, data: pd.Series) -> str:
        """评估去脂体重"""
        lean_mass = data.get("去脂体重(kg)", 0)
        normal_min = data.get("去脂体重正常下限(kg)", 0)
        normal_max = data.get("去脂体重正常上限(kg)", 0)
        
        if normal_min <= lean_mass <= normal_max:
            return "正常"
        elif lean_mass < normal_min:
            return "偏低"
        else:
            return "偏高"
    
    def _assess_skeletal_muscle(self, data: pd.Series) -> str:
        """评估骨骼肌状况"""
        muscle = data.get("骨骼肌(kg)", 0)
        normal_min = data.get("骨骼肌正常下限(kg)", 0)
        normal_max = data.get("骨骼肌正常上限(kg)", 0)
        
        if normal_min <= muscle <= normal_max:
            return "正常"
        elif muscle < normal_min:
            return "偏低"
        else:
            return "偏高"
    
    def _assess_total_water(self, data: pd.Series) -> str:
        """评估总水分状况"""
        water = data.get("总水分(L)", 0)
        normal_min = data.get("总水分正常下限(L)", 0)
        normal_max = data.get("总水分正常上限(L)", 0)
        
        if normal_min <= water <= normal_max:
            return "正常"
        elif water < normal_min:
            return "偏低"
        else:
            return "偏高"
    
    def _assess_visceral_fat(self, visceral_fat: float) -> str:
        """评估内脏脂肪"""
        if visceral_fat < 30:
            return "正常"
        elif visceral_fat < 50:
            return "轻度偏高"
        elif visceral_fat < 100:
            return "中度偏高"
        else:
            return "重度偏高"
    
    def _assess_waist_hip_ratio(self, data: pd.Series) -> str:
        """评估腰臀比"""
        whr = data.get("腰臀比", 0)
        normal_min = data.get("腰臀比正常下限", 0)
        normal_max = data.get("腰臀比正常上限", 0)
        
        if normal_min <= whr <= normal_max:
            return "正常"
        elif whr < normal_min:
            return "偏低"
        else:
            return "偏高"
    
    def _assess_edema(self, edema_index: float) -> str:
        """评估浮肿状况"""
        if edema_index <= 0.380:
            return "正常"
        elif edema_index <= 0.390:
            return "轻度浮肿"
        elif edema_index <= 0.400:
            return "中度浮肿"
        else:
            return "重度浮肿"
    
    def _get_phase_angle_meaning(self, status: str) -> str:
        """获取相位角临床意义"""
        meanings = {
            "正常": "细胞膜完整性良好，营养状况佳",
            "低": "细胞膜完整性下降，可能存在营养不良",
            "极低": "细胞膜严重受损，营养状况差，需要紧急干预",
            "高": "可能存在脱水或炎症状态"
        }
        return meanings.get(status, "需要进一步评估")
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成临床建议 - 仅用于内部分析，不输出给用户"""
        return []  # 不生成建议，只返回计算结果

    def run_bia_calculation(self, excel_path: str = "data/samples/BIA数据/用户相关数据.xlsx") -> Dict[str, Any]:
        """运行BIA计算并返回基础数据结果"""
        try:
            # 加载数据
            df = self.load_bia_data(excel_path)
            if df is None or df.empty:
                logger.warning("使用默认测试数据")
                # 使用默认测试数据
                test_data = pd.Series({
                    "病案号": "TEST001",
                    "姓名": "张某某",
                    "性别": "男",
                    "年龄": 65,
                    "身高(cm)": 170,
                    "体重(kg)": 58,
                    "身体质量指数(kg/m²)": 20.1,
                    "全身相位角(°)": 4.9,
                    "四肢骨骼肌质量指数(kg/m²)": 6.3,
                    "体脂百分比（%)": 18.2,
                    "体脂肪(kg)": 10.6,
                    "去脂体重(kg)": 47.4,
                    "骨骼肌(kg)": 24.3,
                    "总水分(L)": 34.7,
                    "细胞内水分(L)": 21.5,
                    "细胞外水分(L)": 13.2,
                    "内脏脂肪面积(cm²)": 45,
                    "腰臀比": 0.85,
                    "浮肿指数": 0.375,
                    "基础代谢(kcal)": 1450
                })
                results = self._extract_bia_data(test_data)
            else:
                # 使用第一行数据
                patient_data = df.iloc[0]
                results = self._extract_bia_data(patient_data)

            return results

        except Exception as e:
            logger.error(f"BIA计算失败: {e}")
            return {"error": str(e)}

    def _extract_bia_data(self, data: pd.Series) -> Dict[str, Any]:
        """提取BIA基础数据"""
        return {
            "patient_info": {
                "patient_id": data.get("病案号", ""),
                "name": data.get("姓名", ""),
                "gender": data.get("性别", ""),
                "age": data.get("年龄", 0),
                "height": data.get("身高(cm)", 0),
                "weight": data.get("体重(kg)", 0)
            },
            "bia_measurements": {
                "bmi": data.get("身体质量指数(kg/m²)", 0),
                "phase_angle": data.get("全身相位角(°)", 0),
                "asmi": data.get("四肢骨骼肌质量指数(kg/m²)", 0),
                "body_fat_percentage": data.get("体脂百分比（%)", 0),
                "fat_mass": data.get("体脂肪(kg)", 0),
                "fat_free_mass": data.get("去脂体重(kg)", 0),
                "skeletal_muscle_mass": data.get("骨骼肌(kg)", 0),
                "total_body_water": data.get("总水分(L)", 0),
                "intracellular_water": data.get("细胞内水分(L)", 0),
                "extracellular_water": data.get("细胞外水分(L)", 0),
                "visceral_fat_area": data.get("内脏脂肪面积(cm²)", 0),
                "waist_hip_ratio": data.get("腰臀比", 0),
                "edema_index": data.get("浮肿指数", 0),
                "basal_metabolic_rate": data.get("基础代谢(kcal)", 0)
            },
            "calculation_timestamp": pd.Timestamp.now().isoformat()
        }

    def print_bia_results(self, analysis: Dict[str, Any]):
        """打印BIA分析结果"""
        if "error" in analysis:
            print(f"BIA分析失败: {analysis['error']}")
            return

        print("\n" + "="*60)
        print("BIA身体成分分析结果")
        print("="*60)

        # 基本信息
        basic = analysis["basic_info"]
        print(f"\n患者基本信息:")
        print(f"病案号: {analysis['patient_id']}")
        print(f"姓名: {basic['姓名']}")
        print(f"性别: {basic['性别']}")
        print(f"年龄: {basic['年龄']}岁")
        print(f"身高: {basic['身高']}cm")
        print(f"体重: {basic['体重']}kg")
        print(f"BMI: {basic['BMI']} kg/m²")

        # 营养指标
        nutrition = analysis["nutritional_indicators"]
        print(f"\n营养状态指标:")
        print(f"相位角: {nutrition['相位角']['值']}° ({nutrition['相位角']['状态']})")
        print(f"BMI状态: {nutrition['BMI']['状态']} ({nutrition['BMI']['分类']})")
        print(f"基础代谢: {nutrition['基础代谢']['值']} kcal")

        # 身体成分
        composition = analysis["body_composition"]
        print(f"\n身体成分分析:")
        print(f"体脂率: {composition['体脂']['百分比']}% ({composition['体脂']['状态']})")
        print(f"体脂量: {composition['体脂']['绝对量']} kg")
        print(f"去脂体重: {composition['去脂体重']['值']} kg ({composition['去脂体重']['状态']})")
        print(f"内脏脂肪面积: {composition['内脏脂肪']['面积']} cm² ({composition['内脏脂肪']['评估']})")
        print(f"腰臀比: {composition['腰臀比']['值']} ({composition['腰臀比']['状态']})")

        # 肌肉评估
        muscle = analysis["muscle_assessment"]
        print(f"\n肌肉质量评估:")
        print(f"ASMI: {muscle['ASMI']['值']} kg/m² ({muscle['ASMI']['状态']})")
        print(f"骨骼肌总量: {muscle['骨骼肌']['总量']} kg ({muscle['骨骼肌']['状态']})")
        print(f"总肌肉量: {muscle['肌肉分布']['总肌肉量']} kg")

        # 水分状况
        hydration = analysis["hydration_status"]
        print(f"\n水分状况分析:")
        print(f"总水分: {hydration['总水分']['值']} L ({hydration['总水分']['状态']})")
        print(f"细胞内水分: {hydration['细胞内水分']['值']} L")
        print(f"细胞外水分: {hydration['细胞外水分']['值']} L")
        print(f"浮肿评估: {hydration['浮肿评估']['状态']}")

        # 风险评估
        risk = analysis["risk_assessment"]
        print(f"\n营养风险评估:")
        print(f"风险等级: {risk['风险等级']}")
        print(f"NRS2002评分: {risk['NRS2002评分']}")
        print(f"GLIM诊断: {risk['GLIM诊断']}")
        if risk['风险因素']:
            print("风险因素:")
            for factor in risk['风险因素']:
                print(f"  - {factor}")

        print("\n" + "="*60)

