#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话式营养筛查智能体 - LangGraph + AI主脑整合版
华佗GPT通过系统提示词控制整个对话流程，LangGraph提供状态管理
"""
import asyncio
import json
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime
from loguru import logger

from langgraph.graph import StateGraph, END

from src.core.lm_studio_client import LMStudioClient
from src.tools.glim_form_processor import GLIMFormProcessor
from src.tools.bia_calculator import BIACalculator
from src.agents.tool_calling_agent_new import get_tool_calling_agent
from config.settings import settings


class AgentState(TypedDict):
    """智能体状态管理"""
    # 用户基本信息
    user_profile: Dict[str, Any]
    
    # 对话历史
    messages: List[Dict[str, str]]
    
    # 已收集的数据
    collected_data: Dict[str, Any]
    
    # 当前阶段
    current_phase: str
    
    # 等待的用户输入类型
    waiting_for: Optional[str]
    
    # 最终分析结果
    final_report: Optional[str]
    
    # 前端动作指令
    frontend_actions: List[Dict[str, Any]]
    
    # AI主脑的内部状态
    brain_context: Dict[str, Any]
    
    # === 自动模式新增字段 ===
    # 上传文件队列
    uploads_queue: List[Dict[str, Any]]
    
    # 已处理的文件列表
    processed_files: List[Dict[str, Any]]
    
    # 模块状态跟踪
    module_status: Dict[str, str]
    
    # 错误信息列表
    errors: List[Dict[str, Any]]


class ConversationAgent:
    """对话式营养筛查智能体"""
    
    def __init__(self):
        """初始化智能体"""
        self.lm_client = LMStudioClient()
        self.glim_processor = GLIMFormProcessor()
        self.bia_calculator = BIACalculator()
        
        # AI主脑系统提示词 - 简化版本，适配华佗GPT思考模型
        self.brain_system_prompt = """你是一位专业的临床营养学专家AI助手，名为"智能营养顾问"。

你的职责是通过友好对话，引导用户完成营养状况评估。

## 工作流程
1. **问候阶段**：欢迎用户，介绍服务，要求填写基本信息
2. **数据收集**：引导用户选择评估方式：GLIM问卷、面部照片、BIA数据
3. **分析阶段**：综合分析收集到的数据
4. **报告阶段**：生成营养评估报告和建议

## 当前状态
- 用户档案：{user_profile}
- 已收集数据：{collected_data}
- 当前阶段：{current_phase}
- 用户输入：{user_input}

## 重要指导原则
当用户选择具体的评估方式时（如"GLIM评估问卷"、"面部照片分析"、"BIA数据分析"），你应该：
1. 确认用户的选择
2. 简要说明该评估方式的作用
3. 引导用户开始填写/上传相应的数据
4. 使用友好的语言，如"好的，我们来填写GLIM评估问卷"

## 请求
根据当前状态，生成合适的回复。如果是初次启动，请发出友好的问候并引导用户填写基本信息。
如果用户已经做出选择，请确认选择并引导下一步操作。

保持专业、温暖、易懂的交流风格。始终强调这是AI辅助初步筛查，不能替代医生诊断。"""

        # 构建LangGraph工作流
        self.workflow = self._build_workflow()
    
    def _format_visual_analysis_result(self, raw_analysis: str) -> str:
        """
        将JSON格式的视觉分析结果转换为用户友好的报告格式
        """
        try:
            # 尝试解析JSON，处理重复JSON块的问题
            if isinstance(raw_analysis, str):
                # 如果是字符串，尝试解析JSON
                if '{' in raw_analysis and '}' in raw_analysis:
                    # 改进的JSON提取逻辑：只提取第一个完整的JSON块
                    json_blocks = []
                    start_pos = 0
                    brace_count = 0
                    json_start = -1
                    
                    # 限制处理长度，避免超长数据导致卡顿
                    max_length = min(len(raw_analysis), 50000)  # 最多处理50000字符
                    processing_data = raw_analysis[:max_length]
                    
                    if max_length < len(raw_analysis):
                        logger.warning(f"⚠️ 视觉分析数据过长({len(raw_analysis)}字符)，截取前{max_length}字符进行处理")
                    
                    # 查找第一个完整的JSON块
                    for i, char in enumerate(processing_data):
                        if char == '{':
                            if brace_count == 0:
                                json_start = i
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0 and json_start != -1:
                                # 找到一个完整的JSON块
                                json_str = processing_data[json_start:i+1]
                                try:
                                    parsed_json = json.loads(json_str)
                                    json_blocks.append(parsed_json)
                                    logger.info(f"✅ 成功解析第一个JSON块，大小: {len(json_str)}字符")
                                    break  # 只提取第一个有效的JSON块
                                except json.JSONDecodeError as e:
                                    logger.debug(f"JSON解析失败: {e}, 继续查找下一个块")
                                    # 如果解析失败，继续查找下一个可能的JSON块
                                    json_start = -1
                                    continue
                    
                    # 使用第一个有效的JSON块
                    if json_blocks:
                        data = json_blocks[0]
                        logger.info("✅ 成功解析视觉分析JSON结果")
                    else:
                        # 如果没有找到有效JSON，返回简化的原始内容摘要
                        logger.warning("⚠️ 未找到有效的JSON块，返回原始分析摘要")
                        return f"## 面部视觉分析结果\n\n原始分析数据过长或格式异常，无法正常解析。\n\n数据长度: {len(raw_analysis)} 字符\n\n请联系技术支持检查分析结果。"
                else:
                    # 如果不是JSON格式，直接返回原文
                    logger.warning("⚠️ 视觉分析结果不包含JSON格式内容")
                    return raw_analysis
            else:
                # 如果已经是字典，直接使用
                data = raw_analysis
            
            # 格式化为用户友好的报告
            formatted_report = "## 🔍 面部视觉分析报告\n\n"
            
            # 处理visual_analysis部分
            if 'visual_analysis' in data:
                for region_analysis in data['visual_analysis']:
                    region = region_analysis.get('facial_region', '未知区域')
                    formatted_report += f"### {region} 分析\n"
                    
                    # 处理findings
                    if 'findings' in region_analysis:
                        findings = region_analysis['findings']
                        for finding_key, finding_data in findings.items():
                            if isinstance(finding_data, dict):
                                present = finding_data.get('present', False)
                                severity = finding_data.get('severity', 'unknown')
                                description = finding_data.get('description', '无描述')
                                confidence = finding_data.get('confidence', 0.0)
                                
                                status = "✅ 存在" if present else "❌ 未发现"
                                severity_emoji = {
                                    'normal': '🟢',
                                    'mild': '🟡', 
                                    'moderate': '🟠',
                                    'severe': '🔴'
                                }.get(severity, '⚪')
                                
                                formatted_report += f"- **{finding_key.replace('_', ' ').title()}**: {status} {severity_emoji}\n"
                                formatted_report += f"  - 严重程度: {severity}\n"
                                formatted_report += f"  - 置信度: {confidence:.1%}\n"
                                formatted_report += f"  - 描述: {description}\n\n"
                    
                    # 添加区域总结
                    if 'region_summary' in region_analysis:
                        formatted_report += f"**区域总结**: {region_analysis['region_summary']}\n"
                    
                    if 'region_confidence' in region_analysis:
                        formatted_report += f"**区域置信度**: {region_analysis['region_confidence']:.1%}\n\n"
            
            # 处理整体评估
            if 'overall_assessment' in data:
                assessment = data['overall_assessment']
                formatted_report += "### 🎯 整体评估\n"
                
                likelihood = assessment.get('malnutrition_likelihood', '未知')
                confidence = assessment.get('confidence', 0.0)
                
                likelihood_emoji = {
                    'normal': '🟢 正常',
                    'mild': '🟡 轻度', 
                    'moderate': '🟠 中度',
                    'severe': '🔴 重度'
                }.get(likelihood, f'⚪ {likelihood}')
                
                formatted_report += f"- **营养不良可能性**: {likelihood_emoji}\n"
                formatted_report += f"- **整体置信度**: {confidence:.1%}\n\n"
                
                # 关键发现
                if 'key_findings' in assessment:
                    formatted_report += "#### 🔎 关键发现\n"
                    for finding in assessment['key_findings']:
                        formatted_report += f"- {finding}\n"
                    formatted_report += "\n"
                
                # 临床注意事项
                if 'clinical_notes' in assessment:
                    formatted_report += f"#### 📝 临床建议\n{assessment['clinical_notes']}\n\n"
                
                # 图像质量评估
                if 'image_quality_assessment' in assessment:
                    quality = assessment['image_quality_assessment']
                    quality_emoji = {
                        'excellent': '🌟 优秀',
                        'good': '✅ 良好',
                        'fair': '⚠️ 一般',
                        'poor': '❌ 较差'
                    }.get(quality, f'📊 {quality}')
                    formatted_report += f"#### 📸 图像质量: {quality_emoji}\n\n"
                
                # 面部整体视觉描述
                if 'facial_overall_description' in assessment:
                    formatted_report += f"#### 👤 面部整体印象\n{assessment['facial_overall_description']}\n\n"
            
            # 处理专家元数据
            if 'expert_metadata' in data:
                metadata = data['expert_metadata']
                formatted_report += "### 📋 分析详情\n"
                
                if 'analysis_approach' in metadata:
                    formatted_report += f"- **分析方法**: {metadata['analysis_approach']}\n"
                
                if 'primary_indicators_used' in metadata:
                    formatted_report += f"- **主要指标**: {', '.join(metadata['primary_indicators_used'])}\n"
                
                if 'limitations' in metadata:
                    limitations = metadata['limitations']
                    if isinstance(limitations, list):
                        formatted_report += "- **分析局限性**:\n"
                        for limitation in limitations:
                            formatted_report += f"  - {limitation}\n"
                    else:
                        formatted_report += f"- **分析局限性**: {limitations}\n"
            
            return formatted_report
            
        except Exception as e:
            logger.warning(f"视觉分析结果格式化失败: {e}")
            # 如果格式化失败，返回简化的分析结果
            if isinstance(raw_analysis, str):
                # 尝试提取有用的信息
                simplified_result = "## 🔍 面部视觉分析结果\n\n"
                simplified_result += "⚠️ 分析结果格式化过程中出现问题，但已保存原始分析数据。\n\n"
                simplified_result += "### 原始分析内容\n"
                # 只显示前500个字符，避免内容过长
                if len(raw_analysis) > 500:
                    simplified_result += raw_analysis[:500] + "...\n\n"
                    simplified_result += f"*(完整分析内容已保存，总长度：{len(raw_analysis)}字符)*"
                else:
                    simplified_result += raw_analysis
                simplified_result += "\n\n💡 建议：请联系技术支持查看完整分析结果。"
                return simplified_result
            else:
                return f"## 🔍 面部视觉分析结果\n\n⚠️ 分析结果处理失败：{str(e)}"
    
    def _build_workflow(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(AgentState)
        
        # 核心节点：AI主脑决策
        workflow.add_node("brain_decision", self.brain_decision_node)
        
        # 传统手动模式节点
        workflow.add_node("call_vision_analysis", self.call_vision_analysis_node) 
        workflow.add_node("call_bia_analysis", self.call_bia_analysis_node)
        workflow.add_node("call_comprehensive_analysis", self.call_comprehensive_analysis_node)
        workflow.add_node("finalize_conversation", self.finalize_conversation_node)
        
        # 自动模式节点（新增）
        workflow.add_node("auto_ingestion", self.auto_ingest_node)
        workflow.add_node("auto_serial_analysis", self.auto_serial_analysis_node)
        
        # 🤖 真正的工具调用智能体节点（核心创新）
        workflow.add_node("tool_calling_agent", self.tool_calling_agent_node)
        
        # 设置入口点
        workflow.set_entry_point("brain_decision")
        
        # 添加条件边 - 由AI主脑决策决定
        workflow.add_conditional_edges(
            "brain_decision",
            self._route_brain_decision,
            {
                # 传统手动模式路由
                "continue": END,
                "vision_analysis": "call_vision_analysis",
                "bia_analysis": "call_bia_analysis", 
                "comprehensive_analysis": "call_comprehensive_analysis",
                "end": END,
                
                # 自动模式路由（新增）
                "auto_ingestion": "auto_ingestion",
                "auto_serial_analysis": "auto_serial_analysis",  # 串行分析节点
                
                # 🤖 真正的工具调用智能体路由（核心创新）
                "tool_calling_agent": "tool_calling_agent"
            }
        )
        
        # 传统分析节点直接结束
        workflow.add_edge("call_vision_analysis", END)
        workflow.add_edge("call_bia_analysis", END)
        workflow.add_edge("call_comprehensive_analysis", END)
        workflow.add_edge("finalize_conversation", END)
        
        # 自动模式节点路由（新增）
        workflow.add_edge("auto_ingestion", "brain_decision")  # 数据摄取后回到决策
        workflow.add_edge("auto_serial_analysis", "brain_decision")  # 串行分析后回到决策
        
        # 🤖 工具调用智能体直接结束（因为它内部已经处理完整的对话）
        workflow.add_edge("tool_calling_agent", END)
        
        return workflow.compile()
    
    def create_initial_state(self, session_id: str = None) -> AgentState:
        """创建初始状态"""
        # 会话开始时清理所有已加载的模型
        cleanup_result = self.lm_client.cleanup_session_models()

        if cleanup_result['success']:
            if cleanup_result.get('models_unloaded'):
                logger.info(f"🧹 会话 {session_id} 开始 - 已清理模型: {cleanup_result['models_unloaded']}")
            else:
                logger.info(f"🧹 会话 {session_id} 开始 - 模型状态干净")
        else:
            logger.warning(f"⚠️ 会话 {session_id} 开始 - 模型清理失败: {cleanup_result['error']}")

        return AgentState(
            user_profile={},
            messages=[],
            collected_data={
                "glim_results": None,
                "facial_analysis": None,
                "bia_analysis": None
            },
            current_phase="greeting",
            waiting_for=None,
            final_report=None,
            frontend_actions=[],
            brain_context={
                "session_id": session_id or f"session_{datetime.now().timestamp()}",
                "start_time": datetime.now().isoformat(),
                "interaction_count": 0,
                "model_cleanup": cleanup_result  # 记录清理结果
            },
            
            # === 自动模式字段初始化 ===
            uploads_queue=[],
            processed_files=[],
            module_status={},
            errors=[]
        )
    
    async def brain_decision_node(self, state: AgentState) -> AgentState:
        """简化的决策节点 - 数据收集阶段使用简单状态机"""
        logger.info(f"处理对话状态，阶段: {state['current_phase']}")

        # 数据收集阶段使用简化逻辑
        if state["current_phase"] in ["greeting", "data_collection"]:
            return self._simple_data_collection_logic(state)

        # 后续对话阶段处理用户问题
        elif state["current_phase"] == "follow_up_conversation":
            return await self._handle_follow_up_conversation(state)

        # 其他阶段（如综合分析）使用AI主脑
        else:
            return await self._ai_brain_logic(state)

    def _simple_data_collection_logic(self, state: AgentState) -> AgentState:
        """简化的数据收集逻辑 - 不调用AI主脑"""

        # 如果是首次启动且没有用户档案，显示问候
        if state["current_phase"] == "greeting" and not state["user_profile"]:
            logger.info("首次启动，显示问候和档案表单")

            greeting_message = """🌟 您好！我是您的专业营养顾问AI助手。

很高兴为您提供营养状况的初步筛查服务。我将通过友好的对话，引导您提供一些基本信息和可选的检测数据，然后为您生成个性化的营养评估报告。

⚠️ 重要提醒：我提供的是基于AI的初步筛查建议，不能替代专业医生的诊断。建议您将评估结果作为参考，并咨询专业的营养科医生。

首先，让我了解一下您的基本信息。"""

            state["messages"].append({
                "role": "assistant",
                "content": greeting_message,
                "timestamp": datetime.now().isoformat()
            })
            state["current_phase"] = "greeting"
            state["waiting_for"] = "profile"
            state["frontend_actions"] = [{"type": "show_profile_form"}]
            return state

        # 获取用户输入
        user_input = state.get("messages", [])[-1].get("content", "") if state.get("messages") else ""
        user_input_lower = user_input.lower()

        logger.info(f"简化逻辑：处理用户输入 '{user_input}'")

        # 用户档案提交后，显示数据收集选项
        if (state["user_profile"] and not any(state["collected_data"].values()) and
            (not user_input or "基本信息已提交" in user_input)):
            logger.info("用户档案已提交，显示数据收集选项")

            user_name = state["user_profile"].get("name", "")
            greeting = f"{user_name}，" if user_name else ""

            data_options_message = f"""好的，{greeting}您的基本信息已经收到了。

为了更准确地评估您的营养状况，我可以为您提供以下几种评估方式：

1. **GLIM评估问卷** - 基于国际营养不良诊断标准的专业问卷
2. **面部照片分析** - 通过AI视觉分析评估面部营养特征
3. **BIA数据分析** - 分析您的体成分数据

您可以选择其中任意一种或多种方式，也可以跳过直接获得基于基本信息的初步评估。请问您希望从哪一项开始？"""

            state["messages"].append({
                "role": "assistant",
                "content": data_options_message,
                "timestamp": datetime.now().isoformat()
            })
            state["current_phase"] = "data_collection"
            state["waiting_for"] = "user_choice"
            state["frontend_actions"] = [{"type": "none"}]
            return state

        # 处理用户选择 - 简单字符串匹配
        if user_input:
            # 优先处理完成消息（避免与选择逻辑冲突）
            if "已完成" in user_input and ("glim" in user_input_lower or "GLIM" in user_input):
                logger.info(f"用户完成GLIM评估: {user_input}")
                return self._handle_data_completion(state, "glim")

            elif "已上传BIA数据" in user_input or "📊" in user_input:
                logger.info(f"用户完成BIA数据上传: {user_input}")
                # 设置标志，让工作流处理BIA分析，避免重复消息
                state["current_phase"] = "bia_processing"
                state["waiting_for"] = None
                return state

            elif "已上传面部照片" in user_input or "📷" in user_input:
                logger.info(f"用户完成面部照片上传: {user_input}")
                return self._handle_data_completion(state, "photo")

            # 完成收集
            elif "完成收集" in user_input:
                logger.info("用户选择完成收集，准备综合分析")

                # 检查收集到的数据
                collected_data = state.get("collected_data", {})
                data_summary = []
                if collected_data.get("glim_results"):
                    data_summary.append("GLIM评估问卷")
                if collected_data.get("facial_analysis"):
                    data_summary.append("面部照片分析")
                if collected_data.get("bia_analysis"):
                    data_summary.append("BIA体成分数据")

                if not data_summary:
                    # 没有收集到任何数据
                    state["messages"].append({
                        "role": "assistant",
                        "content": "抱歉，我发现还没有收集到任何评估数据。请至少完成一项评估后再进行综合分析。",
                        "timestamp": datetime.now().isoformat()
                    })
                    return state

                # 开始综合分析
                state["current_phase"] = "comprehensive_analysis"
                state["waiting_for"] = "ai_analysis"

                # 添加分析开始消息
                state["messages"].append({
                    "role": "assistant",
                    "content": f"好的，我已收集到以下数据：{', '.join(data_summary)}。\n\n正在进行综合分析，请稍候...",
                    "timestamp": datetime.now().isoformat()
                })

                # 设置前端显示加载状态
                state["frontend_actions"] = [{"type": "show_analysis_loading"}]

                return state

            # 然后处理新的选择请求
            elif "glim" in user_input_lower or "问卷" in user_input or "GLIM评估问卷" in user_input:
                logger.info(f"用户选择GLIM评估问卷: {user_input}")
                state["messages"].append({
                    "role": "assistant",
                    "content": "好的，我们来填写GLIM评估问卷。这是一个基于国际营养不良诊断标准的专业问卷，请您根据实际情况如实填写。",
                    "timestamp": datetime.now().isoformat()
                })
                state["current_phase"] = "data_collection"
                state["waiting_for"] = "glim_data"
                state["frontend_actions"] = [{"type": "show_glim_form"}]
                return state

            # BIA数据选择
            elif "bia" in user_input_lower or "体成分" in user_input or "BIA数据" in user_input:
                logger.info(f"用户选择BIA数据分析: {user_input}")
                state["messages"].append({
                    "role": "assistant",
                    "content": "好的，请您上传BIA体成分分析数据。我将分析您的身体成分指标。",
                    "timestamp": datetime.now().isoformat()
                })
                state["current_phase"] = "data_collection"
                state["waiting_for"] = "bia_data"
                state["frontend_actions"] = [{"type": "show_bia_upload"}]
                return state

            # 面部照片选择
            elif "面部" in user_input or "照片" in user_input or "面部照片" in user_input:
                logger.info(f"用户选择面部照片分析: {user_input}")
                state["messages"].append({
                    "role": "assistant",
                    "content": "好的，请您上传一张近期、清晰、无遮挡的正面照片。我将使用AI视觉分析技术评估面部营养特征。",
                    "timestamp": datetime.now().isoformat()
                })
                state["current_phase"] = "data_collection"
                state["waiting_for"] = "photo_data"
                state["frontend_actions"] = [{"type": "show_photo_upload"}]
                logger.info(f"🚀 设置面部照片上传前端动作: {state['frontend_actions']}")
                return state

        # 默认回复
        logger.info("未识别的用户输入，提供默认选项")
        state["messages"].append({
            "role": "assistant",
            "content": "为了更准确地评估您的营养状况，您可以选择提供GLIM评估问卷、面部照片或BIA数据。您希望从哪一项开始？",
            "timestamp": datetime.now().isoformat()
        })
        state["current_phase"] = "data_collection"
        state["waiting_for"] = "user_choice"
        state["frontend_actions"] = [{"type": "none"}]
        return state

    async def _handle_follow_up_conversation(self, state: AgentState) -> AgentState:
        """处理后续对话阶段的用户问题"""
        if not state["messages"]:
            return state

        # 获取用户最新的问题
        user_message = state["messages"][-1]["content"]
        logger.info(f"处理后续问题: {user_message}")

        # 构建基于分析报告的对话提示词
        conversation_prompt = self._build_follow_up_prompt(state, user_message)

        logger.info("=" * 60)
        logger.info("💬 后续对话提示词构建完成")
        logger.info("=" * 60)
        logger.info("📊 提示词长度: {} 字符".format(len(conversation_prompt)))
        logger.info("❓ 用户问题: {}".format(user_message))
        logger.info("=" * 60)

        try:
            # 调用华佗GPT进行对话
            result = self.lm_client.call_huatuogpt(
                prompt=conversation_prompt,
                timeout=60
            )

            if result['success']:
                # 添加AI回复
                state["messages"].append({
                    "role": "assistant",
                    "content": result['analysis'],
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "follow_up_response"
                })
                logger.info("后续对话回复完成")
            else:
                # 处理失败情况
                error_msg = f"抱歉，我在处理您的问题时遇到了困难：{result['error']}\n\n请重新表述您的问题，或者询问其他相关内容。"
                state["messages"].append({
                    "role": "assistant",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "error"
                })
                logger.error(f"后续对话失败: {result['error']}")

        except Exception as e:
            logger.error(f"后续对话异常: {e}")
            error_msg = f"处理您的问题时出现了技术问题：{str(e)}\n\n请稍后重试，或者询问其他问题。"
            state["messages"].append({
                "role": "assistant",
                "content": error_msg,
                "timestamp": datetime.now().isoformat(),
                "message_type": "error"
            })

        # 保持在后续对话阶段
        state["current_phase"] = "follow_up_conversation"
        state["waiting_for"] = "user_questions"

        return state

    def _build_follow_up_prompt(self, state: AgentState, user_question: str) -> str:
        """构建后续对话的提示词"""
        user_profile = state.get("user_profile", {})
        final_report = state.get("final_report", "")

        prompt = f"""你是一位专业的营养科医生AI助手。之前你已经为患者完成了综合营养评估，现在患者对报告有进一步的问题。

## 患者基本信息
{json.dumps(user_profile, ensure_ascii=False, indent=2)}

## 之前的分析报告
{final_report}

## 患者的问题
{user_question}

请基于之前的分析报告，专业、详细地回答患者的问题。要求：

1. **直接回答问题**：针对患者的具体问题给出明确回答
2. **引用报告内容**：适当引用之前分析报告中的相关内容
3. **提供具体建议**：给出可操作的具体建议
4. **保持专业性**：使用专业但易懂的语言
5. **鼓励就医**：适当提醒患者咨询专业医生

回答要简洁明了，重点突出，避免重复整个报告内容。"""

        return prompt

    def _clean_facial_analysis_for_comprehensive(self, facial_analysis: str) -> str:
        """清理面部分析内容，避免在综合分析时引起模块使用的混淆"""
        try:
            # 需要移除或替换的可能引起混淆的词汇和短语
            replacements = [
                # 移除对其他评估工具的直接建议
                ("建议结合其他营养评估工具（如BIA体成分分析、血液生化指标）进行综合判断", "建议结合其他客观营养指标进行综合判断"),
                ("BIA体成分分析", "体成分分析"),
                ("BIA数据", "体成分数据"),
                ("结合BIA", "结合体成分"),
                # 避免提及具体的评估工具名称
                ("配合GLIM量化评估", "配合标准化营养评估"),
                ("GLIM评估问卷", "营养评估量表"),
                # 保持通用性的表述
                ("需要进一步的BIA", "需要进一步的体成分"),
                ("推荐进行BIA", "推荐进行体成分"),
            ]
            
            cleaned_analysis = facial_analysis
            for old_phrase, new_phrase in replacements:
                cleaned_analysis = cleaned_analysis.replace(old_phrase, new_phrase)
            
            logger.debug(f"面部分析内容清理完成，避免模块混淆")
            return cleaned_analysis
            
        except Exception as e:
            logger.warning(f"面部分析内容清理失败: {e}")
            return facial_analysis

    def _handle_data_completion(self, state: AgentState, data_type: str) -> AgentState:
        """处理数据收集完成后的逻辑"""

        # 检查还有哪些评估可以进行
        collected_data = state["collected_data"]
        available_options = []

        logger.info(f"🔍 处理数据完成: data_type={data_type}")
        logger.info(f"🔍 当前collected_data: {collected_data}")
        logger.info(f"🔍 glim_results存在: {bool(collected_data.get('glim_results'))}")
        logger.info(f"🔍 bia_analysis存在: {bool(collected_data.get('bia_analysis'))}")
        logger.info(f"🔍 facial_analysis存在: {bool(collected_data.get('facial_analysis'))}")

        if not collected_data.get("glim_results") and data_type != "glim":
            available_options.append("GLIM评估问卷")
        if not collected_data.get("facial_analysis") and data_type != "photo":
            available_options.append("面部照片")
        if not collected_data.get("bia_analysis") and data_type != "bia":
            available_options.append("BIA数据")

        # 构建回复消息
        if data_type == "glim":
            success_msg = "✅ GLIM评估问卷已成功提交！您的营养状况数据已保存。"
        elif data_type == "bia":
            success_msg = "✅ BIA数据分析完成！"
        elif data_type == "photo":
            success_msg = "✅ 面部照片分析完成！"
        else:
            success_msg = "✅ 数据已成功处理！"

        if available_options:
            options_text = "、".join(available_options)
            response_message = f"{success_msg}\n\n您还可以选择以下评估方式来获得更全面的分析：\n\n{options_text}\n\n或者您可以选择'完成收集'来基于当前信息进行综合分析。"
            available_options.append("完成收集")
        else:
            response_message = f"{success_msg}\n\n您可以选择'完成收集'来进行综合分析。"
            available_options = ["完成收集"]

        state["messages"].append({
            "role": "assistant",
            "content": response_message,
            "timestamp": datetime.now().isoformat()
        })
        state["current_phase"] = "data_collection"
        state["waiting_for"] = "user_choice"
        state["frontend_actions"] = [{"type": "none"}]
        return state

    async def _ai_brain_logic(self, state: AgentState) -> AgentState:
        """真正的AI主脑逻辑 - 用于综合分析和多轮对话阶段"""
        # TODO: 实现真正的AI主脑调用逻辑
        logger.info("进入AI主脑分析阶段")
        return state
    
    def _build_context_prompt(self, state: AgentState, user_input: str = "") -> str:
        """构建给AI主脑的上下文提示"""
        user_profile = state["user_profile"]
        collected_data = state["collected_data"]
        current_phase = state["current_phase"]
        
        # 简化的用户档案显示
        profile_summary = "未填写"
        if user_profile:
            profile_summary = f"姓名:{user_profile.get('name', '未知')}, 年龄:{user_profile.get('age', '未知')}, 性别:{user_profile.get('gender', '未知')}"
        
        # 简化的已收集数据显示
        data_summary = []
        if collected_data.get("glim_results"):
            data_summary.append("GLIM问卷")
        if collected_data.get("facial_analysis"):
            data_summary.append("面部照片")
        if collected_data.get("bia_analysis"):
            data_summary.append("BIA数据")
        data_summary_text = "、".join(data_summary) if data_summary else "无"
        
        context = self.brain_system_prompt.format(
            user_profile=profile_summary,
            collected_data=data_summary_text,
            current_phase=current_phase,
            user_input=user_input or "（首次启动）"
        )
        
        return context
    
    def _parse_brain_response(self, response: str) -> Dict[str, Any]:
        """解析AI主脑的响应 - 简化版本，直接使用自然语言"""
        # 华佗GPT是思考模型，我们直接使用它的回复
        # 根据回复内容推断前端行为
        decision = {
            "action": "continue_conversation",
            "next_phase": "data_collection",
            "response": response,
            "frontend_action": {"type": "none"},
            "waiting_for": "user_choice"
        }

        response_lower = response.lower()

        # 根据关键词推断前端行为 - 更精确的匹配
        if "基本信息" in response or "个人档案" in response or ("填写" in response and "信息" in response):
            decision["frontend_action"] = {"type": "show_profile_form"}
            decision["waiting_for"] = "profile"
        elif ("glim" in response_lower and ("问卷" in response or "评估" in response)) or "来填写glim" in response_lower:
            decision["frontend_action"] = {"type": "show_glim_form"}
            decision["waiting_for"] = "glim_data"
            logger.info("检测到GLIM问卷请求，将显示GLIM表单")
        elif "照片" in response or "面部" in response:
            decision["frontend_action"] = {"type": "show_photo_upload"}
            decision["waiting_for"] = "photo"
        elif "bia" in response_lower or "体成分" in response:
            decision["frontend_action"] = {"type": "show_bia_upload"}
            decision["waiting_for"] = "bia_data"
        elif "分析" in response and ("开始" in response or "综合" in response):
            decision["action"] = "call_analysis"
            decision["next_phase"] = "analysis"
            decision["frontend_action"] = {"type": "show_analysis_loading"}
        
        return decision
    
    def _parse_structured_response(self, response: str) -> Dict[str, Any]:
        """解析结构化的AI响应（备用方案）"""
        # 根据响应内容推断意图
        decision = {
            "action": "continue_conversation",
            "next_phase": "data_collection", 
            "response": response,
            "frontend_action": {"type": "none"},
            "waiting_for": "user_choice"
        }
        
        response_lower = response.lower()
        
        # 根据关键词推断前端行为
        if "基本信息" in response or "个人档案" in response:
            decision["frontend_action"] = {"type": "show_profile_form"}
            decision["waiting_for"] = "profile"
        elif "glim" in response_lower or "问卷" in response:
            decision["frontend_action"] = {"type": "show_glim_form"}
            decision["waiting_for"] = "glim_data"
        elif "照片" in response or "面部" in response:
            decision["frontend_action"] = {"type": "show_photo_upload"}
            decision["waiting_for"] = "photo"
        elif "bia" in response_lower or "体成分" in response:
            decision["frontend_action"] = {"type": "show_bia_upload"}
            decision["waiting_for"] = "bia_data"
        elif "分析" in response and "开始" in response:
            decision["action"] = "call_analysis"
            decision["next_phase"] = "analysis"
            decision["frontend_action"] = {"type": "show_analysis_loading"}
        
        return decision
    
    def _apply_brain_decision(self, state: AgentState, brain_response: Dict[str, Any]) -> AgentState:
        """应用AI主脑的决策到状态"""
        # 添加助手消息
        state["messages"].append({
            "role": "assistant",
            "content": brain_response.get("response", ""),
            "timestamp": datetime.now().isoformat(),
            "brain_decision": brain_response
        })
        
        # 更新状态
        state["current_phase"] = brain_response.get("next_phase", state["current_phase"])
        state["waiting_for"] = brain_response.get("waiting_for")
        
        # 设置前端动作
        frontend_action = brain_response.get("frontend_action", {"type": "none"})
        if frontend_action["type"] != "none":
            state["frontend_actions"] = [frontend_action]
        else:
            state["frontend_actions"] = []
        
        # 更新主脑上下文
        state["brain_context"]["interaction_count"] += 1
        state["brain_context"]["last_decision"] = brain_response.get("action")
        state["brain_context"]["last_update"] = datetime.now().isoformat()
        
        return state
    
    # 降级逻辑已移除 - 数据收集阶段使用简化状态机，不需要降级
    
    def _route_brain_decision(self, state: AgentState) -> str:
        """根据AI主脑决策路由下一个节点（支持自动和手动模式）"""
        current_phase = state.get("current_phase", "greeting")
        
        # === 自动模式路由优先级（新增）===
        
        # 🤖 优先级0：检查是否使用真正的工具调用智能体（核心创新）
        if self._should_use_tool_calling_agent(state):
            logger.info("🚀 路由到真正的工具调用智能体")
            return "tool_calling_agent"
        
        # 1. 数据摄取阶段
        if current_phase == "ingestion" or state.get("uploads_queue"):
            logger.info("路由到自动数据摄取节点")
            return "auto_ingestion"
            
        # 2. 串行自动分析阶段  
        if current_phase == "auto_serial_analysis":
            module_status = state.get("module_status", {})
            pending_modules = [k for k, v in module_status.items() if v == "pending"]
            running_modules = [k for k, v in module_status.items() if v == "running"]
            
            if pending_modules or running_modules:
                logger.info(f"路由到串行分析节点，待处理模块: {', '.join(pending_modules)}, 运行中: {', '.join(running_modules)}")
                return "auto_serial_analysis"
            else:
                # 没有待处理模块，检查是否可以进入综合分析
                completed_modules = [k for k, v in module_status.items() if v == "done"]
                if self._meets_auto_analysis_requirements(completed_modules):
                    logger.info("满足最小分析要求，路由到综合分析")
                    state["current_phase"] = "comprehensive_analysis"
                    return "comprehensive_analysis"
                else:
                    logger.warning("串行分析阶段未满足最小要求")
                    return "continue"
        
        # 3. 摄取失败处理
        if current_phase == "ingestion_failed":
            logger.info("数据摄取失败，继续对话")
            return "continue"
        
        # === 原有手动模式路由 ===
        
        # 如果是综合分析阶段，直接路由到综合分析
        if current_phase == "comprehensive_analysis":
            logger.info("路由到综合分析节点")
            return "comprehensive_analysis"

        # 如果是BIA处理阶段，直接路由到BIA分析
        if current_phase == "bia_processing":
            logger.info("路由到BIA分析节点")
            return "bia_analysis"

        # 检查是否已完成
        if current_phase == "completed":
            return "end"

        # 检查是否有消息
        if not state["messages"]:
            return "continue"

        last_message = state["messages"][-1]
        brain_decision = last_message.get("brain_decision", {})
        action = brain_decision.get("action", "continue_conversation")

        # 防止无限循环：如果交互次数过多，直接结束
        interaction_count = state.get("brain_context", {}).get("interaction_count", 0)
        if interaction_count > 10:  # 增加交互次数限制
            logger.warning("交互次数过多，强制结束对话")
            return "end"

        # 根据动作决定路由
        if action == "call_analysis":
            return "comprehensive_analysis"
        elif action == "call_vision_analysis":
            return "vision_analysis"
        elif action == "call_bia_analysis":
            return "bia_analysis"
        else:
            # 默认继续对话，但添加停止条件
            return "continue"
    # === 自动模式节点（新增）===
    
    async def auto_ingest_node(self, state: AgentState) -> AgentState:
        """自动数据摄取和识别节点"""
        logger.info("🔍 开始自动数据识别和标准化")
        
        uploads_queue = state.get("uploads_queue", [])
        if not uploads_queue:
            logger.warning("上传队列为空，跳过数据摄取")
            return state
            
        # 初始化状态字段
        if "module_status" not in state:
            state["module_status"] = {}
        if "errors" not in state:
            state["errors"] = []
        if "collected_data" not in state:
            state["collected_data"] = {}
            
        processed_files = []
        
        # 导入数据检测工具
        try:
            from src.utils.data_detection import detect_file_type
            from src.utils.file_parsers import parse_bia_file, parse_glim_data, validate_parsed_data
        except ImportError as e:
            logger.error(f"导入数据处理工具失败: {e}")
            state["errors"].append({
                "module": "ingestion",
                "stage": "import",
                "message": f"数据处理工具导入失败: {str(e)}",
                "retryable": False
            })
            return state
        
        for upload_item in uploads_queue:
            try:
                logger.info(f"🔍 处理文件: {upload_item['filename']}")
                
                # 1. 检测文件类型
                detection_result = detect_file_type(
                    upload_item["file_bytes"], 
                    upload_item["filename"],
                    upload_item["mimetype"]
                )
                
                logger.info(f"📋 文件类型检测结果: {detection_result['kind']} "
                           f"(置信度: {detection_result['confidence']:.2f})")
                
                # 2. 根据检测结果解析和存储
                if detection_result["kind"] == "image" and detection_result["confidence"] > 0.5:
                    self._process_facial_image(state, upload_item, detection_result)
                    logger.info("✅ 面部图像数据已处理")
                    
                elif detection_result["kind"] == "bia" and detection_result["confidence"] > 0.6:
                    self._process_bia_file(state, upload_item, detection_result)
                    logger.info("✅ BIA数据已处理")
                    
                elif detection_result["kind"] == "glim" and detection_result["confidence"] > 0.5:
                    self._process_glim_data(state, upload_item, detection_result)
                    logger.info("✅ GLIM数据已处理")
                    
                elif detection_result["kind"] == "text":
                    self._process_text_document(state, upload_item, detection_result)
                    logger.info("✅ 文本文档已处理")
                    
                else:
                    # 置信度不足或未识别类型
                    logger.warning(f"⚠️ 文件类型识别置信度不足或未识别: {upload_item['filename']}")
                    state["errors"].append({
                        "module": "ingestion",
                        "stage": "detection", 
                        "message": f"文件 {upload_item['filename']} 类型识别失败或置信度不足 (置信度: {detection_result['confidence']:.2f})",
                        "retryable": False,
                        "file_info": {
                            "filename": upload_item['filename'],
                            "detected_type": detection_result["kind"],
                            "confidence": detection_result["confidence"]
                        }
                    })
                    
                processed_files.append({
                    "filename": upload_item["filename"],
                    "type": detection_result["kind"],
                    "confidence": detection_result["confidence"],
                    "processed": detection_result["confidence"] > 0.5
                })
                
            except Exception as e:
                logger.error(f"❌ 处理文件失败: {upload_item['filename']}, 错误: {e}")
                state["errors"].append({
                    "module": "ingestion",
                    "stage": "parsing",
                    "message": f"文件 {upload_item['filename']} 解析失败: {str(e)}",
                    "retryable": True,
                    "file_info": {
                        "filename": upload_item['filename']
                    }
                })
        
        # 清空处理队列，记录处理结果
        state["uploads_queue"] = []
        state["processed_files"] = processed_files
        
        # 确定下一阶段
        module_status = state.get("module_status", {})
        pending_modules = [k for k, v in module_status.items() if v == "pending"]
        
        if pending_modules:
            state["current_phase"] = "auto_serial_analysis"
            logger.info(f"✅ 数据识别完成，发现可分析模块: {', '.join(pending_modules)}")
            
            # 向用户通知识别结果
            successful_files = [f for f in processed_files if f["processed"]]
            failed_files = [f for f in processed_files if not f["processed"]]
            
            summary_message = f"🔍 **文件识别完成**\n\n"
            summary_message += f"✅ 成功识别: {len(successful_files)} 个文件\n"
            
            if successful_files:
                for file_info in successful_files:
                    type_desc = self._get_type_description(file_info["type"])
                    summary_message += f"  • {file_info['filename']} → {type_desc}\n"
            
            if failed_files:
                summary_message += f"\n⚠️ 未识别: {len(failed_files)} 个文件\n"
                for file_info in failed_files:
                    summary_message += f"  • {file_info['filename']} (置信度不足)\n"
            
            summary_message += f"\n🔄 开始逐步分析，预计分析模块: {', '.join([self._get_module_display_name(m) for m in pending_modules])}"
            
            state["messages"].append({
                "role": "assistant",
                "content": summary_message,
                "timestamp": datetime.now().isoformat(),
                "message_type": "ingestion_complete"
            })
        else:
            state["current_phase"] = "ingestion_failed"
            logger.warning("⚠️ 未发现可分析的数据")
            
            # 向用户报告失败
            state["messages"].append({
                "role": "assistant",
                "content": f"❌ **文件处理遇到问题**\n\n很抱歉，上传的 {len(processed_files)} 个文件中没有找到可用于营养分析的数据。\n\n请确保上传的文件包含：\n• 面部图片 (JPG/PNG格式)\n• BIA数据表格 (Excel/CSV格式，包含PhA、BMI、ASMI等指标)\n• GLIM评估表单 (JSON格式)\n\n您可以重新上传正确格式的文件。",
                "timestamp": datetime.now().isoformat(),
                "message_type": "ingestion_failed"
            })
        
        logger.info(f"✅ 数据摄取节点完成，处理文件: {len(processed_files)}, 待分析模块: {len(pending_modules)}")
        return state

    def _process_facial_image(self, state: AgentState, upload_item: dict, detection_result: dict):
        """处理面部图像"""
        collected_data = state["collected_data"]
        
        if "facial_images" not in collected_data:
            collected_data["facial_images"] = []
        
        # 读取图像文件并转换为base64
        import base64
        try:
            with open(upload_item["file_path"], "rb") as f:
                image_bytes = f.read()
                base64_data = base64.b64encode(image_bytes).decode('utf-8')
                
            logger.info(f"成功读取并转换图像文件: {upload_item['filename']}")
        except Exception as e:
            logger.error(f"读取图像文件失败: {e}")
            raise ValueError(f"读取图像文件失败: {e}")
            
        collected_data["facial_images"].append({
            "id": upload_item["id"],
            "path": upload_item["file_path"], 
            "filename": upload_item["filename"],
            "base64_data": base64_data,  # 添加base64数据
            "confidence": detection_result["confidence"],
            "meta": detection_result.get("meta", {}),
            "uploaded_at": upload_item["uploaded_at"]
        })
        
        # 设置面部分析模块为待处理
        state["module_status"]["facial_analysis"] = "pending"
        logger.info("✅ 面部图像处理完成，模块状态设置为pending")

    def _process_bia_file(self, state: AgentState, upload_item: dict, detection_result: dict):
        """处理BIA数据文件"""
        from src.utils.file_parsers import parse_bia_file, validate_parsed_data
        
        try:
            # 解析BIA文件
            parsed_bia = parse_bia_file(upload_item["file_path"])
            
            if validate_parsed_data(parsed_bia, "bia"):
                collected_data = state["collected_data"]
                
                if "bia_files" not in collected_data:
                    collected_data["bia_files"] = []
                    
                collected_data["bia_files"].append({
                    "id": upload_item["id"], 
                    "parsed": parsed_bia,
                    "filename": upload_item["filename"],
                    "confidence": detection_result["confidence"],
                    "meta": detection_result.get("meta", {}),
                    "uploaded_at": upload_item["uploaded_at"]
                })
                
                # 设置BIA分析模块为待处理
                state["module_status"]["bia_analysis"] = "pending"
            else:
                raise ValueError("BIA数据验证失败")
                
        except Exception as e:
            logger.error(f"BIA文件处理失败: {e}")
            raise

    def _process_glim_data(self, state: AgentState, upload_item: dict, detection_result: dict):
        """处理GLIM数据"""
        from src.utils.file_parsers import parse_glim_data, validate_parsed_data
        
        try:
            # 尝试解析GLIM数据
            if upload_item["file_path"].endswith('.json'):
                with open(upload_item["file_path"], 'r', encoding='utf-8') as f:
                    glim_content = f.read()
            else:
                with open(upload_item["file_path"], 'r', encoding='utf-8') as f:
                    glim_content = f.read()
            
            parsed_glim = parse_glim_data(glim_content)
            
            if validate_parsed_data(parsed_glim, "glim"):
                collected_data = state["collected_data"]
                collected_data["glim_results"] = parsed_glim
                collected_data["glim_results"]["source_file"] = upload_item["filename"]
                collected_data["glim_results"]["uploaded_at"] = upload_item["uploaded_at"]
                
                # 设置GLIM评估模块为待处理
                state["module_status"]["glim_assessment"] = "pending"
            else:
                raise ValueError("GLIM数据验证失败")
                
        except Exception as e:
            logger.error(f"GLIM数据处理失败: {e}")
            raise

    def _process_text_document(self, state: AgentState, upload_item: dict, detection_result: dict):
        """处理文本文档（如病历）"""
        from src.utils.file_parsers import extract_text_content
        
        try:
            # 提取文本内容
            text_content = extract_text_content(upload_item["file_path"])
            
            if text_content and len(text_content.strip()) > 0:
                collected_data = state["collected_data"]
                
                if "text_documents" not in collected_data:
                    collected_data["text_documents"] = []
                    
                collected_data["text_documents"].append({
                    "id": upload_item["id"],
                    "filename": upload_item["filename"],
                    "content": text_content,
                    "confidence": detection_result["confidence"],
                    "meta": detection_result.get("meta", {}),
                    "uploaded_at": upload_item["uploaded_at"]
                })
                
                logger.info(f"文本文档已提取，长度: {len(text_content)} 字符")
            else:
                raise ValueError("文本内容为空")
                
        except Exception as e:
            logger.error(f"文本文档处理失败: {e}")
            # 文本文档失败不影响整体流程，记录警告即可
    
    def _get_type_description(self, file_type: str) -> str:
        """获取文件类型的中文描述"""
        descriptions = {
            "image": "面部图像",
            "bia": "BIA体成分数据",
            "glim": "GLIM评估表单",
            "text": "文本文档"
        }
        return descriptions.get(file_type, file_type)
    
    def _get_module_display_name(self, module: str) -> str:
        """获取模块的显示名称"""
        names = {
            "facial_analysis": "面部视觉分析",
            "bia_analysis": "BIA体成分分析",
            "glim_assessment": "GLIM营养评估"
        }
        return names.get(module, module)
    
    async def auto_serial_analysis_node(self, state: AgentState) -> AgentState:
        """串行自动分析节点（一步步执行）"""
        logger.info("🔄 开始串行自动分析")
        
        module_status = state.get("module_status", {})
        pending_modules = [k for k, v in module_status.items() if v == "pending"]
        running_modules = [k for k, v in module_status.items() if v == "running"]
        
        # 如果有正在运行的模块，等待其完成
        if running_modules:
            logger.info(f"⏳ 等待运行中的模块完成: {', '.join(running_modules)}")
            return state
        
        # 如果没有待处理模块，检查是否完成
        if not pending_modules:
            completed_modules = [k for k, v in module_status.items() if v == "done"]
            error_modules = [k for k, v in module_status.items() if v == "error"]
            
            logger.info(f"✅ 串行分析节点完成，当前模块状态: {module_status}")
            logger.info(f"✅ 已完成模块: {completed_modules}")
            logger.info(f"❌ 失败模块: {error_modules}")
            
            # 如果有错误模块但没有成功完成的模块，不应该进入综合分析
            if error_modules and not completed_modules:
                logger.warning("串行分析阶段未满足最小要求 - 所有模块都失败")
                state["messages"].append({
                    "role": "assistant",
                    "content": f"❌ **分析遇到问题**\n\n所有分析模块都执行失败。请检查上传的文件格式和内容，或尝试重新上传。\n\n失败的模块: {', '.join([self._get_module_display_name(m) for m in error_modules])}",
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "analysis_failed"
                })
                state["current_phase"] = "analysis_failed"
                return state
            
            # 检查是否满足综合分析要求
            meets_requirements = self._meets_auto_analysis_requirements(completed_modules)
            logger.info(f"🔍 综合分析要求检查结果: {meets_requirements}")
            
            if meets_requirements:
                logger.info("✅ 满足综合分析要求，设置进入综合分析阶段")
                state["current_phase"] = "comprehensive_analysis"
                state["messages"].append({
                    "role": "assistant", 
                    "content": f"✅ **逐步分析完成**\n\n已完成分析模块: {', '.join([self._get_module_display_name(m) for m in completed_modules])}\n\n🧠 开始进行综合分析...",
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "serial_analysis_complete"
                })
                logger.info("🔄 串行分析完成，准备进入综合分析")
            else:
                logger.warning("串行分析阶段未满足综合分析的最小要求")
                state["current_phase"] = "completed"
                state["messages"].append({
                    "role": "assistant",
                    "content": f"⚠️ **分析完成但数据不足**\n\n已完成模块: {', '.join([self._get_module_display_name(m) for m in completed_modules])}\n\n由于可用数据有限，无法生成完整的综合分析报告。建议您上传更多数据文件。",
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "analysis_insufficient"
                })
            
            return state
        
        # 选择下一个要执行的模块（按优先级顺序）
        module_priority = ["glim_assessment", "facial_analysis", "bia_analysis"]
        next_module = None
        
        for priority_module in module_priority:
            if priority_module in pending_modules:
                next_module = priority_module
                break
        
        # 如果没有找到优先模块，取第一个
        if not next_module:
            next_module = pending_modules[0]
        
        logger.info(f"🔍 开始执行模块: {next_module}")
        
        # 设置模块为运行状态
        module_status[next_module] = "running"
        
        # 向用户通知开始分析
        module_name = self._get_module_display_name(next_module)
        state["messages"].append({
            "role": "assistant",
            "content": f"🔍 **开始{module_name}**\n\n正在分析相关数据，请稍候...",
            "timestamp": datetime.now().isoformat(),
            "message_type": "module_start"
        })
        
        try:
            # 执行具体的分析模块
            if next_module == "facial_analysis":
                state = await self._execute_facial_analysis(state)
            elif next_module == "bia_analysis": 
                state = await self._execute_bia_analysis(state)
            elif next_module == "glim_assessment":
                state = await self._execute_glim_assessment(state)
            else:
                raise ValueError(f"未知的分析模块: {next_module}")
            
            # 标记为完成
            module_status[next_module] = "done"
            
            # 向用户通知完成
            state["messages"].append({
                "role": "assistant",
                "content": f"✅ **{module_name}完成**\n\n分析结果已记录，继续下一个模块...",
                "timestamp": datetime.now().isoformat(),
                "message_type": "module_complete"
            })
            
        except Exception as e:
            logger.error(f"❌ 模块执行失败: {next_module}, 错误: {e}")
            
            # 标记为失败
            module_status[next_module] = "error"
            
            # 记录错误
            if "errors" not in state:
                state["errors"] = []
            state["errors"].append({
                "module": next_module,
                "stage": "execution", 
                "message": str(e),
                "retryable": True
            })
            
            # 向用户通知失败
            state["messages"].append({
                "role": "assistant",
                "content": f"❌ **{module_name}失败**\n\n分析过程中出现错误: {str(e)}\n\n将继续执行其他模块...",
                "timestamp": datetime.now().isoformat(),
                "message_type": "module_error"
            })
        
        logger.info(f"✅ 串行分析节点完成，当前模块状态: {module_status}")
        return state
    
    async def _execute_facial_analysis(self, state: AgentState) -> AgentState:
        """执行面部分析"""
        logger.info("👤 执行面部视觉分析")
        
        collected_data = state.get("collected_data", {})
        facial_images = collected_data.get("facial_images", [])
        
        if not facial_images:
            raise ValueError("未找到面部图像数据")
        
        # 获取第一张图像
        image_data = facial_images[0]
        
        # 如果图像数据有文件路径，需要读取并转换为base64
        if "base64_data" not in image_data and "path" in image_data:
            import base64
            try:
                with open(image_data["path"], "rb") as f:
                    image_bytes = f.read()
                    image_data["base64_data"] = base64.b64encode(image_bytes).decode('utf-8')
                logger.info(f"成功读取图像文件并转换为base64: {image_data['filename']}")
            except Exception as e:
                logger.error(f"读取图像文件失败: {e}")
                raise ValueError(f"读取图像文件失败: {e}")
        
        # 将图像数据转换为手动分析方法期望的格式，添加到messages中
        image_message = {
            "role": "user",
            "content": "面部图像",
            "data_type": "photo",
            "data": {
                "image_base64": image_data.get("base64_data", "")
            }
        }
        
        # 临时添加图像消息
        state["messages"].append(image_message)
        
        try:
            # 直接调用现有的手动面部分析方法
            result_state = await self.call_vision_analysis_node(state)
            
            # 将手动分析的结果转换为自动模式格式
            if result_state["collected_data"].get("facial_analysis"):
                facial_analysis = result_state["collected_data"]["facial_analysis"]
                collected_data["facial_analysis_result"] = {
                    "success": True,
                    "analysis": facial_analysis["analysis"],
                    "model": facial_analysis.get("model", "vision"),
                    "timestamp": facial_analysis.get("timestamp", datetime.now().isoformat())
                }
                
                # 确保自动模式下也保存facial_analysis数据供综合分析使用
                collected_data["facial_analysis"] = facial_analysis
                
                # 更新状态
                state["collected_data"] = collected_data
                
                logger.info("✅ 面部分析完成，数据已保存到collected_data")
            else:
                logger.error("❌ 面部分析未产生有效结果")
                raise ValueError("面部分析未产生有效结果")
            
        finally:
            # 移除临时添加的图像消息
            if state["messages"] and state["messages"][-1] == image_message:
                state["messages"].pop()
        
        return state
    
    async def _execute_bia_analysis(self, state: AgentState) -> AgentState:
        """执行BIA体成分分析"""
        logger.info("⚡ 执行BIA体成分分析")
        
        collected_data = state.get("collected_data", {})
        bia_files = collected_data.get("bia_files", [])
        
        if not bia_files:
            raise ValueError("未找到BIA数据文件")
        
        # 获取第一个BIA文件的解析结果
        bia_data = bia_files[0]["parsed"]
        
        # 调用BIA计算器
        from src.tools.bia_calculator import BIACalculator
        bia_calculator = BIACalculator()
        
        # 处理BIA数据
        result = bia_calculator.calculate_analysis(bia_data)
        
        # 存储结果
        collected_data["bia_analysis_result"] = result
        
        return state
    
    async def _execute_glim_assessment(self, state: AgentState) -> AgentState:
        """执行GLIM营养评估"""
        logger.info("📋 执行GLIM营养评估")
        
        collected_data = state.get("collected_data", {})
        glim_data = collected_data.get("glim_results")
        
        if not glim_data:
            raise ValueError("未找到GLIM评估数据")
        
        # 调用GLIM表单处理器
        from src.tools.glim_form_processor import GLIMFormProcessor
        glim_processor = GLIMFormProcessor()
        
        # 处理GLIM数据
        result = glim_processor.process_form(glim_data)
        
        # 存储结果
        collected_data["glim_analysis_result"] = result
        
        return state
    
    def _meets_auto_analysis_requirements(self, completed_modules: list) -> bool:
        """检查是否满足自动分析的最小要求"""
        try:
            from config.settings import settings
            min_requirements = settings.get_auto_analysis_config()
            
            logger.info(f"🔍 检查分析要求: completed_modules={completed_modules}")
            logger.info(f"🔍 最小要求: {min_requirements}")
            
            # 至少需要指定数量的模块完成
            if len(completed_modules) < min_requirements["min_modules"]:
                logger.info(f"❌ 模块数量不足: {len(completed_modules)} < {min_requirements['min_modules']}")
                return False
            
            # 检查是否包含优选模块
            preferred_modules = min_requirements["preferred_modules"]
            has_preferred = any(mod in completed_modules for mod in preferred_modules)
            
            if has_preferred:
                logger.info(f"✅ 包含优选模块: {[mod for mod in completed_modules if mod in preferred_modules]}")
                return True
            
            # 或者至少2个模块
            if len(completed_modules) >= 2:
                logger.info(f"✅ 模块数量充足: {len(completed_modules)} >= 2")
                return True
            
            # 如果只有1个模块，也允许进行分析（降级处理）
            if len(completed_modules) >= 1:
                logger.info(f"⚠️ 单模块分析: {completed_modules[0]}")
                return True
            
            logger.info("❌ 未满足任何分析要求")
            return False
            
        except Exception as e:
            logger.warning(f"检查分析要求时出错: {e}")
            # 降级：至少1个模块就允许分析
            result = len(completed_modules) >= 1
            logger.info(f"🔄 降级处理: {result} (模块数: {len(completed_modules)})")
            return result
    
    async def call_vision_analysis_node(self, state: AgentState) -> AgentState:
        """调用视觉分析节点"""
        logger.info("执行面部图像视觉分析")
        
        # 从用户消息中获取图像数据
        image_data = None
        for msg in reversed(state["messages"]):
            if msg["role"] == "user" and msg.get("data_type") == "photo":
                image_data = msg.get("data", {}).get("image_base64")
                break
        
        if not image_data:
            logger.error("未找到面部图像数据")
            return state
        
        try:
            # 获取患者信息以构建上下文化的提示词
            patient_info = state.get("user_profile", {})
            
            # 使用专业的视觉分析提示词系统
            try:
                from config.vision_analysis_prompts import get_contextual_vision_prompt
                prompt = get_contextual_vision_prompt(patient_info, instance_id=1)
                logger.info(f"使用专业视觉分析提示词，长度: {len(prompt)} 字符")
            except ImportError as e:
                error_msg = f"❌ 无法导入视觉分析提示词配置: {e}。请检查 config/vision_analysis_prompts.py 文件是否存在且格式正确。"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            result = self.lm_client.call_vision_model(
                image_data=image_data,
                prompt=prompt,
                timeout=settings.VISION_ANALYSIS_TIMEOUT  # 使用配置文件中的超时设置
            )
            
            if result['success']:
                state["collected_data"]["facial_analysis"] = {
                    "analysis": result['analysis'],
                    "model": result.get('model'),
                    "timestamp": datetime.now().isoformat()
                }
                
                # 只显示分析完成状态，不展示详细结果
                completion_message = """✅ **面部视觉分析已完成**

🔍 AI专家已对您的面部照片完成详细的营养状态评估，分析结果已保存。

📋 分析数据将在您完成所有评估项目后，结合GLIM评估和BIA数据，形成综合营养分析报告。

💡 请继续完成其他评估项目，或点击"完成收集"生成最终报告。"""
                
                state["messages"].append({
                    "role": "assistant",
                    "content": completion_message,
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "visual_analysis_completed"
                })
                
                logger.info("面部视觉分析完成，结果已保存待综合分析")
            else:
                logger.error(f"视觉分析失败: {result['error']}")
                state["collected_data"]["facial_analysis"] = {
                    "error": result['error'],
                    "timestamp": datetime.now().isoformat()
                }
        
        except Exception as e:
            logger.error(f"视觉分析异常: {e}")
            state["collected_data"]["facial_analysis"] = {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        return state
    
    async def call_bia_analysis_node(self, state: AgentState) -> AgentState:
        """调用BIA分析节点"""
        logger.info("🔍 执行BIA数据分析")

        # 从用户消息中获取BIA文件路径
        bia_file = None
        for msg in reversed(state["messages"]):
            if msg["role"] == "user" and msg.get("data_type") == "bia":
                bia_file = msg.get("data", {}).get("file_path")
                logger.info(f"🔍 找到BIA文件路径: {bia_file}")
                break

        if not bia_file:
            logger.error("❌ 未找到BIA数据文件")
            return state

        # 检查文件是否存在
        import os
        if not os.path.exists(bia_file):
            logger.error(f"❌ BIA文件不存在: {bia_file}")
            state["collected_data"]["bia_analysis"] = {
                "error": f"BIA数据文件不存在: {bia_file}",
                "timestamp": datetime.now().isoformat()
            }
            return state

        logger.info(f"✅ BIA文件存在，开始分析: {bia_file}")
        
        try:
            # 加载和分析BIA数据
            bia_df = self.bia_calculator.load_bia_data(bia_file)
            if bia_df is not None:
                patient_data = bia_df.iloc[0]  # 分析第一行数据
                bia_analysis = self.bia_calculator.calculate_patient_bia_analysis(patient_data)
                
                state["collected_data"]["bia_analysis"] = {
                    "analysis": bia_analysis,
                    "timestamp": datetime.now().isoformat()
                }
                logger.info("BIA数据分析完成")
            else:
                raise Exception("BIA数据文件读取失败")
        
        except Exception as e:
            logger.error(f"BIA分析异常: {e}")
            state["collected_data"]["bia_analysis"] = {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        # 分析完成后，调用数据完成处理逻辑生成响应消息
        return self._handle_data_completion(state, "bia")
    
    async def call_comprehensive_analysis_node(self, state: AgentState) -> AgentState:
        """调用综合分析节点"""
        logger.info("执行综合分析")
        
        # 添加调试信息
        collected_data = state.get("collected_data", {})
        logger.info(f"🔍 调试：当前收集的数据: {list(collected_data.keys())}")
        for key, value in collected_data.items():
            if value:
                logger.info(f"🔍 调试：{key} 数据存在，类型: {type(value)}")
            else:
                logger.info(f"🔍 调试：{key} 数据为空或None")
        
        try:
            # 构建综合分析提示词
            logger.info("🔍 调试：开始构建综合分析提示词...")
            analysis_prompt = self._build_comprehensive_analysis_prompt(state)
            logger.info("🔍 调试：综合分析提示词构建成功")

            logger.info("=" * 60)
            logger.info("🔍 综合分析提示词构建完成")
            logger.info("=" * 60)
            logger.info("📊 提示词长度: {} 字符".format(len(analysis_prompt)))
            logger.info("📋 包含数据类型:")
            collected_data = state.get("collected_data", {})
            data_types = []
            if collected_data.get("glim_results"):
                logger.info("  ✅ GLIM评估数据")
                data_types.append("GLIM")
            if collected_data.get("facial_analysis"):
                logger.info("  ✅ 面部分析数据")
                data_types.append("面部分析")
            if collected_data.get("bia_analysis"):
                logger.info("  ✅ BIA体成分数据")
                data_types.append("BIA")
            logger.info("=" * 60)

            # 记录到增强日志 - 综合分析开始
            try:
                import builtins
                if hasattr(builtins, 'add_enhanced_log'):
                    builtins.add_enhanced_log(
                        'ai-call',
                        '综合分析调用',
                        model='freedomintelligence.huatuogpt-o1-7b',
                        system_prompt='你是一位资深的临床营养学专家和医学AI助手...',
                        user_prompt=analysis_prompt,
                        collected_data=state.get("collected_data", {}),
                        user_profile=state.get("user_profile", {}),
                        data_types=data_types,
                        prompt_length=len(analysis_prompt)
                    )
                    logger.info("✅ 增强日志记录成功: 综合分析调用")
                else:
                    logger.debug("增强日志函数不可用")
            except Exception as e:
                logger.debug(f"增强日志记录失败: {e}")

            # 调用华佗GPT进行综合分析
            logger.info("🔍 调试：开始调用华佗GPT...")
            result = self.lm_client.call_huatuogpt(
                prompt=analysis_prompt,
                timeout=120
            )
            logger.info(f"🔍 调试：华佗GPT调用完成，成功: {result.get('success', False)}")
            
            if result['success']:
                analysis_content = result['analysis']

                # 记录到增强日志 - 综合分析响应
                try:
                    import builtins
                    if hasattr(builtins, 'add_enhanced_log'):
                        builtins.add_enhanced_log(
                            'ai-response',
                            '综合分析响应',
                            model='freedomintelligence.huatuogpt-o1-7b',
                            response_content=analysis_content,
                            token_usage=result.get('usage', {}),
                            user_prompt=analysis_prompt,
                            performance={'response_length': len(analysis_content)}
                        )
                        logger.info("✅ 增强日志记录成功: 综合分析响应")
                    else:
                        logger.debug("增强日志函数不可用")
                except Exception as e:
                    logger.debug(f"增强日志记录失败: {e}")

                # 构建包含分析结果和后续提示的完整消息
                complete_response = f"""{analysis_content}

---

🎉 **营养状况分析报告已完成！**

如果您对报告有任何疑问，或需要更详细的建议，请随时提问。我可以为您解释报告中的任何部分，或提供个性化的营养改善方案。"""

                # 添加完整的分析结果和提示到消息历史
                state["messages"].append({
                    "role": "assistant",
                    "content": complete_response,
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "comprehensive_analysis_complete"
                })

                # 保存最终报告（只保存分析结果部分）
                state["final_report"] = analysis_content

                # 转换到多轮对话阶段，支持后续询问
                state["current_phase"] = "follow_up_conversation"
                state["waiting_for"] = "user_questions"

                logger.info("综合分析完成，完整报告已展示，用户可进行后续询问")
            else:
                logger.error(f"综合分析失败: {result['error']}")
                error_msg = f"综合分析过程中出现问题：{result['error']}\n\n请稍后重试，或联系技术支持。"

                state["messages"].append({
                    "role": "assistant",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "error"
                })

                # 分析失败时回到数据收集阶段
                state["current_phase"] = "data_collection"
        
        except Exception as e:
            logger.error(f"综合分析异常: {e}")
            logger.error(f"🔍 调试：异常详细信息: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"🔍 调试：异常堆栈: {traceback.format_exc()}")
            state["final_report"] = f"分析过程中出现错误：{str(e)}"
        
        return state
    
    def _build_comprehensive_analysis_prompt(self, state: AgentState) -> str:
        """构建动态综合分析提示词（仅包含实际使用的模块）"""
        user_profile = state["user_profile"]
        collected_data = state["collected_data"]
        
        try:
            logger.debug("🔍 调试：开始导入动态提示词构建模块...")
            # 使用带长度控制的动态模块化提示词系统
            from config.comprehensive_analysis_prompts import (
                build_dynamic_comprehensive_prompt_with_length_control,
                estimate_tokens
            )
            logger.debug("🔍 调试：成功导入动态提示词构建模块")
            
            # 构建实际可用的模块数据
            available_modules_data = {}
            logger.debug("🔍 调试：开始构建模块数据...")
            
            # 检查面部分析数据
            logger.debug("🔍 调试：检查面部分析数据...")
            if collected_data.get("facial_analysis") and collected_data["facial_analysis"].get("analysis"):
                logger.debug("🔍 调试：开始处理面部分析数据...")
                raw_analysis = collected_data['facial_analysis'].get('analysis', '')
                logger.debug(f"🔍 调试：原始面部分析数据长度: {len(raw_analysis)}")
                
                logger.debug("🔍 调试：开始格式化视觉分析结果...")
                try:
                    formatted_analysis = self._format_visual_analysis_result(raw_analysis)
                    logger.debug(f"🔍 调试：格式化后数据长度: {len(formatted_analysis)}")
                except Exception as format_error:
                    logger.error(f"🔍 调试：格式化视觉分析结果失败: {format_error}")
                    # 如果格式化失败，使用原始数据的安全版本
                    formatted_analysis = json.dumps({
                        "原始分析": str(raw_analysis)[:1000] + "..." if len(str(raw_analysis)) > 1000 else str(raw_analysis),
                        "说明": "格式化失败，使用原始数据"
                    }, ensure_ascii=False, indent=2)
                    logger.debug(f"🔍 调试：使用原始数据替代，长度: {len(formatted_analysis)}")
                
                # 清理可能引起混淆的内容，避免提及其他未使用的评估工具
                logger.debug("🔍 调试：开始清理面部分析内容...")
                try:
                    cleaned_analysis = self._clean_facial_analysis_for_comprehensive(formatted_analysis)
                    logger.debug(f"🔍 调试：清理后数据长度: {len(cleaned_analysis)}")
                except Exception as clean_error:
                    logger.error(f"🔍 调试：清理面部分析内容失败: {clean_error}")
                    # 如果清理失败，使用格式化后的数据
                    cleaned_analysis = formatted_analysis
                    logger.debug("🔍 调试：清理失败，使用格式化数据")
                
                available_modules_data["facial_analysis"] = cleaned_analysis
                logger.info("✅ 包含面部视觉分析模块")
            else:
                logger.debug("🔍 调试：未找到面部分析数据")
            
            # 检查BIA分析数据  
            logger.debug("🔍 调试：检查BIA分析数据...")
            if collected_data.get("bia_analysis"):
                logger.debug("🔍 调试：开始处理BIA分析数据...")
                bia_formatted = json.dumps(collected_data['bia_analysis'], ensure_ascii=False, indent=2)
                available_modules_data["bia_analysis"] = bia_formatted
                logger.info("✅ 包含BIA体成分分析模块")
            else:
                logger.debug("🔍 调试：未找到BIA分析数据")
            
            # 检查GLIM评估数据
            logger.debug("🔍 调试：检查GLIM评估数据...")
            if collected_data.get("glim_results"):
                logger.debug("🔍 调试：开始处理GLIM评估数据...")
                glim_formatted = json.dumps(collected_data['glim_results'], ensure_ascii=False, indent=2)
                available_modules_data["glim_assessment"] = glim_formatted
                logger.info("✅ 包含GLIM量化评估模块")
            else:
                logger.debug("🔍 调试：未找到GLIM评估数据")
            
            # 构建患者信息
            logger.debug("🔍 调试：构建患者信息...")
            patient_info_formatted = json.dumps(user_profile, ensure_ascii=False, indent=2)
            logger.debug(f"🔍 调试：患者信息长度: {len(patient_info_formatted)}")
            
            logger.debug(f"🔍 调试：可用模块数量: {len(available_modules_data)}")
            logger.debug(f"🔍 调试：可用模块列表: {list(available_modules_data.keys())}")
            
            # 动态构建提示词（带长度控制）
            logger.debug("🔍 调试：开始调用动态提示词构建函数...")
            dynamic_prompt = build_dynamic_comprehensive_prompt_with_length_control(
                patient_info=patient_info_formatted,
                available_modules_data=available_modules_data,
                max_tokens=3500  # 限制在3500 tokens以内，为模型留出响应空间
            )
            logger.debug("🔍 调试：动态提示词构建成功")
            
            # 记录提示词长度信息
            estimated_tokens = estimate_tokens(dynamic_prompt)
            logger.info(f"🎯 动态构建综合分析提示词，实际使用模块: {list(available_modules_data.keys())}")
            logger.info(f"📏 估计提示词长度: {estimated_tokens} tokens (限制: 3500)")
            
            if estimated_tokens > 3500:
                logger.warning(f"⚠️ 提示词长度仍超出限制，可能需要进一步优化")
            
            return dynamic_prompt
            
        except ImportError as e:
            logger.warning(f"🔍 调试：导入错误 - 无法导入动态提示词系统，使用传统提示词: {e}")
            # 降级到传统提示词
            return self._build_legacy_comprehensive_analysis_prompt(state)
        except Exception as e:
            logger.error(f"🔍 调试：构建综合分析提示词时发生异常: {type(e).__name__}: {e}")
            logger.error(f"🔍 调试：异常详情 - {str(e)}", exc_info=True)
            # 降级到传统提示词
            return self._build_legacy_comprehensive_analysis_prompt(state)
    
    def _build_legacy_comprehensive_analysis_prompt(self, state: AgentState) -> str:
        """构建传统综合分析提示词（降级方案）"""
        user_profile = state["user_profile"]
        collected_data = state["collected_data"]
        
        prompt = f"""请基于以下收集到的多模态数据，进行综合的营养状况评估和诊断。

## 患者基本信息
{json.dumps(user_profile, ensure_ascii=False, indent=2)}

## 收集到的评估数据
"""
        
        # 添加各类分析数据
        if collected_data.get("glim_results"):
            prompt += f"\n### GLIM评估结果\n{json.dumps(collected_data['glim_results'], ensure_ascii=False, indent=2)}\n"
        
        if collected_data.get("facial_analysis"):
            raw_analysis = collected_data['facial_analysis'].get('analysis', '分析失败')
            formatted_analysis = self._format_visual_analysis_result(raw_analysis)
            prompt += f"\n### 面部视觉分析\n{formatted_analysis}\n"
        
        if collected_data.get("bia_analysis"):
            prompt += f"\n### BIA体成分分析\n{json.dumps(collected_data['bia_analysis'], ensure_ascii=False, indent=2)}\n"
        
        # 动态生成使用的评估模块列表
        used_modules = []
        if collected_data.get("facial_analysis"):
            used_modules.append("面部视觉分析")
        if collected_data.get("bia_analysis"):
            used_modules.append("BIA体成分分析")
        if collected_data.get("glim_results"):
            used_modules.append("GLIM量化评估")
        
        module_list = "、".join(used_modules) if used_modules else "无评估数据"
        
        prompt += f"""
请进行专业、全面的分析，并严格按照以下格式输出：

## 📋 分析概览
- 使用的评估模块：{module_list}
- 数据一致性：[高度一致/基本一致/存在分歧/数据不足]
- 系统置信度：[0-100%]

⚠️ 重要提醒：请严格基于实际提供的数据进行分析，不要假设或声称使用了未提供的评估模块。

## 🎯 营养状况评估
**初步诊断：[具体诊断结果]**

## 💡 支持证据
[列出支持诊断的关键证据点]

## 🏥 专业建议
[提供具体的营养干预和治疗建议]

## 📅 后续建议
- 复查频率：[具体时间]
- 建议持续时间：[具体时长]
- 重点关注：[重点监测项目]

## ⚠️ 重要提醒
这是基于AI技术的初步筛查结果，仅供参考。请务必咨询专业的营养科医生或您的主治医生，获取更详细的诊断和治疗指导。建议您携带这份报告前往医院进行进一步检查。"""
        
        return prompt
    
    async def finalize_conversation_node(self, state: AgentState) -> AgentState:
        """结束对话节点"""
        logger.info("对话会话结束")
        
        if state["final_report"]:
            # 添加最终报告消息
            state["messages"].append({
                "role": "assistant",
                "content": state["final_report"],
                "timestamp": datetime.now().isoformat(),
                "message_type": "final_report"
            })
        
        state["current_phase"] = "completed"
        state["frontend_actions"] = [{"type": "show_completion"}]
        
        return state
    
    async def add_user_input(self, state: AgentState, message: str, data: Dict[str, Any] = None) -> AgentState:
        """添加用户输入到状态"""
        user_msg = {
            "role": "user", 
            "content": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if data:
            user_msg.update(data)
        
        state["messages"].append(user_msg)
        
        # 处理特殊数据类型
        if data:
            data_type = data.get("data_type")
            if data_type == "profile":
                profile_data = data.get("data", {})
                
                # 清理用户档案数据 - 移除无效的身高体重值
                cleaned_profile_data = profile_data.copy()
                
                # 检查并移除无效的身高值
                height = profile_data.get('height')
                if height is not None:
                    try:
                        height_val = float(height) if height != '' else 0
                        if height_val <= 0:
                            cleaned_profile_data.pop('height', None)
                            logger.info("对话智能体：已移除无效的身高值")
                    except (ValueError, TypeError):
                        cleaned_profile_data.pop('height', None)
                        logger.info("对话智能体：已移除非数字身高值")
                
                # 检查并移除无效的体重值  
                weight = profile_data.get('weight') or profile_data.get('current_weight')
                if weight is not None:
                    try:
                        weight_val = float(weight) if weight != '' else 0
                        if weight_val <= 0:
                            cleaned_profile_data.pop('weight', None)
                            cleaned_profile_data.pop('current_weight', None)
                            logger.info("对话智能体：已移除无效的体重值")
                    except (ValueError, TypeError):
                        cleaned_profile_data.pop('weight', None)
                        cleaned_profile_data.pop('current_weight', None)
                        logger.info("对话智能体：已移除非数字体重值")
                
                state["user_profile"] = cleaned_profile_data
                state["waiting_for"] = None
                
            elif data_type == "glim":
                # 处理GLIM数据
                glim_data = data.get("data", {})
                try:
                    result = self.glim_processor.process_form_data(glim_data)
                    state["collected_data"]["glim_results"] = result
                except Exception as e:
                    logger.error(f"GLIM数据处理失败: {e}")
                    state["collected_data"]["glim_results"] = {"error": str(e)}
        
        return state
    
    async def process_conversation_turn(self, state: AgentState) -> AgentState:
        """处理一轮对话"""
        try:
            # 运行工作流 - 现在每次只运行一次，不会循环
            result = await self.workflow.ainvoke(state)
            return result
        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            # 添加错误消息
            state["messages"].append({
                "role": "assistant",
                "content": f"❌ 处理过程中出现错误：{str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            return state
    
    def _should_use_tool_calling_agent(self, state: AgentState) -> bool:
        """判断是否应该使用真正的工具调用智能体"""
        try:
            current_phase = state.get("current_phase", "greeting")
            
            # 🎯 使用工具调用智能体的条件判断
            
            # 条件1：明确的自动分析请求且有上传文件
            if (current_phase == "auto_tool_calling" or 
                (state.get("uploads_queue") and len(state.get("uploads_queue", [])) > 0)):
                
                # 检查是否有多种数据类型（适合智能体自主决策）
                uploads = state.get("uploads_queue", [])
                if len(uploads) >= 1:  # 有上传文件就优先使用智能体
                    logger.info("🤖 检测到上传文件，使用工具调用智能体进行智能分析")
                    return True
            
            # 条件2：用户明确要求智能分析
            messages = state.get("messages", [])
            if messages:
                last_message = messages[-1]
                if last_message.get("role") == "user":
                    content = last_message.get("content", "").lower()
                    智能分析_keywords = ["智能分析", "自动分析", "帮我分析", "综合分析", "全面分析"]
                    if any(keyword in content for keyword in 智能分析_keywords):
                        logger.info("🤖 检测到智能分析请求，使用工具调用智能体")
                        return True
            
            # 条件3：复杂数据组合（适合智能体处理）
            collected_data = state.get("collected_data", {})
            data_types_count = sum([
                1 if collected_data.get("facial_images") else 0,
                1 if collected_data.get("bia_data") else 0,
                1 if collected_data.get("glim_data") else 0
            ])
            
            if data_types_count >= 2:  # 有2种以上数据类型
                logger.info("🤖 检测到复杂数据组合，使用工具调用智能体")
                return True
            
            return False
            
        except Exception as e:
            logger.warning(f"工具调用智能体判断失败: {e}")
            return False
    
    async def tool_calling_agent_node(self, state: AgentState) -> AgentState:
        """🤖 真正的工具调用智能体节点 - HuatuoGPT自主调用工具"""
        try:
            logger.info("🚀 启动真正的工具调用智能体")
            
            # 获取工具调用智能体
            tool_agent = get_tool_calling_agent()
            
            # 准备输入数据
            messages = state.get("messages", [])
            last_user_message = ""
            
            # 获取用户最后的消息
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    last_user_message = msg.get("content", "")
                    break
            
            # 如果没有明确的用户消息，生成一个智能分析请求
            if not last_user_message:
                uploads_count = len(state.get("uploads_queue", []))
                last_user_message = f"我上传了{uploads_count}个文件，请帮我进行智能营养分析"
            
            # 运行工具调用智能体
            result = await tool_agent.run_conversation(
                user_input=last_user_message,
                user_profile=state.get("user_profile", {}),
                uploaded_files=state.get("uploads_queue", []),
                session_id=state.get("brain_context", {}).get("session_id")
            )
            
            if result.get("success"):
                # 智能体分析成功
                response = result.get("response", "")
                
                # 添加智能体的回复到对话历史
                state["messages"].append({
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "tool_calling_agent_response",
                    "analysis_complete": True
                })
                
                # 更新状态
                state["current_phase"] = "completed"
                state["final_report"] = response
                
                # 添加前端动作
                state["frontend_actions"] = [{
                    "type": "analysis_complete",
                    "data": {
                        "report": response,
                        "agent_type": "tool_calling_agent",
                        "session_id": result.get("session_id")
                    }
                }]
                
                logger.info("✅ 工具调用智能体分析完成")
                
            else:
                # 智能体分析失败，降级到传统模式
                error_msg = result.get("error", "未知错误")
                logger.error(f"工具调用智能体失败: {error_msg}")
                
                state["messages"].append({
                    "role": "assistant", 
                    "content": f"🤖 **智能体分析遇到问题**\n\n{error_msg}\n\n正在切换到传统分析模式...",
                    "timestamp": datetime.now().isoformat(),
                    "message_type": "agent_fallback"
                })
                
                # 切换到传统自动模式
                state["current_phase"] = "ingestion"
                logger.info("🔄 降级到传统自动分析模式")
            
            return state
            
        except Exception as e:
            logger.error(f"工具调用智能体节点执行失败: {e}")
            # 直接抛出异常，不进行降级处理
            raise e


# 测试函数
async def test_brain_agent():
    """测试AI主脑智能体"""
    agent = ConversationAgent()
    
    # 创建初始状态
    state = agent.create_initial_state("test_session")
    
    print("🧠 测试AI主脑控制的对话智能体...")
    
    # 运行初始对话
    state = await agent.process_conversation_turn(state)
    
    # 打印结果
    for msg in state["messages"]:
        role = "🧠 AI助手" if msg["role"] == "assistant" else "👤 用户"
        print(f"\n{role}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}")
    
    print(f"\n📊 状态信息:")
    print(f"当前阶段: {state['current_phase']}")
    print(f"等待输入: {state['waiting_for']}")
    print(f"前端动作: {state['frontend_actions']}")
    
    return state


if __name__ == "__main__":
    asyncio.run(test_brain_agent())