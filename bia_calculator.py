#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BIA身体成分分析计算器
基于Excel数据进行BIA计算，输出结构化结果
"""

import pandas as pd
import json
import math
from datetime import datetime
import os

class BIACalculator:
    def __init__(self, excel_path="data/samples/BIA数据/用户相关数据.xlsx"):
        self.excel_path = excel_path
        
    def load_data(self):
        """从Excel文件加载BIA数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            return df.to_dict('records')[0]  # 假设只有一行数据
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            # 返回测试数据
            return {
                '姓名': '张某某',
                '年龄': 65,
                '性别': '男',
                '身高': 170,
                '体重': 58,
                '阻抗值': 520,
                '电抗值': 45,
                '相位角': 4.9,
                '频率': 50
            }
    
    def calculate_bmi(self, weight, height):
        """计算BMI"""
        return weight / math.pow(height / 100, 2)
    
    def calculate_tbw(self, height, weight, resistance, gender):
        """计算总体水分(TBW) - Kushner公式"""
        if gender == '男':
            return 0.396 * math.pow(height, 2) / resistance + 0.143 * weight + 8.399
        else:
            return 0.372 * math.pow(height, 2) / resistance + 0.096 * weight + 1.069
    
    def calculate_ffm(self, tbw):
        """计算去脂体重(FFM)"""
        return tbw / 0.732
    
    def calculate_body_fat(self, weight, ffm):
        """计算体脂量和体脂率"""
        fm = weight - ffm
        body_fat_percentage = (fm / weight) * 100
        return fm, body_fat_percentage
    
    def calculate_smm(self, height, weight, resistance, age, gender):
        """计算骨骼肌量(SMM)"""
        if gender == '男':
            return (0.244 * math.pow(height, 2) / resistance + 
                   0.064 * weight + 0.139 * age + 5.71)
        else:
            return (0.252 * math.pow(height, 2) / resistance + 
                   0.138 * weight - 0.071 * age + 2.94)
    
    def calculate_asm_asmi(self, smm, height):
        """计算四肢骨骼肌量(ASM)和骨骼肌指数(ASMI)"""
        asm = smm * 0.75  # ASM约为SMM的75%
        asmi = asm / math.pow(height / 100, 2)
        return asm, asmi
    
    def calculate_visceral_fat(self, weight, body_fat_percentage):
        """计算内脏脂肪等级(简化估算)"""
        level = round((weight * body_fat_percentage / 100 - 10) / 2)
        return max(1, level)
    
    def assess_muscle_mass(self, asmi, gender):
        """评估肌肉质量"""
        if gender == '男':
            if asmi < 7.0:
                return '肌肉质量减少'
            elif asmi < 8.5:
                return '肌肉质量正常偏低'
            else:
                return '肌肉质量正常'
        else:
            if asmi < 5.7:
                return '肌肉质量减少'
            elif asmi < 6.8:
                return '肌肉质量正常偏低'
            else:
                return '肌肉质量正常'
    
    def assess_phase_angle(self, phase_angle):
        """评估相位角"""
        if phase_angle < 5.0:
            return '偏低'
        elif phase_angle > 7.0:
            return '正常'
        else:
            return '正常偏低'
    
    def calculate_all(self):
        """执行完整的BIA计算"""
        # 加载数据
        data = self.load_data()
        
        # 提取基本信息
        name = data.get('姓名', '未知')
        age = data.get('年龄', 0)
        gender = data.get('性别', '男')
        height = data.get('身高', 170)
        weight = data.get('体重', 58)
        resistance = data.get('阻抗值', 520)
        reactance = data.get('电抗值', 45)
        phase_angle = data.get('相位角', 4.9)
        frequency = data.get('频率', 50)
        
        # 执行计算
        bmi = self.calculate_bmi(weight, height)
        tbw = self.calculate_tbw(height, weight, resistance, gender)
        ffm = self.calculate_ffm(tbw)
        fm, body_fat_percentage = self.calculate_body_fat(weight, ffm)
        smm = self.calculate_smm(height, weight, resistance, age, gender)
        asm, asmi = self.calculate_asm_asmi(smm, height)
        visceral_fat_level = self.calculate_visceral_fat(weight, body_fat_percentage)
        
        # 评估结果
        muscle_assessment = self.assess_muscle_mass(asmi, gender)
        phase_assessment = self.assess_phase_angle(phase_angle)
        
        # 构建结果
        results = {
            "patient_info": {
                "name": name,
                "age": age,
                "gender": gender,
                "height": height,
                "weight": weight
            },
            "bia_measurements": {
                "resistance": resistance,
                "reactance": reactance,
                "phase_angle": phase_angle,
                "frequency": frequency
            },
            "calculated_results": {
                "basic_indicators": {
                    "bmi": round(bmi, 1),
                    "body_fat_percentage": round(body_fat_percentage, 1),
                    "fat_mass": round(fm, 1),
                    "fat_free_mass": round(ffm, 1),
                    "total_body_water": round(tbw, 1),
                    "visceral_fat_level": visceral_fat_level
                },
                "muscle_analysis": {
                    "skeletal_muscle_mass": round(smm, 1),
                    "appendicular_skeletal_muscle": round(asm, 1),
                    "skeletal_muscle_index": round(asmi, 1),
                    "muscle_assessment": muscle_assessment
                },
                "nutrition_indicators": {
                    "phase_angle": phase_angle,
                    "phase_angle_assessment": phase_assessment,
                    "resistance": resistance,
                    "reactance": reactance
                }
            },
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        return results
    
    def save_results(self, results, output_path="bia_results.json"):
        """保存计算结果到JSON文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"BIA计算结果已保存到: {output_path}")
    
    def print_results(self, results):
        """打印计算结果"""
        print("\n" + "="*50)
        print("BIA身体成分分析结果")
        print("="*50)
        
        # 患者信息
        patient = results["patient_info"]
        print(f"\n患者信息:")
        print(f"姓名: {patient['name']}")
        print(f"年龄: {patient['age']}岁")
        print(f"性别: {patient['gender']}")
        print(f"身高: {patient['height']}cm")
        print(f"体重: {patient['weight']}kg")
        
        # 基础指标
        basic = results["calculated_results"]["basic_indicators"]
        print(f"\n基础指标:")
        print(f"BMI: {basic['bmi']} kg/m²")
        print(f"体脂率: {basic['body_fat_percentage']}%")
        print(f"体脂量: {basic['fat_mass']} kg")
        print(f"去脂体重: {basic['fat_free_mass']} kg")
        print(f"总体水分: {basic['total_body_water']} kg")
        print(f"内脏脂肪等级: {basic['visceral_fat_level']}级")
        
        # 肌肉分析
        muscle = results["calculated_results"]["muscle_analysis"]
        print(f"\n肌肉质量分析:")
        print(f"骨骼肌量(SMM): {muscle['skeletal_muscle_mass']} kg")
        print(f"四肢骨骼肌量(ASM): {muscle['appendicular_skeletal_muscle']} kg")
        print(f"骨骼肌指数(ASMI): {muscle['skeletal_muscle_index']} kg/m²")
        print(f"肌肉质量评估: {muscle['muscle_assessment']}")
        
        # 营养指标
        nutrition = results["calculated_results"]["nutrition_indicators"]
        print(f"\n营养状态指标:")
        print(f"相位角: {nutrition['phase_angle']}°")
        print(f"相位角评估: {nutrition['phase_angle_assessment']}")
        print(f"阻抗值: {nutrition['resistance']} Ω")
        print(f"电抗值: {nutrition['reactance']} Ω")
        
        print("\n" + "="*50)

def main():
    """主函数 - 执行BIA计算测试"""
    calculator = BIACalculator()
    
    # 执行计算
    results = calculator.calculate_all()
    
    # 打印结果
    calculator.print_results(results)
    
    # 保存结果
    calculator.save_results(results)
    
    return results

if __name__ == "__main__":
    main()
