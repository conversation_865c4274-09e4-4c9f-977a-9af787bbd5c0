# 统一提示词管理系统

## 项目概述

成功实施了营养分析系统的统一提示词管理，解决了原有系统中提示词分散、重复、难以维护的问题。该系统采用分层架构设计，实现了模块化、可扩展的提示词管理方案。

## 系统架构

### 📁 文件结构

```
config/
├── base_prompts.py                    # 基础提示词模板
├── facial_analysis_prompts.py         # 面部分析提示词
├── glim_prompts.py                    # GLIM评估提示词
├── comprehensive_analysis_prompts.py  # 综合分析提示词
└── unified_prompt_manager.py          # 统一提示词管理器
```

### 🏗️ 架构层次

```
统一提示词管理器 (unified_prompt_manager.py)
├── 面部分析模块 (facial_analysis_prompts.py)
├── GLIM评估模块 (glim_prompts.py)
├── 综合分析模块 (comprehensive_analysis_prompts.py)
└── 基础模板 (base_prompts.py)
```

## 核心功能

### 1. 基础提示词模板 (`base_prompts.py`)

**功能**：定义通用的角色信息和专业背景，避免重复代码

**核心组件**：
- `HOSPITAL_INFO`: 医院信息
- `BASE_PROFESSIONAL_QUALIFICATIONS`: 专业资质模板
- `BASE_ANALYSIS_PRINCIPLES`: 分析原则模板
- `GLIM_DIAGNOSTIC_FRAMEWORK`: GLIM诊断框架
- `get_base_system_prompt()`: 获取基础系统提示词

**示例**：
```python
from config.base_prompts import get_base_system_prompt

base_prompt = get_base_system_prompt()
```

### 2. 面部分析提示词 (`facial_analysis_prompts.py`)

**功能**：专门用于面部营养特征分析的提示词

**提供的函数**：
- `get_facial_analysis_system_prompt()`: 系统级面部分析提示词
- `get_facial_consensus_system_prompt()`: 面部共识分析提示词
- `get_simple_facial_prompt()`: 简单面部分析提示词

**特色功能**：
- 基于基础模板构建，避免重复
- 包含详细的面部特征评估标准
- 支持多种分析深度级别

### 3. GLIM评估提示词 (`glim_prompts.py`)

**功能**：基于GLIM国际标准的营养评估提示词

**提供的函数**：
- `get_glim_assessment_system_prompt()`: GLIM系统评估提示词
- `get_simple_glim_prompt(patient_data)`: 简单GLIM评估（支持参数）
- `get_glim_form_analysis_prompt(form_data)`: GLIM表单分析（支持参数）

**特色功能**：
- 严格遵循GLIM国际标准
- 支持动态患者数据注入
- 包含详细的评估标准和诊断流程

### 4. 综合分析提示词 (`comprehensive_analysis_prompts.py`)

**功能**：整合多模态数据的综合分析提示词

**提供的函数**：
- `get_comprehensive_analysis_system_prompt()`: 综合分析系统提示词
- `build_dynamic_comprehensive_prompt_with_length_control()`: 动态构建（支持长度控制）
- `build_comprehensive_analysis_prompt()`: 简单综合分析构建

**特色功能**：
- 支持多模态数据整合
- 动态长度控制和token管理
- 智能数据压缩和优先级排序

### 5. 统一提示词管理器 (`unified_prompt_manager.py`)

**功能**：提供统一的提示词访问接口和管理功能

**核心类**：
- `UnifiedPromptManager`: 统一管理器主类

**便捷函数**：
```python
# 获取系统提示词的统一接口
get_system_prompt(module, prompt_type, **kwargs)

# 便捷访问函数
get_facial_prompt(prompt_type)
get_glim_prompt(prompt_type, **kwargs)
get_comprehensive_prompt(prompt_type, **kwargs)
```

## 使用方法

### 基本使用

```python
from config.unified_prompt_manager import get_system_prompt

# 获取面部分析系统提示词
facial_prompt = get_system_prompt("facial", "system")

# 获取GLIM评估提示词
glim_prompt = get_system_prompt("glim", "system", patient_data={"age": 70})

# 获取综合分析提示词
comprehensive_prompt = get_system_prompt("comprehensive", "system")
```

### 便捷函数使用

```python
from config.unified_prompt_manager import (
    get_facial_prompt, 
    get_glim_prompt, 
    get_comprehensive_prompt
)

# 面部分析
facial_system = get_facial_prompt("system")
facial_simple = get_facial_prompt("simple")

# GLIM评估
glim_system = get_glim_prompt("system")
glim_with_data = get_glim_prompt("simple", patient_data={"age": 76, "gender": "男"})

# 综合分析
comp_system = get_comprehensive_prompt("system")
comp_dynamic = get_comprehensive_prompt(
    "dynamic",
    patient_info="76岁男性",
    available_modules_data={"facial": {}, "glim": {}},
    max_tokens=2000
)
```

### 在LM Studio客户端中使用

```python
from src.core.lm_studio_client import LMStudioClient

client = LMStudioClient()

# 自动使用统一提示词系统
facial_prompt = client.get_system_prompt("facial", "system")
glim_prompt = client.get_system_prompt("glim", "system") 
comprehensive_prompt = client.get_system_prompt("comprehensive", "system")
```

## 系统优势

### 1. 🎯 统一管理
- 所有提示词集中在config目录下
- 统一的访问接口和调用方式
- 消除了代码中的重复提示词

### 2. 📦 模块化设计
- 每个分析模块独立管理提示词
- 基础模板复用，减少冗余
- 易于扩展新的分析模块

### 3. 🔧 动态配置
- 支持运行时参数注入
- 智能长度控制和优化
- 降级机制确保系统稳定性

### 4. ⚡ 性能优化
- 懒加载机制，按需加载提示词
- 全局单例模式，避免重复初始化
- 智能缓存管理

### 5. 🛡️ 容错设计
- 完善的降级机制
- 导入错误自动处理
- 详细的错误日志记录

## 测试验证

### 测试覆盖

✅ **基础提示词模块测试** - 验证基础模板加载
✅ **面部分析提示词模块测试** - 验证面部分析功能
✅ **GLIM评估提示词模块测试** - 验证GLIM标准实现
✅ **综合分析提示词模块测试** - 验证多模态整合
✅ **统一提示词管理器测试** - 验证管理器功能
✅ **LM Studio客户端集成测试** - 验证系统集成

### 运行测试

```bash
python test_unified_prompt_system.py
```

**测试结果**：
```
📊 总体结果: 6/6 测试通过
🎉 所有测试通过！统一提示词管理系统工作正常
```

## 提示词统计

| 模块 | 系统提示词长度 | 功能数量 | 特色功能 |
|------|---------------|----------|----------|
| 基础模板 | 946字符 | 1个核心函数 | 通用模板复用 |
| 面部分析 | 2490字符 | 3个函数 | 多级别分析 |
| GLIM评估 | 3148字符 | 3个函数 | 动态参数注入 |
| 综合分析 | 1636字符 | 3个函数 | 智能长度控制 |

## 兼容性

### 向后兼容
- 现有代码无需修改即可使用统一系统
- 原有提示词调用方式仍然支持
- 渐进式迁移，降低风险

### 降级机制
- 模块导入失败时自动使用降级提示词
- 确保系统在各种环境下正常运行
- 详细的错误处理和日志记录

## 维护指南

### 添加新模块

1. 在config目录创建新的提示词文件
2. 继承基础模板 (`from .base_prompts import get_base_system_prompt`)
3. 在统一管理器中注册新模块
4. 添加相应的测试用例

### 修改提示词

1. 直接在对应模块文件中修改
2. 运行测试验证功能正常
3. 更新文档说明

### 性能监控

使用管理器提供的统计功能：
```python
from config.unified_prompt_manager import get_prompt_manager

manager = get_prompt_manager()
stats = manager.get_prompt_statistics()
validation = manager.validate_prompts()

print(f"系统统计: {stats}")
print(f"验证结果: {validation}")
```

## 成果总结

### 解决的问题
1. ✅ **消除提示词重复**：将分散在多个文件中的重复提示词整合
2. ✅ **统一管理接口**：提供一致的提示词访问方式
3. ✅ **提高可维护性**：模块化设计便于维护和扩展
4. ✅ **增强系统稳定性**：完善的错误处理和降级机制

### 技术成就
- 实现了分层架构的提示词管理系统
- 创建了模块化、可扩展的设计模式
- 建立了完整的测试验证体系
- 提供了便捷的开发和维护工具

### 业务价值
- 减少代码重复，提高开发效率
- 统一提示词标准，确保输出一致性
- 简化系统维护，降低运营成本
- 为系统扩展奠定坚实基础

这个统一提示词管理系统为营养分析AI系统提供了强大的基础设施，确保了系统的可维护性、可扩展性和稳定性。
