<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能营养顾问</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f7f7f8;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部工具栏 */
        .toolbar {
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .toolbar-title {
            font-size: 16px;
            font-weight: 600;
            color: #202123;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }
        
        .model-status-display {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .model-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }
        
        .model-label {
            color: #6b7280;
        }
        
        .model-status {
            font-weight: 500;
        }
        
        .model-status.loading {
            color: #f59e0b;
        }
        
        .model-status.ready {
            color: #10b981;
        }
        
        .model-status.error {
            color: #ef4444;
        }
        
        .toolbar-btn {
            background: #f4f4f5;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .toolbar-btn:hover {
            background: #e4e4e7;
        }
        
        .toolbar-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        /* === 主容器布局 === */
        .main-container {
            flex: 1;
            display: flex;
            padding-top: 64px;
            height: 100vh;
        }
        
        /* === 左侧自动模式面板 === */
        .auto-mode-sidebar {
            width: 380px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }
        
        .auto-mode-sidebar.hidden {
            transform: translateX(-100%);
            position: absolute;
            z-index: -1;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .sidebar-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 文件上传区域 */
        .upload-section {
            margin-bottom: 24px;
        }
        
        .upload-zone {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 32px 16px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .upload-zone.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .upload-text h4 {
            color: #374151;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .upload-text p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .file-types-info {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-bottom: 16px;
        }
        
        .file-type-tag {
            background: #f3f4f6;
            color: #4b5563;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .browse-files-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .browse-files-btn:hover {
            background: #2563eb;
        }
        
        /* 已选文件列表 */
        .selected-files {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .file-icon {
            font-size: 20px;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .file-details {
            font-size: 12px;
            color: #6b7280;
        }
        
        .file-remove {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }
        
        .file-remove:hover {
            background: #fee2e2;
        }
        
        /* 分析状态区域 */
        .analysis-status {
            margin-bottom: 24px;
        }
        
        .status-item {
            background: #f9fafb;
            border-left: 4px solid #e5e7eb;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
        }
        
        .status-item.active {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }
        
        .status-item.completed {
            border-left-color: #10b981;
            background: #ecfdf5;
        }
        
        .status-item.error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        
        .status-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .status-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.pending {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .status-badge.active {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .status-badge.completed {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-badge.error {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .status-description {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        
        /* 操作按钮 */
        .action-buttons {
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .start-analysis-btn {
            width: 100%;
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .start-analysis-btn:hover:not(:disabled) {
            background: #059669;
        }
        
        .start-analysis-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .clear-btn {
            width: 100%;
            background: #f3f4f6;
            color: #6b7280;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 8px;
        }
        
        .clear-btn:hover {
            background: #e5e7eb;
        }
        
        /* === 右侧对话区域 === */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            transition: margin-left 0.3s ease;
        }
        
        .chat-area.full-width {
            margin-left: -380px;
        }
        
        .chat-container {
            flex: 1;
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-height: 0;
        }
        
        /* 消息区域 */
        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px 0;
            scroll-behavior: smooth;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        /* 隐藏 WebKit 浏览器的滚动条 */
        .messages-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 消息样式 */
        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
            align-items: flex-start;
            width: 100%;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .message.assistant .message-avatar {
            background: #19c37d;
            color: white;
        }
        
        .message.user .message-avatar {
            background: #343541;
            color: white;
        }
        
        .message-content {
            flex: 1;
            min-width: 0;
        }
        
        .message-text {
            color: #374151;
            line-height: 1.6;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        
        /* 选项按钮样式 */
        .option-btn {
            padding: 8px 16px;
            background: #fff;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 4px 8px 4px 0;
            transition: all 0.2s;
        }
        
        .option-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .option-btn.selected {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }
        
        /* 用户档案表单样式 */
        .profile-form {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-top: 16px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 12px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .submit-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .submit-btn:hover {
            background: #059669;
        }
        
        /* 输入区域 */
        .input-area {
            display: flex;
            gap: 12px;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .input-container {
            flex: 1;
            position: relative;
        }
        
        .user-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            font-size: 16px;
            resize: none;
            outline: none;
            font-family: inherit;
            min-height: 48px;
            max-height: 120px;
        }
        
        .user-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .send-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            white-space: nowrap;
        }
        
        .send-btn:hover:not(:disabled) {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        /* 打字指示器 */
        .typing-indicator {
            display: none;
            padding: 16px 0;
        }
        
        .typing-content {
            display: flex;
            gap: 16px;
            align-items: center;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            position: relative;
            background-color: white;
            margin: 10vh auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .modal-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .close {
            position: absolute;
            top: 20px;
            right: 24px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #6b7280;
        }
        
        .close:hover {
            color: #374151;
        }
        
        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        /* 文件上传样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .upload-area.dragover {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .upload-area-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .upload-area-text {
            font-size: 16px;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .upload-area-hint {
            font-size: 14px;
            color: #6b7280;
        }
        
        .file-input {
            display: none;
        }
        
        .file-info {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            display: none;
        }
        
        .file-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .file-info-item:last-child {
            margin-bottom: 0;
        }
        
        .upload-button {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 16px;
        }
        
        .upload-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes typing {
            0%, 20% { opacity: 0.4; }
            50% { opacity: 1; }
            80%, 100% { opacity: 0.4; }
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .auto-mode-sidebar {
                width: 320px;
            }
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .auto-mode-sidebar {
                width: 100%;
                height: 50vh;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }
            
            .auto-mode-sidebar.hidden {
                transform: translateY(-100%);
                height: 0;
            }
            
            .chat-area.full-width {
                margin-left: 0;
            }
            
            .toolbar {
                padding: 8px 16px;
            }
            
            .modal-content {
                width: 95%;
                margin: 5vh auto;
            }
            
            .form-row {
                flex-direction: column;
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="toolbar">
        <div class="toolbar-left">
            <div class="toolbar-title">🧠 智能营养顾问</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="systemStatus">系统运行中</span>
            </div>
            <div class="model-status-display">
                <div class="model-item" id="visionModelStatus">
                    <span class="model-label">视觉模型:</span>
                    <span class="model-status loading" id="visionStatus">检查中...</span>
                </div>
                <div class="model-item" id="huatuoModelStatus">
                    <span class="model-label">分析模型:</span>
                    <span class="model-status loading" id="huatuoStatus">检查中...</span>
                </div>
            </div>
        </div>
        <div>
            <button class="toolbar-btn" id="modeToggleBtn" onclick="toggleMode()">
                <span id="modeToggleText">🤖 切换到自动模式</span>
            </button>
            <button class="toolbar-btn" onclick="clearConversation()">
                🗑️ 清空对话
            </button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧自动模式面板 -->
        <div class="auto-mode-sidebar hidden" id="autoModeSidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">🤖 智能批量分析模式</div>
                <div class="sidebar-subtitle">一次性上传所有数据，系统将自动识别文件类型并进行智能分析</div>
            </div>
            
            <div class="sidebar-content">
                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <div class="section-title">📂 文件上传</div>
                    <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">📂</div>
                        <div class="upload-text">
                            <h4>拖拽文件到此处 或 点击选择</h4>
                            <p>支持：面部图片、BIA数据、GLIM表单等</p>
                            <div class="file-types-info">
                                <span class="file-type-tag">📷 图片</span>
                                <span class="file-type-tag">📊 表格</span>
                                <span class="file-type-tag">📋 表单</span>
                                <span class="file-type-tag">📄 文档</span>
                            </div>
                        </div>
                        <button class="browse-files-btn">选择文件</button>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.xlsx,.xls,.csv,.json,.pdf,.doc,.docx" style="display: none;">
                </div>
                
                <!-- 已选文件列表 -->
                <div class="selected-files" id="selectedFiles" style="display: none;">
                    <div class="section-title">📋 已选择的文件</div>
                    <div id="filesList"></div>
                </div>
                
                <!-- 分析状态 -->
                <div class="analysis-status" id="analysisStatus" style="display: none;">
                    <div class="section-title">📊 分析进度</div>
                    
                    <div class="status-item" id="planningStatus">
                        <div class="status-header">
                            <div class="status-title">🧠 分析规划</div>
                            <div class="status-badge pending" id="planningBadge">等待中</div>
                        </div>
                        <div class="status-description" id="planningDesc">等待开始分析...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="planningProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="status-item" id="toolsStatus">
                        <div class="status-header">
                            <div class="status-title">🔧 工具执行</div>
                            <div class="status-badge pending" id="toolsBadge">等待中</div>
                        </div>
                        <div class="status-description" id="toolsDesc">等待规划完成...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="toolsProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="status-item" id="finalStatus">
                        <div class="status-header">
                            <div class="status-title">🎯 综合分析</div>
                            <div class="status-badge pending" id="finalBadge">等待中</div>
                        </div>
                        <div class="status-description" id="finalDesc">等待工具执行完成...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="finalProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="start-analysis-btn" id="startAnalysisBtn" onclick="startAnalysis()" disabled>
                    🚀 开始智能分析
                </button>
                <button class="clear-btn" onclick="clearFiles()">清空文件</button>
            </div>
        </div>
        
        <!-- 右侧对话区域 -->
        <div class="chat-area" id="chatArea">
            <div class="chat-container">
                <div class="messages-area" id="messagesArea">
                    <div class="message assistant">
                        <div class="message-avatar">AI</div>
                        <div class="message-content">
                            <div class="message-text">正在初始化智能营养顾问...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 打字指示器 -->
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-content">
                        <div class="message-avatar" style="background: #19c37d; color: white;">AI</div>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="input-area" id="inputArea">
                    <div class="input-container">
                        <textarea class="user-input" id="userInput" placeholder="请输入您的问题..." rows="1"></textarea>
                    </div>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传模态框 -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="uploadTitle">文件上传</h3>
                <span class="close" onclick="closeUploadModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('modalFileInput').click()">
                    <div class="upload-area-icon" id="uploadIcon">📁</div>
                    <div class="upload-area-text">拖拽文件到此处 或 点击选择文件</div>
                    <div class="upload-area-hint" id="uploadHint">请选择文件</div>
                </div>
                <input type="file" id="modalFileInput" class="file-input">
                <div class="file-info" id="fileInfo">
                    <div class="file-info-item">
                        <span>文件名:</span>
                        <span id="fileName"></span>
                    </div>
                    <div class="file-info-item">
                        <span>文件大小:</span>
                        <span id="fileSize"></span>
                    </div>
                </div>
                <button class="upload-button" id="uploadButton" onclick="uploadFile()" disabled>上传文件</button>
            </div>
        </div>
    </div>

    <!-- GLIM评估模态框 -->
    <div id="glimModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">GLIM营养不良评估问卷</h3>
                <span class="close" onclick="closeGlimModal()">&times;</span>
            </div>
            <div class="modal-body">
                <iframe src="/templates/glim_form.html" width="100%" height="600" frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <script>
        // === 全局变量 ===
        let conversationState = {
            messages: [],
            userProfile: {},
            currentTask: 'initializing',
            selectedFile: null,
            currentUploadType: null,
            sessionId: generateSessionId(),
            collectedData: {}
        };
        
        // 自动模式状态
        let isAutoMode = false;
        let selectedFiles = [];
        let analysisInProgress = false;
        let statusPollingInterval = null;
        
        // === 工具函数 ===
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        // 带超时的fetch包装函数 - 40分钟超时适应本地大模型推理
        async function fetchWithTimeout(url, options = {}, timeoutMs = 2400000) { // 40分钟超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

            try {
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return response;
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    throw new Error('请求超时（40分钟），本地大模型推理时间异常，请检查模型状态或稍后重试');
                }
                throw error;
            }
        }
        
        // === 页面初始化 ===
        document.addEventListener('DOMContentLoaded', function() {
            initializeChat();
            setupFileUpload();
            setupAutoResize();
            setupAutoModeEventListeners();
            
            // 初始化模型状态
            updateModelStatus();
            
            // 每10秒更新一次模型状态
            setInterval(updateModelStatus, 10000);
        });
        
        // === 模式切换 ===
        function toggleMode() {
            isAutoMode = !isAutoMode;
            const sidebar = document.getElementById('autoModeSidebar');
            const chatArea = document.getElementById('chatArea');
            const modeToggleBtn = document.getElementById('modeToggleBtn');
            const modeToggleText = document.getElementById('modeToggleText');
            const inputArea = document.getElementById('inputArea');
            
            if (isAutoMode) {
                sidebar.classList.remove('hidden');
                chatArea.classList.remove('full-width');
                modeToggleBtn.classList.add('active');
                modeToggleText.textContent = '📋 切换到手动模式';
                inputArea.style.display = 'none';
                
                // 添加欢迎消息
                addMessage('assistant', '已切换到自动分析模式。请在左侧上传需要分析的文件，系统将自动识别文件类型并进行智能分析。');
            } else {
                sidebar.classList.add('hidden');
                chatArea.classList.add('full-width');
                modeToggleBtn.classList.remove('active');
                modeToggleText.textContent = '🤖 切换到自动模式';
                inputArea.style.display = 'flex';
                
                // 清理自动模式状态
                clearFiles();
                stopStatusPolling();
                
                addMessage('assistant', '已切换到手动对话模式。您可以直接与我对话，或选择下方的选项进行营养评估。');
            }
        }
        
        // === 自动模式功能 ===
        function setupAutoModeEventListeners() {
            // 文件输入事件
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
            
            // 拖拽事件
            const uploadZone = document.getElementById('uploadZone');
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleFileDrop);
        }
        
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }
        
        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }
        
        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            addFiles(files);
        }
        
        function addFiles(files) {
            files.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });
            updateFilesList();
            updateStartButton();
        }
        
        function updateFilesList() {
            const filesList = document.getElementById('filesList');
            const selectedFilesSection = document.getElementById('selectedFiles');
            
            if (selectedFiles.length === 0) {
                selectedFilesSection.style.display = 'none';
                return;
            }
            
            selectedFilesSection.style.display = 'block';
            filesList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const fileIcon = getFileIcon(file.type, file.name);
                const fileSize = formatFileSize(file.size);
                const fileType = getFileType(file.type, file.name);
                
                fileItem.innerHTML = `
                    <div class="file-icon">${fileIcon}</div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-details">${fileType} • ${fileSize}</div>
                    </div>
                    <button class="file-remove" onclick="removeFile(${index})">✕</button>
                `;
                
                filesList.appendChild(fileItem);
            });
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFilesList();
            updateStartButton();
        }
        
        function clearFiles() {
            selectedFiles = [];
            updateFilesList();
            updateStartButton();
            hideAnalysisStatus();
        }
        
        function updateStartButton() {
            const startBtn = document.getElementById('startAnalysisBtn');
            startBtn.disabled = selectedFiles.length === 0 || analysisInProgress;
        }
        
        // 文件工具函数
        function getFileIcon(type, name) {
            if (type.startsWith('image/')) return '📷';
            if (name.endsWith('.xlsx') || name.endsWith('.xls') || name.endsWith('.csv')) return '📊';
            if (name.endsWith('.json')) return '📋';
            if (name.endsWith('.pdf')) return '📄';
            if (name.endsWith('.doc') || name.endsWith('.docx')) return '📝';
            return '📎';
        }
        
        function getFileType(type, name) {
            if (type.startsWith('image/')) return '图片文件';
            if (name.endsWith('.xlsx') || name.endsWith('.xls')) return 'Excel表格';
            if (name.endsWith('.csv')) return 'CSV数据';
            if (name.endsWith('.json')) return 'JSON数据';
            if (name.endsWith('.pdf')) return 'PDF文档';
            if (name.endsWith('.doc') || name.endsWith('.docx')) return 'Word文档';
            return '文档文件';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 分析流程
        async function startAnalysis() {
            if (selectedFiles.length === 0 || analysisInProgress) return;
            
            analysisInProgress = true;
            updateStartButton();
            showAnalysisStatus();
            
            try {
                // 显示开始分析消息
                addMessage('assistant', `开始智能分析 ${selectedFiles.length} 个文件，请稍候...`);
                
                // 生成会话ID
                const sessionId = getCurrentSessionId();
                
                // 上传文件
                const formData = new FormData();
                formData.append('session_id', sessionId);
                selectedFiles.forEach(file => {
                    formData.append('files[]', file);
                });
                
                // 更新状态：规划阶段
                updateAnalysisStatus('planning', 'active', '正在制定分析计划...', 30);
                
                const response = await fetchWithTimeout('/api/upload-batch', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`上传失败: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    // 开始轮询状态
                    startStatusPolling(sessionId);
                } else {
                    throw new Error(result.message || '分析启动失败');
                }
                
            } catch (error) {
                console.error('Analysis error:', error);
                analysisInProgress = false;
                updateStartButton();
                updateAnalysisStatus('planning', 'error', `分析失败: ${error.message}`, 0);
                addMessage('assistant', `分析过程出现错误: ${error.message}`);
            }
        }
        
        function showAnalysisStatus() {
            document.getElementById('analysisStatus').style.display = 'block';
            resetAnalysisStatus();
        }
        
        function hideAnalysisStatus() {
            document.getElementById('analysisStatus').style.display = 'none';
        }
        
        function resetAnalysisStatus() {
            updateAnalysisStatus('planning', 'pending', '等待开始分析...', 0);
            updateAnalysisStatus('tools', 'pending', '等待规划完成...', 0);
            updateAnalysisStatus('final', 'pending', '等待工具执行完成...', 0);
        }
        
        function updateAnalysisStatus(stage, status, description, progress) {
            const stageElement = document.getElementById(`${stage}Status`);
            const badgeElement = document.getElementById(`${stage}Badge`);
            const descElement = document.getElementById(`${stage}Desc`);
            const progressElement = document.getElementById(`${stage}Progress`);
            
            // 更新状态项样式
            stageElement.className = `status-item ${status}`;
            
            // 更新徽章
            badgeElement.className = `status-badge ${status}`;
            badgeElement.textContent = getStatusText(status);
            
            // 更新描述
            descElement.textContent = description;
            
            // 更新进度条
            progressElement.style.width = `${progress}%`;
        }
        
        function getStatusText(status) {
            switch (status) {
                case 'pending': return '等待中';
                case 'active': return '进行中';
                case 'completed': return '已完成';
                case 'error': return '失败';
                default: return '未知';
            }
        }
        
        // 状态轮询
        function startStatusPolling(sessionId) {
            statusPollingInterval = setInterval(async () => {
                try {
                    const response = await fetchWithTimeout(`/api/session-status/${sessionId}`);
                    const status = await response.json();
                    
                    if (status.success) {
                        updateAnalysisStatusFromServer(status.data);
                        
                        // 检查是否完成
                        if (status.data.status === 'completed' || status.data.status === 'failed') {
                            stopStatusPolling();
                            analysisInProgress = false;
                            updateStartButton();
                            
                            if (status.data.status === 'completed') {
                                addMessage('assistant', status.data.final_response || '分析完成！');
                            } else {
                                addMessage('assistant', '分析失败，请重试。');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Status polling error:', error);
                }
            }, 2000);
        }
        
        function stopStatusPolling() {
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
                statusPollingInterval = null;
            }
        }
        
        function updateAnalysisStatusFromServer(data) {
            // 根据服务器返回的状态更新UI
            if (data.current_phase === 'analysis_planning') {
                updateAnalysisStatus('planning', 'active', '正在制定分析计划...', 50);
            } else if (data.current_phase === 'batch_tools') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'active', '正在执行分析工具...', 30);
            } else if (data.current_phase === 'final_analysis') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'completed', '工具执行已完成', 100);
                updateAnalysisStatus('final', 'active', '正在进行综合分析...', 50);
            } else if (data.status === 'completed') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'completed', '工具执行已完成', 100);
                updateAnalysisStatus('final', 'completed', '综合分析已完成', 100);
            }
        }
        
        function getCurrentSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        // === 手动模式功能 ===
        
        // 初始化聊天 - 调用AI主脑
        async function initializeChat() {
            updateStatus('初始化中');
            
            // 清空初始消息
            document.getElementById('messagesArea').innerHTML = '';
            
            try {
                // 调用对话API开始会话
                const response = await fetchWithTimeout('/api/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: '', // 空消息表示开始会话
                        session_id: conversationState.sessionId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success && result.messages && result.messages.length > 0) {
                        const aiMessage = result.messages[0];
                        
                        // 处理前端动作
                        if (result.frontend_actions && result.frontend_actions.length > 0) {
                            handleFrontendAction(result.frontend_actions[0]);
                        }
                        
                        addMessage('assistant', aiMessage.content, { showProfileForm: true });
                        updateStatus('等待用户信息');
                        return;
                    }
                }
            } catch (error) {
                console.error('初始化失败:', error);
                addMessage('assistant', `初始化出现问题: ${error.message}`);
            }
            
            updateStatus('初始化完成');
        }
        
        // 添加消息
        function addMessage(sender, content, actions = null) {
            const messagesArea = document.getElementById('messagesArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = sender === 'assistant' ? 'AI' : 'You';
            
            let messageHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${content}</div>
            `;
            
            // 添加用户档案表单
            if (actions && actions.showProfileForm) {
                messageHTML += createProfileForm();
            }
            
            // 添加选项按钮
            if (actions && actions.options) {
                messageHTML += createOptionButtons(actions.options);
            }
            
            messageHTML += '</div>';
            messageDiv.innerHTML = messageHTML;
            
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        // 创建用户档案表单
        function createProfileForm() {
            return `
                <div class="profile-form">
                    <h4 style="margin-bottom: 16px; color: #1f2937;">📋 请填写基本信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-input" id="profileName" placeholder="请输入姓名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">性别</label>
                            <select class="form-input" id="profileGender">
                                <option value="">请选择</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">年龄</label>
                            <input type="number" class="form-input" id="profileAge" placeholder="请输入年龄" min="1" max="120">
                        </div>
                        <div class="form-group">
                            <label class="form-label">身高(cm)</label>
                            <input type="number" class="form-input" id="profileHeight" placeholder="请输入身高" min="100" max="250">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">体重(kg)</label>
                            <input type="number" class="form-input" id="profileWeight" placeholder="请输入体重" min="20" max="200" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">平时体重(kg)</label>
                            <input type="number" class="form-input" id="profileUsualWeight" placeholder="请输入平时体重" min="20" max="200" step="0.1">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">疾病史</label>
                        <textarea class="form-input" id="profileMedicalHistory" placeholder="请简述相关疾病史或填写'无'" rows="3"></textarea>
                    </div>
                    <button class="submit-btn" onclick="submitProfile()">提交信息</button>
                </div>
            `;
        }
        
        // 创建选项按钮
        function createOptionButtons(options) {
            let buttonsHTML = '<div style="margin-top: 16px;">';
            options.forEach(option => {
                buttonsHTML += `<button class="option-btn" onclick="selectOption('${option}')">${option}</button>`;
            });
            buttonsHTML += '</div>';
            return buttonsHTML;
        }
        
        // 提交用户档案
        async function submitProfile() {
            const profile = {
                name: document.getElementById('profileName').value,
                gender: document.getElementById('profileGender').value,
                age: parseInt(document.getElementById('profileAge').value),
                height: parseInt(document.getElementById('profileHeight').value),
                weight: parseFloat(document.getElementById('profileWeight').value),
                usualWeight: parseFloat(document.getElementById('profileUsualWeight').value),
                medicalHistory: document.getElementById('profileMedicalHistory').value
            };
            
            // 验证必填字段
            if (!profile.name || !profile.gender || !profile.age || !profile.height || !profile.weight) {
                alert('请填写所有必填信息');
                return;
            }
            
            conversationState.userProfile = profile;
            
            showTyping();
            
            try {
                const response = await fetchWithTimeout('/api/submit-profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        profile: profile,
                        session_id: conversationState.sessionId
                    })
                });
                
                hideTyping();
                
                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success && result.messages && result.messages.length > 0) {
                        const aiMessage = result.messages[0];
                        
                        // 处理前端动作
                        if (result.frontend_actions && result.frontend_actions.length > 0) {
                            handleFrontendAction(result.frontend_actions[0]);
                        }
                        
                        // 生成动态选项
                        let actions = null;
                        const hasNoAction = !result.frontend_actions || result.frontend_actions.length === 0;
                        if (hasNoAction && result.current_phase === 'data_collection') {
                            // 根据已收集数据动态生成选项
                            const availableOptions = [];
                            const collectedData = conversationState.collectedData || {};

                            if (!collectedData.glim_results) {
                                availableOptions.push('GLIM评估问卷');
                            }
                            if (!collectedData.facial_analysis) {
                                availableOptions.push('面部照片');
                            }
                            if (!collectedData.bia_analysis) {
                                availableOptions.push('BIA数据');
                            }

                            availableOptions.push('完成收集');
                            actions = { options: availableOptions };
                        }
                        
                        addMessage('assistant', aiMessage.content, actions);
                        updateStatus('等待数据选择');
                    } else {
                        addMessage('assistant', `❌ 档案处理失败：${result.error || '未知错误'}`);
                        updateStatus('处理失败');
                    }
                }
                
            } catch (error) {
                hideTyping();
                addMessage('assistant', `❌ 档案提交出错：${error.message}`);
                updateStatus('提交失败');
            }
        }
        
        // 选择选项
        async function selectOption(option) {
            // 高亮选中的按钮
            const buttons = document.querySelectorAll('.option-btn');
            buttons.forEach(btn => {
                if (btn.textContent === option) {
                    btn.classList.add('selected');
                } else {
                    btn.classList.remove('selected');
                }
            });
            
            // 添加用户消息
            addMessage('user', `选择了：${option}`);
            
            // 处理不同选项
            if (option === 'GLIM评估问卷') {
                openGlimModal();
            } else if (option === '面部照片') {
                setupUploadModal('photo');
                openUploadModal();
            } else if (option === 'BIA数据') {
                setupUploadModal('bia');
                openUploadModal();
            } else if (option === '完成收集') {
                showTyping();
                
                try {
                    const response = await fetchWithTimeout('/api/conversation', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: option,
                            session_id: conversationState.sessionId
                        })
                    });
                    
                    hideTyping();
                    
                    if (response.ok) {
                        const result = await response.json();
                        
                        if (result.success && result.messages && result.messages.length > 0) {
                            const aiMessage = result.messages[0];
                            addMessage('assistant', aiMessage.content);
                            updateStatus('分析完成');
                        }
                    }
                } catch (error) {
                    hideTyping();
                    addMessage('assistant', `❌ 处理失败：${error.message}`);
                }
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage('user', message);
            userInput.value = '';
            userInput.style.height = 'auto';
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            
            showTyping();
            
            try {
                const response = await fetchWithTimeout('/api/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: conversationState.sessionId
                    })
                });
                
                hideTyping();
                
                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success && result.messages && result.messages.length > 0) {
                        const aiMessage = result.messages[0];
                        
                        // 处理前端动作
                        if (result.frontend_actions && result.frontend_actions.length > 0) {
                            handleFrontendAction(result.frontend_actions[0]);
                        }
                        
                        // 生成动态选项
                        let actions = null;
                        const hasNoAction = !result.frontend_actions || result.frontend_actions.length === 0;
                        if (hasNoAction && result.current_phase === 'data_collection') {
                            const availableOptions = [];
                            const collectedData = conversationState.collectedData || {};

                            if (!collectedData.glim_results) {
                                availableOptions.push('GLIM评估问卷');
                            }
                            if (!collectedData.facial_analysis) {
                                availableOptions.push('面部照片');
                            }
                            if (!collectedData.bia_analysis) {
                                availableOptions.push('BIA数据');
                            }

                            availableOptions.push('完成收集');
                            actions = { options: availableOptions };
                        }
                        
                        addMessage('assistant', aiMessage.content, actions);
                    } else {
                        addMessage('assistant', '抱歉，处理您的消息时出现了错误，请重试。');
                    }
                } else {
                    addMessage('assistant', '网络连接出现问题，请稍后重试。');
                }
            } catch (error) {
                hideTyping();
                console.error('Send message error:', error);
                addMessage('assistant', `网络错误: ${error.message}`);
            } finally {
                sendBtn.disabled = false;
            }
        }
        
        // === 模态框功能 ===
        
        // 文件上传设置
        function setupUploadModal(uploadType) {
            conversationState.currentUploadType = uploadType;
            
            const titles = {
                photo: '上传面部照片',
                bia: '上传BIA数据'
            };
            
            const icons = {
                photo: '📷',
                bia: '📊'
            };
            
            const hints = {
                photo: '支持 JPG, PNG, WEBP 格式',
                bia: '支持 Excel, CSV 格式'
            };
            
            const accepts = {
                photo: 'image/*',
                bia: '.xlsx,.xls,.csv'
            };

            document.getElementById('uploadTitle').textContent = titles[uploadType] || '文件上传';
            document.getElementById('uploadIcon').textContent = icons[uploadType] || '📁';
            document.getElementById('uploadHint').textContent = hints[uploadType] || '请选择文件';
            document.getElementById('modalFileInput').accept = accepts[uploadType] || '*';
        }
        
        function openUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }
        
        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            // 重置表单
            document.getElementById('modalFileInput').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('uploadButton').disabled = true;
            conversationState.selectedFile = null;
        }
        
        function openGlimModal() {
            document.getElementById('glimModal').style.display = 'block';
        }
        
        function closeGlimModal() {
            document.getElementById('glimModal').style.display = 'none';
        }
        
        // 设置文件上传
        function setupFileUpload() {
            const fileInput = document.getElementById('modalFileInput');
            const uploadArea = document.getElementById('uploadArea');
            const fileInfo = document.getElementById('fileInfo');
            const uploadButton = document.getElementById('uploadButton');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    conversationState.selectedFile = file;
                    document.getElementById('fileName').textContent = file.name;
                    document.getElementById('fileSize').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
                    fileInfo.style.display = 'block';
                    uploadButton.disabled = false;
                }
            });

            // 拖拽功能
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => uploadArea.classList.add('dragover'), false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('dragover'), false);
            });

            uploadArea.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    conversationState.selectedFile = file;
                    document.getElementById('fileName').textContent = file.name;
                    document.getElementById('fileSize').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
                    fileInfo.style.display = 'block';
                    uploadButton.disabled = false;
                }
            });
        }
        
        // 上传文件
        async function uploadFile() {
            if (!conversationState.selectedFile || !conversationState.currentUploadType) {
                return;
            }
            
            const uploadButton = document.getElementById('uploadButton');
            uploadButton.disabled = true;
            uploadButton.textContent = '上传中...';
            
            try {
                const formData = new FormData();
                formData.append('file', conversationState.selectedFile);
                formData.append('session_id', conversationState.sessionId);
                
                const endpoint = conversationState.currentUploadType === 'photo' 
                    ? '/api/upload-image' 
                    : '/api/upload-bia';
                
                const response = await fetchWithTimeout(endpoint, {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        closeUploadModal();
                        
                        // 更新收集数据状态
                        if (conversationState.currentUploadType === 'photo') {
                            conversationState.collectedData.facial_analysis = true;
                        } else if (conversationState.currentUploadType === 'bia') {
                            conversationState.collectedData.bia_analysis = true;
                        }
                        
                        if (result.messages && result.messages.length > 0) {
                            const aiMessage = result.messages[0];
                            
                            // 生成更新后的选项
                            const availableOptions = [];
                            const collectedData = conversationState.collectedData || {};

                            if (!collectedData.glim_results) {
                                availableOptions.push('GLIM评估问卷');
                            }
                            if (!collectedData.facial_analysis) {
                                availableOptions.push('面部照片');
                            }
                            if (!collectedData.bia_analysis) {
                                availableOptions.push('BIA数据');
                            }

                            availableOptions.push('完成收集');
                            
                            addMessage('assistant', aiMessage.content, { options: availableOptions });
                        }
                    } else {
                        alert(`上传失败: ${result.error || '未知错误'}`);
                    }
                } else {
                    alert('上传失败，请重试');
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert(`上传失败: ${error.message}`);
            } finally {
                uploadButton.disabled = false;
                uploadButton.textContent = '上传文件';
            }
        }
        
        // === 辅助函数 ===
        
        function showTyping() {
            document.getElementById('typingIndicator').style.display = 'block';
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        function hideTyping() {
            document.getElementById('typingIndicator').style.display = 'none';
        }
        
        function updateStatus(status) {
            document.getElementById('systemStatus').textContent = status;
        }
        
        function setupAutoResize() {
            const userInput = document.getElementById('userInput');
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // 回车发送消息
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }
        
        function clearConversation() {
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.innerHTML = '';
            conversationState.messages = [];
            conversationState.collectedData = {};
            conversationState.sessionId = generateSessionId();
            initializeChat();
        }
        
        async function updateModelStatus() {
            try {
                const response = await fetch('/api/model-status');
                const status = await response.json();
                
                // 更新视觉模型状态
                const visionStatus = document.getElementById('visionStatus');
                if (status.vision_model && status.vision_model.status === 'loaded') {
                    visionStatus.textContent = '已就绪';
                    visionStatus.className = 'model-status ready';
                } else {
                    visionStatus.textContent = '未加载';
                    visionStatus.className = 'model-status error';
                }
                
                // 更新分析模型状态
                const huatuoStatus = document.getElementById('huatuoStatus');
                if (status.huatuogpt && status.huatuogpt.status === 'loaded') {
                    huatuoStatus.textContent = '已就绪';
                    huatuoStatus.className = 'model-status ready';
                } else {
                    huatuoStatus.textContent = '未加载';
                    huatuoStatus.className = 'model-status error';
                }
                
            } catch (error) {
                console.error('Model status check failed:', error);
                document.getElementById('visionStatus').textContent = '检查失败';
                document.getElementById('huatuoStatus').textContent = '检查失败';
            }
        }
        
        function handleFrontendAction(action) {
            if (action.type === 'redirect' && action.url) {
                window.open(action.url, '_blank');
            }
        }
        
        // 页面卸载清理
        window.addEventListener('beforeunload', function() {
            stopStatusPolling();
        });
    </script>
</body>
</html>
