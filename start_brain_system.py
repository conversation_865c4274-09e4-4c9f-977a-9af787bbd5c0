#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph + AI主脑整合版本 - 完整系统启动脚本
启动API服务器和前端界面，展示真正的智能体架构
"""
import os
import sys
import time
import threading
import webbrowser
import subprocess
import socket
from pathlib import Path
from loguru import logger
import asyncio

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.lm_studio_client import LMStudioClient
from src.agents.conversation_agent import ConversationAgent

def check_port(port):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        result = sock.connect_ex(('127.0.0.1', port))
        return result != 0

def check_lm_studio():
    """检查LM Studio是否运行"""
    try:
        import requests
        response = requests.get('http://127.0.0.1:1234/v1/models', timeout=5)
        return response.status_code == 200
    except:
        return False

def start_api_server():
    """启动API服务器"""
    print("🚀 启动LangGraph + AI主脑 API服务器...")
    try:
        # 不重定向输出，让用户能看到启动信息
        process = subprocess.Popen([
            sys.executable, 'api_server.py'
        ])
        
        # 等待服务器启动，并检查端口是否可用
        print("⏳ 等待API服务器启动...")
        for i in range(10):  # 最多等待10秒
            time.sleep(1)
            
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"❌ API服务器进程已退出，退出码: {process.poll()}")
                return None
            
            # 检查端口是否可用（服务器是否启动成功）
            try:
                import requests
                response = requests.get('http://127.0.0.1:5000/api/health', timeout=3)
                if response.status_code == 200:
                    print("✅ API服务器启动成功")
                    return process
            except:
                continue
        
        print("⚠️ API服务器启动超时，但进程仍在运行")
        return process
        
    except Exception as e:
        print(f"❌ API服务器启动异常: {e}")
        return None

def start_frontend():
    """启动前端服务器"""
    print("🌐 启动前端服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, '-m', 'http.server', '8080'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(2)
        
        if process.poll() is None:
            print("✅ 前端服务器启动成功")
            return process
        else:
            print("❌ 前端服务器启动失败")
            return None
    except Exception as e:
        print(f"❌ 前端服务器启动异常: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("🌟 等待服务就绪...")
    time.sleep(5)
    
    url = 'http://localhost:8080/templates/conversation_interface.html'
    try:
        webbrowser.open(url)
        print(f"🎉 浏览器已打开: {url}")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问: {url}")

async def test_lm_studio_connection():
    """测试LM Studio连接"""
    print("  测试LM Studio连接...")

    client = LMStudioClient()
    health = client.check_health()

    print(f"  连接状态: {'正常' if health['healthy'] else '失败'}")

    if health['healthy']:
        current_models = health.get('current_models', [])
        if current_models:
            print(f"  已加载模型: {', '.join(current_models)}")
        else:
            print("  警告: 未检测到已加载的模型")
    else:
        print(f"  错误信息: {health.get('error', '未知错误')}")
    
    return health['healthy']

async def test_ai_brain_agent():
    """测试AI主脑智能体"""
    print("  测试AI主脑智能体...")
    
    try:
        agent = ConversationAgent()
        
        # 创建初始会话
        state = agent.create_initial_state("test_session")
        print(f"  初始状态创建完成，会话ID: {state['brain_context']['session_id']}")
        
        # 运行初始对话
        state = await agent.process_conversation_turn(state)
        
        # 显示AI回复
        for msg in state['messages']:
            if msg['role'] == 'assistant':
                print(f"  AI主脑: {msg['content'][:100]}{'...' if len(msg['content']) > 100 else ''}")
                break
        
        print(f"  当前状态: {state['current_phase']}")
        
        return True
    except Exception as e:
        print(f"  AI主脑测试失败: {e}")
        return False

async def test_user_profile_submission():
    """测试用户档案提交"""
    print("  测试用户档案提交...")
    
    try:
        agent = ConversationAgent()
        state = agent.create_initial_state("test_profile")
        
        # 模拟用户提交档案
        profile_data = {
            "name": "张先生",
            "age": 45,
            "gender": "男",
            "height": 175,
            "current_weight": 65,
            "usual_weight": 70
        }
        
        # 添加档案数据
        message_data = {
            'data_type': 'profile',
            'data': profile_data
        }
        
        state = await agent.add_user_input(
            state, 
            "✅ 基本信息已提交", 
            message_data
        )
        
        # 处理对话
        state = await agent.process_conversation_turn(state)
        
        print("  档案数据提交测试完成")
        
        return True
    except Exception as e:
        print(f"  档案提交测试失败: {e}")
        return False

async def test_comprehensive_workflow():
    """测试完整工作流程"""
    print("  测试完整工作流程...")
    
    try:
        agent = ConversationAgent()
        state = agent.create_initial_state("test_workflow")
        
        # Step 1: 初始对话
        state = await agent.process_conversation_turn(state)
        print(f"  状态: {state['current_phase']}")
        
        # Step 2: 提交档案
        profile_data = {
            "name": "测试用户",
            "age": 35,
            "gender": "女",
            "height": 160,
            "current_weight": 50,
            "usual_weight": 55
        }
        
        state = await agent.add_user_input(
            state, 
            "提交档案", 
            {'data_type': 'profile', 'data': profile_data}
        )
        state = await agent.process_conversation_turn(state)
        print(f"  状态: {state['current_phase']}")
        
        # Step 3: 完成收集
        state = await agent.add_user_input(state, "完成收集")
        state = await agent.process_conversation_turn(state)
        print(f"  状态: {state['current_phase']}")
        
        print("  完整工作流程测试完成")
        
        return True
    except Exception as e:
        print(f"  工作流程测试失败: {e}")
        return False

def run_integration_test():
    """运行系统集成测试"""
    print("🧪 运行系统集成测试...")
    
    async def run_tests():
        results = []
        
        # 测试LM Studio连接
        try:
            lm_result = await test_lm_studio_connection()
            results.append(("LM Studio连接", lm_result))
        except Exception as e:
            print(f"  LM Studio测试异常: {e}")
            results.append(("LM Studio连接", False))
        
        # 测试AI主脑智能体
        try:
            brain_result = await test_ai_brain_agent()
            results.append(("AI主脑智能体", brain_result))
        except Exception as e:
            print(f"  AI主脑测试异常: {e}")
            results.append(("AI主脑智能体", False))
        
        # 测试用户档案提交
        try:
            profile_result = await test_user_profile_submission()
            results.append(("用户档案提交", profile_result))
        except Exception as e:
            print(f"  档案提交测试异常: {e}")
            results.append(("用户档案提交", False))
        
        # 测试完整工作流程
        try:
            workflow_result = await test_comprehensive_workflow()
            results.append(("完整工作流程", workflow_result))
        except Exception as e:
            print(f"  工作流程测试异常: {e}")
            results.append(("完整工作流程", False))
        
        return results
    
    try:
        # 运行异步测试
        results = asyncio.run(run_tests())
        
        # 统计结果
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        if passed == total:
            print("✅ 系统集成测试全部通过")
            return True
        else:
            print("⚠️ 系统集成测试部分失败，但系统仍可使用")
            return False
            
    except Exception as e:
        print(f"⚠️ 集成测试异常: {e}")
        return False

def show_system_status():
    """显示系统状态"""
    print("\n" + "=" * 80)
    print("🧠 LangGraph + AI主脑整合版本 - 系统启动完成")
    print("=" * 80)
    
    # 检查LM Studio
    lm_status = "🟢 运行中" if check_lm_studio() else "🔴 未连接"
    print(f"🤖 LM Studio状态: {lm_status}")
    
    # 检查端口
    api_status = "🟢 运行中" if not check_port(5000) else "🔴 未启动"
    frontend_status = "🟢 运行中" if not check_port(8080) else "🔴 未启动"
    
    print(f"🔗 API服务器 (端口5000): {api_status}")
    print(f"🌐 前端服务器 (端口8080): {frontend_status}")
    
    print("\n🎯 访问地址:")
    print("   • 对话界面: http://localhost:8080/templates/conversation_interface.html")
    print("   • API健康检查: http://localhost:5000/api/health")
    print("   • 日志查看器: 通过对话界面的日志按钮访问")
    
    print("\n🧠 核心架构特色:")
    print("   • AI主脑控制: 华佗GPT通过系统提示词智能决策")
    print("   • LangGraph状态管理: 专业的状态机和工作流引擎")
    print("   • 真实模型调用: 本地部署的华佗GPT + 视觉分析模型")
    print("   • 统一对话接口: 前端简化，后端智能化")
    print("   • 完整日志系统: 所有AI交互完全透明")
    
    print("\n🎨 用户体验:")
    print("   • ChatGPT级别界面: 灰白扁平化设计")
    print("   • 智能引导对话: AI主动推荐最适合的评估路径")
    print("   • 多模态分析: 支持图像、问卷、体成分数据")
    print("   • 个性化报告: 基于用户特征的专业营养建议")
    
    print("\n💡 推荐使用的模型:")
    print("   • 主脑模型: freedomintelligence.huatuogpt-o1-7b")
    print("   • 视觉模型: mimo-vl-7b-rl (思考型，需较长处理时间)")
    
    print("\n📊 技术指标:")
    print("   • 对话延迟: 通常 2-5秒 (视模型性能而定)")
    print("   • 图像分析: 60-90秒 (MIMO-VL思考模型)")
    print("   • 综合分析: 30-60秒 (华佗GPT综合推理)")
    print("   • 内存占用: ~2-4GB (取决于加载的模型)")
    
    print("\n⚠️ 重要说明:")
    print("   • 这是医疗AI辅助工具，用于初步筛查")
    print("   • 所有结果仅供参考，不能替代医生诊断")
    print("   • 建议将AI报告用于医生咨询参考")
    
    print("\n🛑 停止系统:")
    print("   按 Ctrl+C 停止所有服务")
    print("=" * 80)

def main():
    """主函数"""
    print("🚀 LangGraph + AI主脑整合版本 - 完整系统启动")
    print("智能体驱动的医疗营养筛查平台")
    print("=" * 80)
    
    # 检查依赖文件
    print("🔍 检查系统依赖...")
    
    required_files = [
        'api_server.py',
        'src/agents/conversation_agent.py',
        'templates/conversation_interface.html',
        'templates/enhanced_log_viewer.html',
        'src/core/lm_studio_client.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {', '.join(missing_files)}")
        return
    
    print("✅ 文件检查通过")
    
    # 检查端口
    if not check_port(5000):
        print("❌ 端口5000已被占用，请释放后重试")
        return
    
    if not check_port(8080):
        print("❌ 端口8080已被占用，请释放后重试") 
        return
    
    print("✅ 端口检查通过")
    
    # 检查LM Studio
    print("🔍 检查LM Studio连接...")
    lm_connected = check_lm_studio()
    if lm_connected:
        print("✅ LM Studio连接正常")
    else:
        print("⚠️ LM Studio未连接，请确认:")
        print("   1. LM Studio已启动")
        print("   2. 已加载华佗GPT模型 (freedomintelligence.huatuogpt-o1-7b)")
        print("   3. 已加载视觉模型 (mimo-vl-7b-rl)")
        print("   4. 服务器运行在127.0.0.1:1234")
        print("\n系统可以启动，但AI功能可能不可用")
    
    # 运行集成测试
    print("\n🧪 运行预启动检查...")
    test_passed = run_integration_test()
    
    print("\n🚀 启动系统组件...")
    
    # 启动API服务器
    api_process = start_api_server()
    if not api_process:
        print("❌ 系统启动失败")
        return
    
    # 启动前端服务器
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 系统启动失败")
        if api_process:
            api_process.terminate()
        return
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 显示系统状态
    show_system_status()
    
    try:
        # 等待进程结束或用户中断
        while True:
            if api_process.poll() is not None:
                print("\n❌ API服务器已停止")
                break
            if frontend_process.poll() is not None:
                print("\n❌ 前端服务器已停止")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止系统...")
        
        # 终止所有进程
        processes = [api_process, frontend_process]
        for process in processes:
            if process and process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except:
                    process.kill()
        
        print("✅ 系统已完全停止")
        print("感谢使用 LangGraph + AI主脑整合版本！")
    except Exception as e:
        print(f"\n❌ 系统运行时错误: {e}")
        # 清理进程
        for process in [api_process, frontend_process]:
            if process and process.poll() is None:
                process.terminate()

if __name__ == "__main__":
    main()
