#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话式营养筛查智能体 - API服务器 (LangGraph + AI主脑整合版)
处理前端请求，调用LangGraph智能体进行真实分析
"""
import os
import sys
import json
import base64
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional
import tempfile
from pathlib import Path
from functools import wraps

from flask import Flask, request, jsonify, send_from_directory, Response, stream_template
from flask_cors import CORS
from werkzeug.utils import secure_filename
import pandas as pd
import queue
import time
from threading import Lock
from loguru import logger

# 全局模型状态管理
class ModelStatusManager:
    def __init__(self):
        self.current_status = {
            'loaded_models': [],
            'vision_loaded': False,
            'huatuo_loaded': False,
            'last_update': None
        }
        self.status_lock = Lock()
        self.event_queues = []  # 存储客户端事件队列
        
    def update_status(self, loaded_models=None, force_check=False):
        """更新模型状态并通知所有客户端"""
        with self.status_lock:
            if loaded_models is None and not force_check:
                # 如果没有提供状态且不强制检查，则返回当前状态
                return self.current_status
                
            # 检查当前加载的模型
            if loaded_models is None:
                loaded_models = self._check_loaded_models()
            
            # 更新状态
            old_status = self.current_status.copy()
            self.current_status['loaded_models'] = loaded_models
            self.current_status['last_update'] = datetime.now().isoformat()
            
            # 检查具体模型状态
            from config.settings import settings
            vision_model = settings.VLM_MODEL_ID
            huatuo_model = settings.HUATUO_MODEL_ID
            
            vision_loaded = any(vision_model.split('/')[-1] in model for model in loaded_models if model)
            huatuo_loaded = any(huatuo_model.split('/')[-1] in model for model in loaded_models if model)
            
            self.current_status['vision_loaded'] = vision_loaded
            self.current_status['huatuo_loaded'] = huatuo_loaded
            
            # 如果状态发生变化，通知所有客户端
            status_changed = (
                old_status['vision_loaded'] != vision_loaded or
                old_status['huatuo_loaded'] != huatuo_loaded or
                set(old_status['loaded_models']) != set(loaded_models)
            )
            
            if status_changed:
                logger.info(f"🔄 模型状态变化: 视觉模型={vision_loaded}, 华佗模型={huatuo_loaded}")
                self._notify_clients()
                
        return self.current_status
    
    def _check_loaded_models(self):
        """检查当前加载的模型"""
        try:
            import subprocess
            result = subprocess.run(
                ["lms", "ps"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                loaded_models = []
                output = result.stdout or ""
                
                if "No models are currently loaded" not in output:
                    lines = output.strip().split('\n') if output else []
                    for line in lines:
                        line = line.strip()
                        if line.startswith('Identifier:'):
                            model_name = line.replace('Identifier:', '').strip()
                            if model_name:
                                loaded_models.append(model_name)
                
                return loaded_models
        except Exception as e:
            logger.error(f"检查模型状态失败: {e}")
        
        return []
    
    def _notify_clients(self):
        """通知所有连接的客户端"""
        if not self.event_queues:
            return
            
        event_data = json.dumps({
            'type': 'model_status_update',
            'data': self.current_status
        })
        
        # 清理断开的连接
        active_queues = []
        for q in self.event_queues:
            try:
                q.put_nowait(event_data)
                active_queues.append(q)
            except queue.Full:
                # 队列满了，说明客户端可能断开了
                pass
        
        self.event_queues = active_queues
        logger.info(f"📢 通知 {len(active_queues)} 个客户端模型状态更新")
    
    def add_client(self, client_queue):
        """添加客户端事件队列"""
        self.event_queues.append(client_queue)
        # 立即发送当前状态
        try:
            current_data = json.dumps({
                'type': 'model_status_update',
                'data': self.current_status
            })
            client_queue.put_nowait(current_data)
        except queue.Full:
            pass

# 全局模型状态管理器实例
model_status_manager = ModelStatusManager()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.lm_studio_client import LMStudioClient
from src.agents.conversation_agent import ConversationAgent

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

# 配置超时时间为10分钟
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 600
app.config['PERMANENT_SESSION_LIFETIME'] = 600

# 初始化组件
lm_client = LMStudioClient()
conversation_agent = ConversationAgent()

# 会话管理存储
session_states = {}  # 存储每个会话的状态
session_logs = {}    # 存储会话日志

# 简化的异步包装器
def run_async(coro):
    """在当前线程中运行异步函数"""
    import asyncio
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    except Exception as e:
        logger.error(f"异步执行错误: {e}")
        raise e

# 配置上传
UPLOAD_FOLDER = tempfile.gettempdir()
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def log_api_call(session_id: str, api_name: str, request_data: Any, response_data: Any, success: bool):
    """记录API调用日志"""
    if session_id not in session_logs:
        session_logs[session_id] = []
    
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'type': 'api_call',
        'api': api_name,
        'request': request_data,
        'response': response_data,
        'success': success,
        'session_id': session_id
    }
    
    session_logs[session_id].append(log_entry)
    logger.info(f"API调用记录: {api_name} - {'成功' if success else '失败'}")

def get_or_create_session_state(session_id: str):
    """获取或创建会话状态"""
    if session_id not in session_states:
        logger.info(f"🆕 创建新会话: {session_id}")
        session_states[session_id] = conversation_agent.create_initial_state(session_id)
        logger.info(f"✅ 会话 {session_id} 初始化完成（包含模型清理）")
    return session_states[session_id]

@app.route('/')
def index():
    """主页重定向到对话界面"""
    return send_from_directory('templates', 'conversation_interface.html')

@app.route('/templates/<path:filename>')
def serve_templates(filename):
    """提供模板文件"""
    return send_from_directory('templates', filename)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        # 检查LM Studio连接
        lm_health = lm_client.check_health()
        
        response = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'lm_studio': lm_health,
            'conversation_agent': True,
            'active_sessions': len(session_states)
        }
        
        return jsonify(response)

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# 全局日志存储
enhanced_logs = []
MAX_LOGS = 1000  # 最大保存日志数量

def add_enhanced_log(log_type, event, **kwargs):
    """添加增强日志条目"""
    global enhanced_logs

    log_entry = {
        'type': log_type,
        'event': event,
        'timestamp': datetime.now().isoformat(),
        **kwargs
    }

    enhanced_logs.append(log_entry)

    # 保持日志数量在限制内
    if len(enhanced_logs) > MAX_LOGS:
        enhanced_logs = enhanced_logs[-MAX_LOGS:]

@app.route('/api/logs/enhanced', methods=['GET'])
def get_enhanced_logs():
    """获取增强版日志"""
    try:
        return jsonify({
            'success': True,
            'logs': enhanced_logs,
            'total': len(enhanced_logs),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取增强日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'logs': [],
            'total': 0
        }), 500

@app.route('/api/logs/enhanced/clear', methods=['POST'])
def clear_enhanced_logs():
    """清空增强版日志"""
    global enhanced_logs
    try:
        enhanced_logs = []
        return jsonify({
            'success': True,
            'message': '日志已清空',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"清空增强日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 会话管理API
@app.route('/api/sessions/save', methods=['POST'])
def save_session():
    """保存会话数据"""
    try:
        data = request.get_json()
        session_id = data.get('sessionId')

        if not session_id:
            return jsonify({'success': False, 'error': '缺少会话ID'}), 400

        # 创建会话存储目录
        sessions_dir = os.path.join(os.getcwd(), 'sessions')
        os.makedirs(sessions_dir, exist_ok=True)

        # 保存会话数据到文件
        session_file = os.path.join(sessions_dir, f'{session_id}.json')
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.info(f"会话已保存: {session_id}")
        return jsonify({'success': True, 'message': '会话保存成功'})

    except Exception as e:
        logger.error(f"保存会话失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sessions/list', methods=['GET'])
def list_sessions():
    """获取会话列表"""
    try:
        sessions_dir = os.path.join(os.getcwd(), 'sessions')

        if not os.path.exists(sessions_dir):
            return jsonify({'success': True, 'sessions': []})

        sessions = []
        for filename in os.listdir(sessions_dir):
            if filename.endswith('.json'):
                session_file = os.path.join(sessions_dir, filename)
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)

                    # 生成预览文本
                    preview = generate_session_preview(session_data)

                    sessions.append({
                        'sessionId': session_data.get('sessionId'),
                        'title': session_data.get('title', '未命名会话'),
                        'timestamp': session_data.get('timestamp'),
                        'preview': preview,
                        'messageCount': len(session_data.get('messages', []))
                    })
                except Exception as e:
                    logger.warning(f"读取会话文件失败 {filename}: {e}")
                    continue

        # 按时间戳排序（最新的在前）
        def get_sort_timestamp(session):
            timestamp = session.get('timestamp', 0)
            # 如果是字符串，尝试转换为浮点数
            if isinstance(timestamp, str):
                try:
                    # 先尝试直接转换为浮点数
                    return float(timestamp)
                except (ValueError, TypeError):
                    try:
                        # 尝试解析ISO格式时间戳
                        from datetime import datetime
                        # 处理不同的ISO格式
                        if timestamp.endswith('Z'):
                            timestamp = timestamp[:-1] + '+00:00'
                        dt = datetime.fromisoformat(timestamp)
                        return dt.timestamp()
                    except (ValueError, TypeError):
                        return 0
            # 如果是数字，直接返回
            elif isinstance(timestamp, (int, float)):
                return float(timestamp)
            else:
                return 0

        sessions.sort(key=get_sort_timestamp, reverse=True)

        return jsonify({'success': True, 'sessions': sessions})

    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sessions/load/<session_id>', methods=['GET'])
def load_session(session_id):
    """加载指定会话"""
    try:
        sessions_dir = os.path.join(os.getcwd(), 'sessions')
        session_file = os.path.join(sessions_dir, f'{session_id}.json')

        if not os.path.exists(session_file):
            return jsonify({'success': False, 'error': '会话不存在'}), 404

        with open(session_file, 'r', encoding='utf-8') as f:
            session_data = json.load(f)

        logger.info(f"会话已加载: {session_id}")
        return jsonify({'success': True, 'session': session_data})

    except Exception as e:
        logger.error(f"加载会话失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sessions/delete/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    """删除指定会话"""
    try:
        sessions_dir = os.path.join(os.getcwd(), 'sessions')
        session_file = os.path.join(sessions_dir, f'{session_id}.json')

        if not os.path.exists(session_file):
            return jsonify({'success': False, 'error': '会话不存在'}), 404

        os.remove(session_file)

        logger.info(f"会话已删除: {session_id}")
        return jsonify({'success': True, 'message': '会话删除成功'})

    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def generate_session_preview(session_data):
    """生成会话预览文本"""
    try:
        messages = session_data.get('messages', [])
        user_profile = session_data.get('userProfile', {})

        # 基本信息
        name = user_profile.get('name', '未知用户')
        age = user_profile.get('age', '')
        gender = user_profile.get('gender', '')

        preview_parts = []

        # 用户信息
        if name != '未知用户':
            user_info = f"{name}"
            if age:
                user_info += f", {age}岁"
            if gender:
                user_info += f", {gender}"
            preview_parts.append(user_info)

        # 消息摘要
        user_messages = [msg for msg in messages if msg.get('role') == 'user']
        if user_messages:
            last_user_msg = user_messages[-1].get('content', '')
            if len(last_user_msg) > 50:
                last_user_msg = last_user_msg[:50] + '...'
            preview_parts.append(f"最后消息: {last_user_msg}")

        return ' | '.join(preview_parts) if preview_parts else '空会话'

    except Exception as e:
        return f"预览生成失败: {str(e)}"

@app.route('/api/model-status', methods=['GET'])
def get_model_status():
    """获取当前模型状态（优化版）"""
    try:
        # 使用模型状态管理器获取状态
        status = model_status_manager.update_status(force_check=True)
        
        from config.settings import settings
        return jsonify({
            'success': True,
            'loaded_models': status['loaded_models'],
            'vision_loaded': status['vision_loaded'],
            'huatuo_loaded': status['huatuo_loaded'],
            'vision_model': settings.VLM_MODEL_ID,
            'huatuo_model': settings.HUATUO_MODEL_ID,
            'timestamp': status['last_update'] or datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取模型状态失败: {e}")
        error_details = traceback.format_exc()
        logger.error(f"详细错误信息: {error_details}")
        
        from config.settings import settings
        return jsonify({
            'success': False,
            'error': str(e),
            'loaded_models': [],
            'vision_loaded': False,
            'huatuo_loaded': False,
            'vision_model': settings.VLM_MODEL_ID,
            'huatuo_model': settings.HUATUO_MODEL_ID,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/model-status-stream')
def model_status_stream():
    """服务端推送事件流 - 实时模型状态更新"""
    def event_stream():
        client_queue = queue.Queue(maxsize=10)
        model_status_manager.add_client(client_queue)
        
        try:
            while True:
                try:
                    # 等待事件，超时30秒发送心跳
                    event_data = client_queue.get(timeout=30)
                    yield f"data: {event_data}\n\n"
                except queue.Empty:
                    # 发送心跳保持连接
                    yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': datetime.now().isoformat()})}\n\n"
        except GeneratorExit:
            # 客户端断开连接
            if client_queue in model_status_manager.event_queues:
                model_status_manager.event_queues.remove(client_queue)
    
    return Response(event_stream(), mimetype='text/event-stream', headers={
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
    })

@app.route('/api/conversation', methods=['POST'])
def conversation_step():
    """统一的对话接口 - 核心API"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '缺少请求数据'}), 400
        
        session_id = data.get('session_id', f"session_{datetime.now().timestamp()}")
        message = data.get('message', '')
        user_data = data.get('data', {})
        additional_data = data.get('additional_data', {})

        logger.info(f"收到对话请求，会话: {session_id}, 消息长度: {len(message)}")
        logger.info(f"用户消息内容: '{message}'")
        if additional_data:
            logger.info(f"额外数据: {additional_data.get('type', 'unknown')}")

        # 获取或创建会话状态
        state = get_or_create_session_state(session_id)

        # 添加用户输入到状态
        if message or user_data:
            state = run_async(conversation_agent.add_user_input(state, message, user_data))

        # 处理额外数据（如GLIM、BIA、照片完成数据）
        if additional_data and additional_data.get('type') == 'glim_completion':
            glim_results = additional_data.get('glim_results', {})
            if glim_results:
                # 保存GLIM数据到状态
                if 'collected_data' not in state:
                    state['collected_data'] = {}
                state['collected_data']['glim_results'] = glim_results
                logger.info("GLIM评估数据已保存到会话状态")

        elif additional_data and additional_data.get('type') == 'bia_completion':
            bia_results = additional_data.get('bia_results', {})
            if bia_results:
                # 保存BIA数据到状态
                if 'collected_data' not in state:
                    state['collected_data'] = {}
                state['collected_data']['bia_analysis'] = bia_results
                logger.info("BIA分析数据已保存到会话状态")

        elif additional_data and additional_data.get('type') == 'photo_completion':
            photo_results = additional_data.get('photo_results', {})
            if photo_results:
                # 保存照片分析数据到状态
                if 'collected_data' not in state:
                    state['collected_data'] = {}
                state['collected_data']['facial_analysis'] = photo_results
                logger.info("面部照片分析数据已保存到会话状态")
        
        # 运行AI主脑决策
        state = run_async(conversation_agent.process_conversation_turn(state))
        
        # 更新会话状态
        session_states[session_id] = state
        
        # 提取响应信息
        assistant_messages = []
        frontend_actions = []
        
        # 获取最新的助手消息
        for msg in reversed(state['messages']):
            if msg['role'] == 'assistant':
                assistant_messages.append(msg)
                break
        
        # 获取前端动作
        frontend_actions = state.get('frontend_actions', [])
        
        # 构建响应
        response = {
            'success': True,
            'session_id': session_id,
            'messages': assistant_messages,
            'frontend_actions': frontend_actions,
            'current_phase': state.get('current_phase'),
            'waiting_for': state.get('waiting_for'),
            'collected_data': state.get('collected_data', {}),
            'is_completed': state.get('current_phase') == 'completed',
            'timestamp': datetime.now().isoformat()
        }
        
        # 记录日志
        log_api_call(session_id, 'conversation', {
            'message_length': len(message),
            'has_data': bool(user_data),
            'current_phase': state.get('current_phase')
        }, {
            'response_messages': len(assistant_messages),
            'frontend_actions': len(frontend_actions),
            'phase': state.get('current_phase')
        }, True)
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"对话处理异常: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'对话处理失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/upload-image', methods=['POST'])
def upload_image():
    """处理图像上传"""
    try:
        session_id = request.form.get('session_id', 'unknown')
        
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '文件名为空'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        logger.info(f"收到图像上传请求，会话: {session_id}，文件: {file.filename}")
        
        # 将图像转换为base64
        image_data = file.read()
        base64_image = base64.b64encode(image_data).decode('utf-8')
        
        # 获取会话状态
        state = get_or_create_session_state(session_id)
        
        # 添加图像数据到状态
        message_data = {
            'data_type': 'photo',
            'data': {
                'filename': file.filename,
                'image_base64': base64_image,
                'size': len(image_data)
            }
        }
        
        state = run_async(conversation_agent.add_user_input(
            state, 
            f"📎 已上传照片：{file.filename}",
            message_data
        ))
        
        # 触发视觉分析
        state = run_async(conversation_agent.call_vision_analysis_node(state))
        
        # 继续对话流程
        state = run_async(conversation_agent.process_conversation_turn(state))
        
        # 更新会话状态
        session_states[session_id] = state
        
        # 构建响应
        facial_analysis = state.get('collected_data', {}).get('facial_analysis')
        
        if facial_analysis and 'error' not in facial_analysis:
            analysis_text = facial_analysis.get('analysis', '分析完成')
            # 确保返回的是格式化后的结果，而不是原始JSON
            if isinstance(analysis_text, str) and analysis_text.strip().startswith('{'):
                # 如果仍然是JSON格式，说明格式化失败，返回简化结果
                response = {
                    'success': True,
                    'analysis': '✅ 面部照片分析完成！\n\n⚠️ 分析结果正在处理中，请稍后查看完整报告。',
                    'session_id': session_id,
                    'timestamp': datetime.now().isoformat()
                }
                logger.warning("⚠️ 视觉分析返回的仍是JSON格式，可能格式化未完成")
            else:
                response = {
                    'success': True,
                    'analysis': analysis_text,
                    'session_id': session_id,
                    'timestamp': datetime.now().isoformat()
                }
        else:
            error_msg = facial_analysis.get('error', '未知错误') if facial_analysis else '分析失败'
            response = {
                'success': False,
                'error': error_msg,
                'session_id': session_id
            }
        
        log_api_call(session_id, 'upload-image', {
            'filename': file.filename,
            'size': len(image_data)
        }, {
            'analysis_success': response['success']
        }, response['success'])
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"图像上传异常: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'图像处理失败: {str(e)}'
        }), 500

@app.route('/api/upload-bia', methods=['POST'])
def upload_bia():
    """处理BIA数据上传"""
    try:
        session_id = request.form.get('session_id', 'unknown')
        
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '文件名为空'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        logger.info(f"收到BIA数据上传请求，会话: {session_id}，文件: {file.filename}")
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        filepath = os.path.join(UPLOAD_FOLDER, f"{session_id}_{filename}")
        file.save(filepath)
        
        try:
            # 获取会话状态
            state = get_or_create_session_state(session_id)
            
            # 添加BIA数据到状态
            message_data = {
                'data_type': 'bia',
                'data': {
                    'filename': filename,
                    'file_path': filepath
                }
            }
            
            state = run_async(conversation_agent.add_user_input(
                state,
                f"📊 已上传BIA数据：{filename}",
                message_data
            ))
            
            # 触发BIA分析
            state = run_async(conversation_agent.call_bia_analysis_node(state))

            # 分析完成后再清理文件
            if os.path.exists(filepath):
                os.remove(filepath)
                logger.info(f"已清理临时文件: {filepath}")

            # 继续对话流程
            state = run_async(conversation_agent.process_conversation_turn(state))

            # 更新会话状态
            session_states[session_id] = state

            # 构建响应
            bia_analysis = state.get('collected_data', {}).get('bia_analysis')

            logger.info(f"🔍 BIA分析状态检查: bia_analysis = {bia_analysis}")
            logger.info(f"🔍 collected_data = {state.get('collected_data', {})}")

            if bia_analysis and 'error' not in bia_analysis:
                analysis_data = bia_analysis.get('analysis', {})

                # 在日志中显示真实的BIA数据提取结果
                logger.info("=== BIA数据提取结果 ===")
                logger.info(f"BIA分析数据: {analysis_data}")

                # 转换numpy数据类型为JSON可序列化的格式
                def convert_to_json_serializable(obj):
                    """递归转换numpy数据类型为Python原生类型"""
                    try:
                        import numpy as np
                        import pandas as pd

                        if isinstance(obj, dict):
                            return {k: convert_to_json_serializable(v) for k, v in obj.items()}
                        elif isinstance(obj, list):
                            return [convert_to_json_serializable(item) for item in obj]
                        elif isinstance(obj, (np.integer, np.int64, np.int32, np.int16, np.int8)):
                            return int(obj)
                        elif isinstance(obj, (np.floating, np.float64, np.float32, np.float16)):
                            return float(obj)
                        elif isinstance(obj, np.ndarray):
                            return obj.tolist()
                        elif isinstance(obj, (pd.Series, pd.DataFrame)):
                            return obj.to_dict()
                        elif hasattr(obj, 'item'):  # numpy标量
                            return obj.item()
                        else:
                            return obj
                    except Exception as e:
                        logger.warning(f"转换数据类型时出错: {e}, 对象类型: {type(obj)}")
                        return str(obj)  # 降级为字符串

                # 转换分析数据
                logger.info("开始转换BIA分析数据为JSON可序列化格式")
                serializable_data = convert_to_json_serializable(analysis_data)
                logger.info("BIA数据转换完成")

                response = {
                    'success': True,
                    'analysis': "✅ BIA数据分析完成！",  # 简化回复，不显示风险等级
                    'detailed_data': serializable_data,
                    'session_id': session_id,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                error_msg = bia_analysis.get('error', '分析失败') if bia_analysis else '未知错误'
                response = {
                    'success': False,
                    'error': error_msg,
                    'session_id': session_id
                }

            log_api_call(session_id, 'upload-bia', {
                'filename': filename,
                'filepath': filepath
            }, {
                'analysis_success': response['success']
            }, response['success'])

            return jsonify(response)

        except Exception as analysis_error:
            # 如果分析过程出错，确保清理文件
            if os.path.exists(filepath):
                os.remove(filepath)
                logger.info(f"分析出错，已清理临时文件: {filepath}")
            raise analysis_error
                
    except Exception as e:
        logger.error(f"BIA上传异常: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'BIA数据处理失败: {str(e)}'
        }), 500

# === 自动模式API（新增） ===

@app.route('/api/upload-batch', methods=['POST'])
def upload_batch():
    """批量文件上传接口（自动模式）"""
    logger.info("📤 收到批量文件上传请求")
    
    try:
        # 获取会话ID
        session_id = request.form.get('session_id')
        if not session_id:
            return jsonify({'success': False, 'error': '缺少session_id参数'}), 400
        
        # 获取上传的文件列表
        files = request.files.getlist('files[]')
        if not files or len(files) == 0:
            return jsonify({'success': False, 'error': '未选择任何文件'}), 400
        
        # 验证文件数量限制
        from config.settings import settings
        batch_config = settings.get_batch_upload_config()
        if len(files) > batch_config['max_files']:
            return jsonify({
                'success': False, 
                'error': f'最多只能同时上传{batch_config["max_files"]}个文件'
            }), 400
        
        # 验证文件
        total_size = 0
        validated_files = []
        
        for file in files:
            if file.filename == '':
                continue
                
            # 检查文件类型
            if not settings.is_supported_file(file.filename):
                return jsonify({
                    'success': False,
                    'error': f'不支持的文件类型: {file.filename}'
                }), 400
            
            # 检查文件大小
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)     # 重置到文件开始
            
            if file_size > settings.get_file_size_limit_mb() * 1024 * 1024:
                return jsonify({
                    'success': False,
                    'error': f'文件{file.filename}大小超过限制({settings.get_file_size_limit_mb()}MB)'
                }), 400
            
            total_size += file_size
            validated_files.append({
                'file': file,
                'size': file_size,
                'filename': secure_filename(file.filename)
            })
        
        # 检查总大小
        if total_size > batch_config['allowed_total_size_mb'] * 1024 * 1024:
            return jsonify({
                'success': False,
                'error': f'文件总大小超过限制({batch_config["allowed_total_size_mb"]}MB)'
            }), 400
        
        # 创建临时存储目录
        import tempfile
        import uuid
        session_temp_dir = os.path.join(settings.TEMP_DIR, f"session_{session_id}")
        os.makedirs(session_temp_dir, exist_ok=True)
        
        # 保存文件并构建上传队列
        upload_items = []
        
        for file_info in validated_files:
            file = file_info['file']
            filename = file_info['filename']
            
            # 生成唯一文件ID
            file_id = str(uuid.uuid4())
            safe_filename = f"{file_id}_{filename}"
            file_path = os.path.join(session_temp_dir, safe_filename)
            
            # 保存文件
            file.save(file_path)
            
            # 读取文件字节用于检测
            with open(file_path, 'rb') as f:
                file_bytes = f.read()
            
            upload_items.append({
                'id': file_id,
                'filename': filename,
                'file_path': file_path,
                'file_bytes': file_bytes,
                'mimetype': file.mimetype or 'application/octet-stream',
                'size': file_info['size'],
                'uploaded_at': datetime.now().isoformat()
            })
        
        logger.info(f"✅ 批量上传成功，共{len(upload_items)}个文件")
        
        # 获取或初始化会话状态
        if session_id not in session_states:
            session_states[session_id] = conversation_agent.create_initial_state(session_id)
        
        state = session_states[session_id]
        
        # 添加到上传队列
        if 'uploads_queue' not in state:
            state['uploads_queue'] = []
        state['uploads_queue'].extend(upload_items)
        
        # 设置当前阶段为数据摄取
        state['current_phase'] = 'ingestion'
        
        # 触发智能体处理
        logger.info("🔄 触发智能体自动处理批量上传...")
        updated_state = run_async(conversation_agent.process_conversation_turn(state))
        session_states[session_id] = updated_state
        
        return jsonify({
            'success': True,
            'message': f'批量上传成功，共{len(upload_items)}个文件',
            'files_count': len(upload_items),
            'total_size_mb': round(total_size / 1024 / 1024, 2),
            'session_id': session_id,
            'current_phase': updated_state.get('current_phase', 'ingestion')
        })
        
    except Exception as e:
        error_msg = f"批量上传失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return jsonify({'success': False, 'error': error_msg}), 500

@app.route('/api/session/<session_id>/status', methods=['GET'])
def get_session_status(session_id: str):
    """获取会话处理状态"""
    logger.info(f"📊 查询会话状态: {session_id}")
    
    try:
        if session_id not in session_states:
            return jsonify({'success': False, 'error': '会话不存在'}), 404
        
        state = session_states[session_id]
        
        # 构建状态响应
        status_response = {
            'success': True,
            'session_id': session_id,
            'current_stage': state.get('current_phase', 'unknown'),
            'module_status': state.get('module_status', {}),
            'errors': state.get('errors', []),
            'progress': _calculate_progress(state),
            'final_report_ready': 'final_report' in state,
            'timestamp': datetime.now().isoformat()
        }
        
        # 添加详细信息
        if 'collected_data' in state:
            collected_data = state['collected_data']
            status_response['data_summary'] = {
                'has_facial_images': bool(collected_data.get('facial_images')),
                'has_bia_data': bool(collected_data.get('bia_files')),
                'has_glim_data': bool(collected_data.get('glim_results')),
                'total_files': len(state.get('processed_files', []))
            }
        
        logger.info(f"✅ 会话状态查询成功: {status_response['current_stage']}")
        return jsonify(status_response)
        
    except Exception as e:
        error_msg = f"状态查询失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg}), 500

def _calculate_progress(state: Dict[str, Any]) -> float:
    """计算整体进度（0.0-1.0）"""
    current_phase = state.get('current_phase', 'greeting')
    
    # 定义阶段权重
    phase_weights = {
        'greeting': 0.0,
        'ingestion': 0.1,
        'auto_analysis': 0.6,
        'comprehensive_analysis': 0.9,
        'completed': 1.0,
        'follow_up_conversation': 1.0
    }
    
    base_progress = phase_weights.get(current_phase, 0.0)
    
    # 如果在分析阶段，根据模块完成情况细化进度
    if current_phase == 'auto_analysis':
        module_status = state.get('module_status', {})
        if module_status:
            completed_modules = sum(1 for status in module_status.values() if status == 'done')
            total_modules = len(module_status)
            if total_modules > 0:
                module_progress = completed_modules / total_modules * 0.5  # 分析阶段内的进度
                base_progress = 0.1 + module_progress  # 基础进度 + 模块进度
    
    return round(base_progress, 2)

@app.route('/api/session/<session_id>/retry', methods=['POST'])
def retry_module(session_id: str):
    """重试失败的模块"""
    logger.info(f"🔄 收到模块重试请求: {session_id}")
    
    try:
        data = request.get_json() or {}
        module_name = data.get('module')
        
        if not module_name:
            return jsonify({'success': False, 'error': '缺少module参数'}), 400
        
        if session_id not in session_states:
            return jsonify({'success': False, 'error': '会话不存在'}), 404
        
        state = session_states[session_id]
        
        # 重置模块状态
        module_status = state.get('module_status', {})
        if module_name in module_status:
            module_status[module_name] = 'pending'
            
        # 清除相关错误
        errors = state.get('errors', [])
        state['errors'] = [e for e in errors if e.get('module') != module_name]
        
        # 设置阶段为自动分析以重新触发
        state['current_phase'] = 'auto_analysis'
        
        # 重新运行智能体
        updated_state = run_async(conversation_agent.process_conversation_turn(state))
        session_states[session_id] = updated_state
        
        logger.info(f"✅ 模块重试已触发: {module_name}")
        return jsonify({
            'success': True,
            'message': f'模块 {module_name} 重试已启动',
            'current_phase': updated_state.get('current_phase')
        })
        
    except Exception as e:
        error_msg = f"模块重试失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg}), 500

@app.route('/api/submit-profile', methods=['POST'])
def submit_profile():
    """处理用户档案提交"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '缺少档案数据'}), 400
        
        session_id = data.get('session_id', 'unknown')
        profile_data = data.get('profile', {})
        
        logger.info(f"收到用户档案提交，会话: {session_id}")
        
        # 清理用户档案数据 - 移除无效的身高体重值
        cleaned_profile_data = profile_data.copy()
        
        # 检查并移除无效的身高值
        height = profile_data.get('height')
        if height is not None:
            try:
                height_val = float(height) if height != '' else 0
                if height_val <= 0:
                    cleaned_profile_data.pop('height', None)
                    logger.info("已移除无效的身高值")
            except (ValueError, TypeError):
                cleaned_profile_data.pop('height', None)
                logger.info("已移除非数字身高值")
        
        # 检查并移除无效的体重值
        weight = profile_data.get('weight') or profile_data.get('current_weight')
        if weight is not None:
            try:
                weight_val = float(weight) if weight != '' else 0
                if weight_val <= 0:
                    cleaned_profile_data.pop('weight', None)
                    cleaned_profile_data.pop('current_weight', None)
                    logger.info("已移除无效的体重值")
            except (ValueError, TypeError):
                cleaned_profile_data.pop('weight', None) 
                cleaned_profile_data.pop('current_weight', None)
                logger.info("已移除非数字体重值")

        # 记录到增强日志
        add_enhanced_log(
            'user-data',
            '用户档案提交',
            user_profile=cleaned_profile_data,
            session_id=session_id,
            data_type='user_profile'
        )

        # 获取会话状态
        state = get_or_create_session_state(session_id)

        # 添加档案数据
        message_data = {
            'data_type': 'profile',
            'data': cleaned_profile_data
        }
        
        # 构建档案摘要时只显示有效字段
        profile_summary_parts = ["✅ 基本信息已提交："]
        profile_summary_parts.append(f"姓名：{cleaned_profile_data.get('name', '未填写')}")
        profile_summary_parts.append(f"年龄：{cleaned_profile_data.get('age', '未填写')}岁")
        profile_summary_parts.append(f"性别：{cleaned_profile_data.get('gender', '未填写')}")
        
        # 只有在有有效身高时才显示
        if cleaned_profile_data.get('height'):
            profile_summary_parts.append(f"身高：{cleaned_profile_data.get('height')}cm")
            
        # 只有在有有效体重时才显示
        weight_val = cleaned_profile_data.get('weight') or cleaned_profile_data.get('current_weight')
        if weight_val:
            profile_summary_parts.append(f"体重：{weight_val}kg")
        
        profile_summary = "\n".join(profile_summary_parts)
        
        state = run_async(conversation_agent.add_user_input(
            state,
            profile_summary,
            message_data
        ))
        
        # 继续对话流程
        state = run_async(conversation_agent.process_conversation_turn(state))
        
        # 更新会话状态
        session_states[session_id] = state
        
        # 获取AI回复
        assistant_messages = []
        for msg in reversed(state['messages']):
            if msg['role'] == 'assistant':
                assistant_messages.append(msg)
                break
        
        response = {
            'success': True,
            'session_id': session_id,
            'messages': assistant_messages,
            'frontend_actions': state.get('frontend_actions', []),
            'current_phase': state.get('current_phase', 'unknown'),
            'waiting_for': state.get('waiting_for', 'unknown'),
            'timestamp': datetime.now().isoformat()
        }
        
        log_api_call(session_id, 'submit-profile', cleaned_profile_data, {
            'messages_count': len(assistant_messages)
        }, True)
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"档案提交异常: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'档案处理失败: {str(e)}'
        }), 500

@app.route('/api/submit-glim', methods=['POST'])
def submit_glim():
    """处理GLIM表单提交"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '缺少GLIM数据'}), 400
        
        session_id = data.get('session_id', 'unknown')
        glim_data = data.get('glim_data', {})
        
        logger.info(f"收到GLIM表单提交，会话: {session_id}")

        # 记录到增强日志
        add_enhanced_log(
            'user-data',
            'GLIM评估提交',
            glim_data=glim_data,
            session_id=session_id,
            data_type='glim_assessment'
        )

        # 获取会话状态
        state = get_or_create_session_state(session_id)
        
        # 添加GLIM数据
        message_data = {
            'data_type': 'glim',
            'data': glim_data
        }
        
        state = run_async(conversation_agent.add_user_input(
            state,
            "✅ 已完成GLIM评估问卷填写",
            message_data
        ))
        
        # 继续对话流程
        state = run_async(conversation_agent.process_conversation_turn(state))
        
        # 更新会话状态
        session_states[session_id] = state
        
        # 提取响应信息
        assistant_messages = []
        frontend_actions = []

        # 获取最新的助手消息
        for msg in reversed(state['messages']):
            if msg['role'] == 'assistant':
                assistant_messages.append(msg)
                break

        # 获取前端动作
        frontend_actions = state.get('frontend_actions', [])

        # 构建响应
        response = {
            'success': True,
            'session_id': session_id,
            'messages': assistant_messages,
            'frontend_actions': frontend_actions,
            'current_phase': state.get('current_phase'),
            'waiting_for': state.get('waiting_for'),
            'collected_data': state.get('collected_data', {}),
            'is_completed': state.get('current_phase') == 'completed',
            'timestamp': datetime.now().isoformat()
        }

        log_api_call(session_id, 'submit-glim', glim_data, response, True)
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"GLIM提交异常: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'GLIM数据处理失败: {str(e)}'
        }), 500

@app.route('/api/get-session/<session_id>', methods=['GET'])
def get_session(session_id):
    """获取会话状态"""
    try:
        if session_id in session_states:
            state = session_states[session_id]
            return jsonify({
                'success': True,
                'session_id': session_id,
                'messages': state.get('messages', []),
                'current_phase': state.get('current_phase'),
                'collected_data': state.get('collected_data', {}),
                'user_profile': state.get('user_profile', {}),
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': '会话不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"获取会话异常: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/logs/<session_id>', methods=['GET'])
def get_session_logs(session_id):
    """获取会话日志"""
    try:
        logs = session_logs.get(session_id, [])
        return jsonify({
            'session_id': session_id,
            'logs': logs,
            'count': len(logs),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取日志异常: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/models', methods=['GET'])
def get_models():
    """获取可用模型信息"""
    try:
        health_info = lm_client.check_health()
        return jsonify(health_info)
    except Exception as e:
        logger.error(f"获取模型信息异常: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_chat_history', methods=['POST'])
def export_chat_history():
    """导出聊天记录为TXT文件"""
    try:
        data = request.get_json()
        session_id = data.get('sessionId', 'unknown')
        messages = data.get('messages', [])
        user_profile = data.get('userProfile', {})
        export_time = data.get('exportTime', datetime.now().isoformat())
        
        if not messages:
            return jsonify({'error': '没有可导出的聊天记录'}), 400
        
        # 生成TXT格式的聊天记录
        txt_content = _generate_chat_txt(session_id, messages, user_profile, export_time)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_file:
            temp_file.write(txt_content)
            temp_filename = temp_file.name
        
        # 返回文件内容
        try:
            with open(temp_filename, 'r', encoding='utf-8') as file:
                file_content = file.read()
            
            # 清理临时文件
            os.unlink(temp_filename)
            
            # 记录导出日志
            log_api_call(session_id, '导出聊天记录', 
                        {'messages_count': len(messages)}, 
                        {'success': True}, True)
            
            from flask import Response
            return Response(
                file_content,
                mimetype='text/plain; charset=utf-8',
                headers={
                    'Content-Disposition': f'attachment; filename="智能营养顾问_聊天记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt"'
                }
            )
            
        except Exception as e:
            # 确保清理临时文件
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
            raise e
            
    except Exception as e:
        logger.error(f"导出聊天记录失败: {e}")
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@app.route('/api/clear_models', methods=['POST'])
def clear_models():
    """强制清理所有已加载的模型"""
    try:
        logger.info("🧹 用户请求：清理所有已加载的模型")
        
        # 调用模型清理
        cleanup_result = conversation_agent.lm_client.cleanup_session_models()
        
        if cleanup_result['success']:
            logger.info("✅ 用户请求的模型清理完成")
            return jsonify({
                'success': True,
                'message': '模型清理完成',
                'details': cleanup_result
            })
        else:
            logger.warning(f"⚠️ 用户请求的模型清理失败: {cleanup_result['error']}")
            return jsonify({
                'success': False,
                'error': cleanup_result['error'],
                'details': cleanup_result
            }), 500
            
    except Exception as e:
        logger.error(f"模型清理API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'模型清理失败: {str(e)}'
        }), 500

def _generate_chat_txt(session_id: str, messages: List[Dict], user_profile: Dict, export_time: str) -> str:
    """生成TXT格式的聊天记录"""
    try:
        lines = []
        
        # 文件头部
        lines.append("=" * 80)
        lines.append("智能营养顾问 - 聊天记录")
        lines.append("=" * 80)
        lines.append(f"会话ID: {session_id}")
        lines.append(f"导出时间: {datetime.fromisoformat(export_time.replace('Z', '+00:00')).strftime('%Y年%m月%d日 %H:%M:%S')}")
        lines.append("")
        
        # 用户信息
        if user_profile:
            lines.append("📋 用户信息")
            lines.append("-" * 40)
            for key, value in user_profile.items():
                if value:  # 只显示有值的字段
                    field_names = {
                        'name': '姓名',
                        'gender': '性别', 
                        'age': '年龄',
                        'height': '身高(cm)',
                        'weight': '体重(kg)',
                        'usualWeight': '平时体重(kg)',
                        'medicalHistory': '疾病史'
                    }
                    field_name = field_names.get(key, key)
                    lines.append(f"{field_name}: {value}")
            lines.append("")
        
        # 聊天内容
        lines.append("💬 对话记录")
        lines.append("-" * 40)
        
        for i, message in enumerate(messages, 1):
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            timestamp = message.get('timestamp', '')
            
            # 角色标识
            if role == 'user':
                role_name = '👤 用户'
            elif role == 'assistant':
                role_name = '🤖 智能营养顾问'
            else:
                role_name = f'📝 {role}'
            
            # 时间戳处理
            time_str = ''
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = f" ({dt.strftime('%H:%M:%S')})"
                except:
                    time_str = f" ({timestamp})"
            
            # 添加消息
            lines.append(f"[{i:03d}] {role_name}{time_str}")
            
            # 处理消息内容，保持格式
            content_lines = content.split('\n')
            for line in content_lines:
                lines.append(f"     {line}")
            
            lines.append("")  # 消息间空行
        
        # 文件尾部
        lines.append("-" * 80)
        lines.append("本聊天记录由智能营养顾问系统自动生成")
        lines.append("⚠️ 重要提醒：本报告基于AI技术生成，仅供参考，不能替代专业医生诊断")
        lines.append("建议携带此报告咨询专业营养科医生或主治医生")
        lines.append("=" * 80)
        
        return '\n'.join(lines)
        
    except Exception as e:
        logger.error(f"生成聊天记录TXT失败: {e}")
        return f"生成聊天记录失败: {str(e)}"

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'API接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

# === 兼容性端点（为新前端界面添加）===

@app.route('/api/session-status/<session_id>', methods=['GET'])
def get_session_status_compat(session_id: str):
    """获取会话状态 - 兼容性端点"""
    logger.info(f"📊 兼容性端点查询会话状态: {session_id}")
    
    try:
        if session_id not in session_states:
            return jsonify({'success': False, 'error': '会话不存在'}), 404
        
        state = session_states[session_id]
        
        # 构建兼容的状态响应
        status_response = {
            'success': True,
            'data': {
                'session_id': session_id,
                'status': state.get('status', 'unknown'),
                'current_phase': state.get('current_phase', 'unknown'),
                'progress': state.get('progress', 0),
                'final_response': state.get('final_response', ''),
                'error': state.get('error', ''),
                'timestamp': datetime.now().isoformat()
            }
        }
        
        return jsonify(status_response)
        
    except Exception as e:
        logger.error(f"❌ 查询会话状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def main():
    """主函数"""
    print("🚀 启动对话式营养筛查智能体 API服务器")
    print("🧠 LangGraph + AI主脑整合版")
    print("=" * 60)

    # 设置增强日志函数为全局变量，供其他模块使用
    import builtins
    builtins.add_enhanced_log = add_enhanced_log
    logger.info("✅ 增强日志函数已设置为全局变量")
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    logger.add("logs/api_server.log", rotation="1 day", level="INFO")
    
    # 检查LM Studio连接
    print("🔍 检查LM Studio连接...")
    health = lm_client.check_health()
    if health['healthy']:
        print("✅ LM Studio连接正常")
        if health.get('current_models'):
            print(f"📋 当前加载的模型: {', '.join(health['current_models'])}")
        else:
            print("⚠️  未检测到已加载的模型，请在LM Studio中加载模型")
    else:
        print(f"❌ LM Studio连接失败: {health['error']}")
        print("请确认LM Studio已启动并在127.0.0.1:1234端口运行")
    
    print("\n🧠 初始化AI主脑智能体...")
    print("✅ ConversationAgent初始化完成")
    
    print("\n🌐 API服务器启动中...")
    print("访问地址:")
    print("  • 对话界面: http://localhost:5000/")
    print("  • API健康检查: http://localhost:5000/api/health")
    print("  • 日志查看: 通过前端日志按钮访问")
    
    print("\n📋 支持的API接口:")
    print("  • POST /api/conversation - 统一对话接口（主要）")
    print("  • POST /api/upload-image - 图像上传")
    print("  • POST /api/upload-bia - BIA数据上传")  
    print("  • POST /api/submit-profile - 用户档案提交")
    print("  • POST /api/submit-glim - GLIM表单提交")
    print("  • GET /api/get-session/<id> - 获取会话状态")
    print("  • GET /api/health - 健康检查")
    print("  • GET /api/models - 模型信息")
    
    print("\n🎯 核心特性:")
    print("  • AI主脑控制整个对话流程")
    print("  • 通过系统提示词智能决策")
    print("  • LangGraph提供专业状态管理")
    print("  • 真实调用本地大模型")
    print("  • 完整的会话状态管理")
    
    print(f"\n🛑 按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    # 启动服务器 - 配置适合本地大模型推理的超时设置
    from werkzeug.serving import WSGIRequestHandler
    WSGIRequestHandler.timeout = 2400  # 40分钟超时，适应本地大模型推理

    # 支持环境变量配置端口
    port = int(os.environ.get('PORT', 5000))

    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,
        threaded=True,
        request_handler=WSGIRequestHandler
    )

if __name__ == '__main__':
    main()