#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph智能体工具定义
为HuatuoGPT提供自主工具调用能力
"""
import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from loguru import logger

# LangGraph工具导入
from langchain_core.tools import tool
from langchain_core.messages import ToolMessage

# 现有组件导入
from src.core.lm_studio_client import LMStudioClient
from src.tools.bia_calculator import BIACalculator
from src.tools.glim_form_processor import GLIMFormProcessor
from src.utils.data_detection import detect_file_type
from src.utils.file_parsers import parse_bia_file, parse_glim_data


@tool
async def analyze_facial_nutrition(image_info: Dict[str, Any], patient_profile: Dict[str, Any] = None) -> str:
    """
    分析面部图像中的营养状况相关特征
    
    Args:
        image_info: 图像信息，包含文件路径、base64数据等
        patient_profile: 患者基本信息（年龄、性别、身高、体重等）
        
    Returns:
        str: 面部营养分析的详细文本报告
    """
    try:
        logger.info("🔧 工具调用：analyze_facial_nutrition")
        
        # 复用现有的视觉分析逻辑
        lm_client = LMStudioClient()
        
        # 获取患者信息以构建上下文化的提示词
        patient_info = patient_profile or {}
        
        # 使用专业的视觉分析提示词系统
        try:
            from config.vision_analysis_prompts import get_contextual_vision_prompt
            prompt = get_contextual_vision_prompt(patient_info, instance_id=1)
        except ImportError as e:
            error_msg = f"❌ 无法导入视觉分析提示词配置: {e}。请检查 config/vision_analysis_prompts.py 文件是否存在且格式正确。"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 获取图像数据 - 支持多种格式
        image_data = None
        
        # 方法1: 直接的base64数据
        if image_info.get("base64_data"):
            image_data = image_info["base64_data"]
        # 方法2: image_base64键（单个上传格式）
        elif image_info.get("image_base64"):
            image_data = image_info["image_base64"]
        # 方法3: 文件字节数据需要转换为base64（批量上传格式）
        elif image_info.get("file_bytes"):
            import base64
            if isinstance(image_info["file_bytes"], bytes):
                image_data = base64.b64encode(image_info["file_bytes"]).decode('utf-8')
            else:
                image_data = image_info["file_bytes"]  # 已经是base64字符串
        # 方法4: 文件路径
        elif image_info.get("path") or image_info.get("file_path"):
            image_data = image_info.get("path") or image_info.get("file_path")
        
        if not image_data:
            error_msg = f"未找到有效的图像数据，可用键: {list(image_info.keys())}"
            logger.error(error_msg)
            return error_msg
        
        logger.info(f"🖼️ 使用图像数据进行视觉分析，数据类型: {type(image_data)}, 长度: {len(str(image_data))}")
        
        # 🔄 **动态模型切换** - 开始视觉分析前卸载HuatuoGPT以释放内存
        logger.info("🔄 准备进行视觉分析，开始动态模型切换...")
        
        # 1. 卸载HuatuoGPT模型
        from config.settings import settings
        huatuogpt_model = settings.HUATUO_MODEL_ID
        logger.info(f"🔄 卸载HuatuoGPT模型以释放内存: {huatuogpt_model}")
        unload_huatuogpt_result = lm_client.unload_model_with_lms(huatuogpt_model)
        
        if unload_huatuogpt_result['success']:
            logger.info(f"✅ HuatuoGPT模型已卸载: {huatuogpt_model}")
        else:
            logger.warning(f"⚠️ HuatuoGPT模型卸载失败，继续进行视觉分析: {unload_huatuogpt_result['error']}")
        
        # 2. 调用视觉分析（call_vision_model内部会自动加载和卸载视觉模型）
        result = lm_client.call_vision_model(
            image_data=image_data,
            prompt=prompt
        )
        
        # 3. 视觉分析完成后重新加载HuatuoGPT模型（设置15000 tokens上下文长度）
        logger.info("🔄 视觉分析完成，重新加载HuatuoGPT模型...")
        reload_huatuogpt_result = lm_client.load_model_with_lms(
            huatuogpt_model, 
            ttl_seconds=3600,  # 1小时TTL
            context_length=15000  # 设置15000 tokens上下文长度
        )
        
        if reload_huatuogpt_result['success']:
            logger.info(f"✅ HuatuoGPT模型已重新加载: {huatuogpt_model} (上下文长度: 15000 tokens)")
        else:
            logger.error(f"❌ HuatuoGPT模型重新加载失败: {reload_huatuogpt_result['error']}")
        
        # 4. 更新结果信息，包含模型切换状态
        if result.get('success'):
            result['model_switching'] = {
                'huatuogpt_unloaded': unload_huatuogpt_result['success'],
                'huatuogpt_reloaded': reload_huatuogpt_result['success'],
                'vision_model_handled': result.get('vision_model_unloaded', False)
            }
            logger.info(f"🔄 动态模型切换完成: HuatuoGPT卸载={unload_huatuogpt_result['success']}, HuatuoGPT重载={reload_huatuogpt_result['success']}")
        else:
            # 如果视觉分析失败，确保HuatuoGPT被重新加载
            if not reload_huatuogpt_result['success']:
                logger.error("🚨 视觉分析失败且HuatuoGPT重新加载失败，尝试再次加载...")
                fallback_reload = lm_client.load_model_with_lms(huatuogpt_model, context_length=15000)
                if fallback_reload['success']:
                    logger.info("✅ HuatuoGPT模型降级重新加载成功")
                else:
                    logger.error("❌ HuatuoGPT模型降级重新加载也失败")
        
        if result.get('success'):
            # 格式化返回给HuatuoGPT的文本描述
            analysis = result.get('analysis', '')
            
            formatted_report = f"""
【面部营养特征分析报告】- 视觉分析已完成

患者信息：
- 年龄：{patient_info.get('age', '未提供')}岁
- 性别：{patient_info.get('gender', '未提供')}

面部分析结果：
{analysis}

分析模型：{result.get('model', 'vision')}
分析时间：{result.get('timestamp', 'N/A')}

【重要】视觉分析已完成，无需重复调用analyze_facial_nutrition工具。
请基于以上分析结果进行综合营养状况评估和临床建议。

注：此分析基于面部视觉特征，需结合其他临床数据进行综合评估。
"""
            
            logger.info("✅ 面部分析工具执行成功")
            return formatted_report.strip()
        else:
            error_msg = "面部图像分析失败：" + str(result.get('error', '未知错误'))
            logger.error(error_msg)
            return error_msg
            
    except Exception as e:
        error_msg = f"面部分析工具执行出错：{str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def calculate_bia_metrics(bia_data: Dict[str, Any], patient_info: Dict[str, Any] = None) -> str:
    """
    计算和分析BIA体成分数据
    
    Args:
        bia_data: BIA原始数据，包含PhA、ASMI、BMI等指标
        patient_info: 患者信息（年龄、性别等）
        
    Returns:
        str: BIA体成分分析的详细文本报告
    """
    try:
        logger.info("🔧 工具调用：calculate_bia_metrics")
        
        # 使用现有的BIA计算器
        bia_calculator = BIACalculator()
        
        # 执行BIA分析
        result = bia_calculator.comprehensive_analysis(
            bia_data=bia_data,
            patient_profile=patient_info or {}
        )
        
        if result.get('success'):
            analysis = result.get('analysis', {})
            
            # 格式化为文本报告给HuatuoGPT
            formatted_report = f"""
【BIA体成分分析报告】

患者信息：
- 年龄：{patient_info.get('age', '未提供')}岁  
- 性别：{patient_info.get('gender', '未提供')}

关键指标：
- 相角(PhA)：{bia_data.get('PhA', 'N/A')}°
- 骨骼肌指数(ASMI)：{bia_data.get('ASMI', 'N/A')} kg/m²
- BMI：{bia_data.get('BMI', 'N/A')} kg/m²
- 体脂率：{bia_data.get('Body_Fat', 'N/A')}%

分析结果：
{analysis.get('summary', '分析数据不完整')}

营养风险评估：
{analysis.get('risk_assessment', '需要更多数据')}

临床建议：
{analysis.get('recommendations', '请咨询专业营养师')}
"""
            
            logger.info("✅ BIA分析工具执行成功")
            return formatted_report.strip()
        else:
            error_msg = "BIA数据分析失败：" + str(result.get('error', '未知错误'))
            logger.error(error_msg)
            return error_msg
            
    except Exception as e:
        error_msg = f"BIA分析工具执行出错：{str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def assess_glim_criteria(glim_data: Dict[str, Any]) -> str:
    """
    根据GLIM标准评估营养不良程度
    
    Args:
        glim_data: GLIM评估数据，包含表型标准和病因学标准
        
    Returns:
        str: GLIM营养不良评估的详细文本报告
    """
    try:
        logger.info("🔧 工具调用：assess_glim_criteria")
        
        # 使用现有的GLIM处理器
        glim_processor = GLIMFormProcessor()
        
        # 执行GLIM评估
        result = glim_processor.process_form_data(glim_data)
        
        if result.get('success'):
            assessment = result.get('assessment', {})
            
            # 格式化为文本报告给HuatuoGPT
            formatted_report = f"""
【GLIM营养不良评估报告】

表型标准评估：
- 体重丢失：{'是' if glim_data.get('phenotypic_criteria', {}).get('weight_loss') else '否'}
- 低BMI：{'是' if glim_data.get('phenotypic_criteria', {}).get('low_bmi') else '否'}  
- 肌肉量丢失：{'是' if glim_data.get('phenotypic_criteria', {}).get('muscle_loss') else '否'}

病因学标准评估：
- 食物摄入减少：{'是' if glim_data.get('etiologic_criteria', {}).get('food_intake_reduction') else '否'}
- 疾病炎症：{'是' if glim_data.get('etiologic_criteria', {}).get('disease_inflammation') else '否'}

GLIM诊断结果：
等级：{assessment.get('malnutrition_grade', '评估不完整')}
严重程度：{assessment.get('severity', '需要更多信息')}

临床意义：
{assessment.get('clinical_significance', 'GLIM是国际认可的营养不良诊断标准')}

建议：
{assessment.get('recommendations', '请结合其他临床数据综合评估')}
"""
            
            logger.info("✅ GLIM评估工具执行成功")
            return formatted_report.strip()
        else:
            error_msg = "GLIM评估失败：" + str(result.get('error', '未知错误'))
            logger.error(error_msg)  
            return error_msg
            
    except Exception as e:
        error_msg = f"GLIM评估工具执行出错：{str(e)}"
        logger.error(error_msg)
        return error_msg


@tool  
def ingest_and_identify_data(uploaded_files: List[Dict[str, Any]]) -> str:
    """
    自动识别上传文件类型并提取关键信息
    
    Args:
        uploaded_files: 上传的文件列表，每个文件包含路径、二进制数据等信息
        
    Returns:
        str: 文件识别和数据提取结果的详细文本描述
    """
    try:
        logger.info("🔧 工具调用：ingest_and_identify_data")
        
        if not uploaded_files:
            return "未检测到任何上传文件"
        
        analysis_summary = "【数据识别和摄取报告】\n\n"
        
        identified_data = {
            "images": [],
            "bia_data": [],  
            "glim_data": [],
            "text_docs": [],
            "unknown": []
        }
        
        for file_info in uploaded_files:
            try:
                # 检测文件类型
                detection_result = detect_file_type(
                    file_info.get("file_bytes", b""),
                    file_info.get("filename", ""),
                    file_info.get("mimetype", "")
                )
                
                file_summary = f"文件：{file_info.get('filename', 'unknown')}\n"
                file_summary += f"类型：{detection_result.get('kind', 'unknown')}\n"
                file_summary += f"置信度：{detection_result.get('confidence', 0):.2f}\n"
                
                # 根据类型进行分类和解析
                if detection_result["kind"] == "image" and detection_result["confidence"] > 0.5:
                    identified_data["images"].append(file_info)
                    file_summary += "识别为：面部图像，用于营养状态视觉分析\n"
                    
                elif detection_result["kind"] == "bia" and detection_result["confidence"] > 0.7:
                    # 尝试解析BIA数据
                    try:
                        parsed_data = parse_bia_file(file_info["file_bytes"], file_info["filename"])
                        identified_data["bia_data"].append(parsed_data)
                        file_summary += f"识别为：BIA体成分数据，包含{len(parsed_data)}行数据\n"
                    except Exception as e:
                        file_summary += f"BIA数据解析失败：{str(e)}\n"
                        
                elif detection_result["kind"] == "glim" and detection_result["confidence"] > 0.7:
                    # 尝试解析GLIM数据
                    try:
                        parsed_data = parse_glim_data(file_info["file_bytes"], file_info["filename"])
                        identified_data["glim_data"].append(parsed_data)
                        file_summary += "识别为：GLIM营养评估表单\n"
                    except Exception as e:
                        file_summary += f"GLIM数据解析失败：{str(e)}\n"
                        
                elif detection_result["kind"] == "text":
                    identified_data["text_docs"].append(file_info)
                    file_summary += "识别为：医疗文档，可能包含临床信息\n"
                    
                else:
                    identified_data["unknown"].append(file_info)
                    file_summary += "未能识别，可能不是支持的数据类型\n"
                
                analysis_summary += file_summary + "\n"
                
            except Exception as e:
                analysis_summary += f"文件 {file_info.get('filename', 'unknown')} 处理失败：{str(e)}\n\n"
        
        # 生成摘要
        total_files = len(uploaded_files)
        identified_files = sum([
            len(identified_data["images"]),
            len(identified_data["bia_data"]),
            len(identified_data["glim_data"]), 
            len(identified_data["text_docs"])
        ])
        
        analysis_summary += f"处理摘要：\n"
        analysis_summary += f"- 总文件数：{total_files}\n"
        analysis_summary += f"- 成功识别：{identified_files}\n"
        analysis_summary += f"- 面部图像：{len(identified_data['images'])}张\n"
        analysis_summary += f"- BIA数据：{len(identified_data['bia_data'])}份\n"
        analysis_summary += f"- GLIM评估：{len(identified_data['glim_data'])}份\n"
        analysis_summary += f"- 医疗文档：{len(identified_data['text_docs'])}份\n"
        analysis_summary += f"- 未识别：{len(identified_data['unknown'])}份\n"
        
        if identified_files > 0:
            analysis_summary += f"\n建议分析顺序：\n"
            if identified_data["glim_data"]:
                analysis_summary += "1. 先进行GLIM标准评估\n"
            if identified_data["images"]:
                analysis_summary += "2. 分析面部营养特征\n" 
            if identified_data["bia_data"]:
                analysis_summary += "3. 计算BIA体成分指标\n"
            analysis_summary += "4. 最后综合所有数据进行营养诊断\n"
        
        logger.info(f"✅ 数据识别工具执行成功，识别{identified_files}/{total_files}个文件")
        return analysis_summary.strip()
        
    except Exception as e:
        error_msg = f"数据识别工具执行出错：{str(e)}"
        logger.error(error_msg)
        return error_msg


# 工具集合 - 提供给HuatuoGPT的可用工具（移除数据识别工具，直接智能分析）
NUTRITION_ANALYSIS_TOOLS = [
    analyze_facial_nutrition, 
    calculate_bia_metrics,
    assess_glim_criteria
]


def get_tools_description() -> str:
    """获取工具集合的描述信息"""
    return """
    可用的营养分析工具：

    1. analyze_facial_nutrition: 分析面部图像的营养状况特征  
    2. calculate_bia_metrics: 计算和分析BIA体成分数据
    3. assess_glim_criteria: 根据GLIM标准评估营养不良程度

    使用策略：
    - 根据文件类型直接选择合适的分析工具
    - 按照临床逻辑顺序进行分析
    - 最后综合所有结果进行诊断
    """
