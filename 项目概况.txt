2.主要研究内容、目标、方案和进度及拟解决的关键问题：
2.1 研究内容
本项目旨在构建一个基于多模态大语言模型（MLLM）的智能体（AI Agent）系统，用于疾病相关性营养不良（DRM）的自动化、智能化筛查与辅助诊断。研究将围绕智能体的架构设计、多模态信息融合、以及临床应用可行性展开，具体包括以下四个核心研究内容：

多模态营养不良诊断智能体（AI Agent）的架构设计与构建：
研究如何设计一个以MLLM为核心调度“主脑”，协同多个专业子模块工作的智能体系统。这包括：（1）构建一个由多个视觉语言模型（VLM）组成的、采用投票共识机制的视觉分析模块，以提高面部特征识别的鲁棒性并抑制模型幻觉；（2）集成一个灵活的API工具调用框架，使智能体能够动态调用外部计算工具或专业模型，以处理和分析如生物电阻抗（BIA）指标、医学影像等多样化数据。

面向智能体调度的多层次提示词工程（Prompt Engineering）体系开发：
研究并开发一套驱动智能体高效工作的提示词体系。该体系不仅包括设定MLLM“营养专家”角色的高级指令，还涵盖分发任务给各子模块（视觉模块、工具模块）的具体指令，以及指导MLLM整合所有返回信息、进行符合临床逻辑的最终推理与报告生成的复杂指令链。

“营养-面部体征”关联知识库的构建与应用：
系统性梳理并整合Tay et al. (2022)  等权威文献，构建一个结构化的“营养状态-面部体征”关联知识库。研究如何将该知识库有效嵌入视觉分析模块的提示词中，为VLM提供明确的评估参照系，引导其准确捕捉颞部凹陷、颊部凹陷等关键视觉生物标志物。

智能体系统性能的综合评估与临床可用性分析：
在真实临床数据集上，系统性地评估该智能体架构的诊断性能。通过与资深临床专家的共识诊断进行对比，采用F1分数、AUC、准确率等指标进行定量评估。同时，由临床专家对模型生成的诊断报告进行定性分析，评估其推理过程的逻辑合理性、证据溯源的准确性及语言的专业性，全面考察其临床转化潜力。


2.2 研究目标
本项目的总体目标是：开发一个可推广的的、基于多模态大模型智能体架构的营养不良多模态智能诊断系统。该系统旨在超越传统单模型方法的性能和可靠性瓶颈，为传统营养不良诊断提供一个兼具高准确性、强可解释性和良好扩展性的新型辅助诊断工具。
为实现此总体目标，设定以下具体目标：
架构验证目标： 成功构建一个以MLLM为核心，集成多VLM投票式视觉模块和API工具调用模块的智能体原型系统，并验证该架构在处理多源异构医学数据（图像、文本、量化指标）时的可行性与高效性。
性能评估目标： 在包含至少100-200例真实患者样本的数据集上，实现对营养不良（无/中度/重度）三分类任务的优异预测性能，力求关键指标（如F1分数）超越单模态方法的基线（如AUC > 0.70 ）。
可解释性与可靠性目标： 验证多VLM投票机制在降低单一模型误判和幻觉方面的有效性。同时，确保系统能生成逻辑清晰、证据可追溯的诊断报告，增强临床医生对AI决策过程的信任度和接受度。
转化应用目标： 基于最优化的系统配置，开发一个功能可演示的用户友好型原型，为未来部署于临床一线（如医院信息系统插件、移动App）奠定坚实的技术基础。

2.3 拟解决的关键科学问题
1、多模态大语言模型能否有效融合面部图像与结构化临床数据，实现对营养不良的准确识别？本问题聚焦模型对异质性输入（图像+文本+数值）之间语义联系的理解与综合推理能力，评估其在多源信息融合下的表现稳定性与诊断可靠性。
2、面对临床实践中各种各样的新检查手段和数据类型，如何构建一个具备良好扩展性的AI系统？本项目通过API工具调用框架探索解决方案，旨在设计一个让智能体“即插即用”地集成新的分析能力，以适应未来医学技术的发展。
3、模型生成的推理过程是否具备逻辑合理性与临床解释性？着眼于临床可用性，检验大模型输出的推理链条是否具备医学知识基础、是否能够被专业人员理解与接受，从而推动“可解释AI”在医学场景中的应用落地。
4、在真实临床环境中，提示词工程如何实现适配性与可重复性？探索提示词模板在不同患者特征、数据缺失情况下的鲁棒性表现，评估其迁移到其他疾病或机构环境中的应用潜力。

2.4 技术路线
数据采集与准备： 与合作医院共同构建一个包含2D面部图像、GLIM量化数据、BIA生理指标和电子病历的多模态数据集，并由专家组完成“金标准”诊断标注。
↓
知识库构建： 整理相关文献，构建“营养-面部体征”的结构化知识库。
↓
智能体系统开发：
主脑 MLLM：选择并部署核心MLLM，编写顶层控制提示词。
视觉模块：部署多个VLM，开发投票共识算法，并将知识库嵌入其提示词。
工具模块：开发或封装用于计算BIA指标、分析影像的API，并建立MLLM调用这些API的机制。
↓
系统集成与测试： 将所有模块集成为一个完整的智能体系统，并在小规模样本上进行功能调试。
↓
性能评估与分析： 在完整的测试集上运行系统，将输出结果与专家“金标准”进行对比，进行定性和定量评估。
↓
优化与成果转化： 根据评估结果迭代优化系统，并开发可交互的原型。


2.4研究方案
本项目旨在开发基于面部图像识别的营养不良筛查智能体系统，构建一套适用于标准2D图像与结构化临床信息的诊断提示词体系，并评估其诊断性能。
数据集构建
研究工作将依托温州医科大学附属第一医院营养科及胃肠外科，招募100–200名门诊或住院患者为研究对象。纳入标准为年龄60岁以上，并已签署知情同意；排除标准包括严重面部畸形、认知障碍或关键临床信息缺失。每位受试者将采集以下四类关键数据：（1）在标准环境下获取正面无遮挡2D面部图像；（2）依据GLIM标准采集体重减轻、食物摄入减少、BMI等量化指标；（3）通过统一型号BIA设备测量生理参数（如PhA、ASMI等）；（4）提取包含营养相关内容的电子病历文本，分别保留原始版本与由大模型生成的病历摘要版本。由两名及以上副高职称的营养专科医师依据GLIM标准独立判定患者营养状态，形成共识结果，作为模型评估的参考标准。
知识库构建与临床特征整理
为增强多模态提示词的医学解释能力，本项目将同步构建一套结构化的“营养状态—面部体征”关联知识库。该知识库旨在作为提示词工程中“医学知识嵌入模块”的基础资源，为模型提供明确的评估参照。
本部分将系统梳理并持续更新已公开发表的、支持面部形态与营养不良状态之间关联的临床研究文献，提取可量化或具描述性特征的证据，并按面部解剖区域分类整理。示例如下表所示：
面部区域	特征	说明	佐证文献
上脸部
（额头和眼眶周围）	太阳穴凹陷	正常情况下，太阳穴区域饱满；营养不良时，这一部位会明显凹陷	①
	颞肌（咀嚼肌之一）变薄	是判断肌肉量减少的信号，常用于诊断肌少症	②
	眼眶变深、黑眼圈明显	因为眼睛周围的脂肪减少，导致出现“深眼窝”现象	③
中脸部
（面颊和颧骨）	面颊凹陷	营养不良时，脸颊脂肪减少，面部看起来塌陷	④
	咬肌（咀嚼肌）变薄	咬肌变小，会让面部轮廓变得不清晰，是肌肉减少的表现之一	⑤
	颧骨突出	脂肪垫减少后，颧骨会更加明显，脸显得“高颧瘦削”	⑥
下脸部
（下颌和下巴）	下颌线条清晰	体脂减少时，下巴和下颌的骨骼轮廓会变得更清楚	⑦
	脸型变尖	体重降低者脸型通常更尖、下颌角度更明显；体重增加则脸型更圆钝	⑧
	颈部皮肤松弛或凹陷	随着脂肪和肌肉减少，下巴以下的皮肤可能出现下垂或塌陷	⑨
佐证文献：① PLoS One 2018;13(11):e0207849；TJ Oncol 2020:119-126；J Clin Med 2020 Apr 28;9(5):1272；② Nutr Clin Pract 2015 Apr;30(2):239-248；Br J Anaesth 2019 Jul;123(1):37-50；J Gerontol A Biol Sci Med Sci 2021 Nov 15;76(12):2242-2248；③ Surv Ophthalmol 2017;62(2):190-202；Plast Reconstr Surg 2014 Dec;134(6):1154-1170；Dermatol Surg 2009 Aug;35(8):1163-1171；④ Nutr Clin Pract 2015 Apr;30(2):239-248；Postepy Dermatol Alergol 2015 Apr;32(2):127-133；JPEN J Parenter Enteral Nutr 2012 Apr 24;36(3):275-283；⑤ Gerodontology 2020 Dec;37(4):383-388；J Surg Res 2014 Jan;186(1):246-252；Arch Gerontol Geriatr 2018;78:18-22；⑥ Postepy Dermatol Alergol 2015 Apr;32(2):127-133；Swiss Dent J 2018 Sep 10;128(9):678-688；Plast Reconstr Surg Glob Open 2013 Dec;1(9):e92；⑦ Perception 2010;39(1):51-61；PLoS ONE 2017 Jan 4;12(1):e0169336；Image Vision Comput 2013 May;31(5):392-400；⑧ Perception 2010;39(1):51-61；PLoS ONE 2017 Jan 4;12(1):e0169336；⑨ Aesthet Surg J 2006;26(1S):S4-S9；Madridge J Dermatol Res 2017 Dec 29;2(1):26-31；Dermatol Surg 2000 Dec;26(12):1107-1112；Plast Reconstr Surg Glob Open 2019 Oct;7(10):e2459.
基于多模态大模型的智能体AI系统构建
基于多模态大模型的智能体AI系统构建的算法框架如下：

系统整体架构： 采用以一个中央MLLM为“主脑”的智能体架构。主脑负责任务理解、规划、分解和调度，并将专业任务分派给两个核心子模块：①多实例VLM投票式视觉分析模块 和 ②API工具调用模块。最终由主脑汇总所有信息，完成综合推理。
MLLM：智能体的中枢神经系统
本系统的核心是一个功能强大的多模态大模型（MLLM），选用通义千问-Omni-Turbo模型。它不直接执行底层分析，而是作为整个系统的“中枢神经系统”或“认知调度官”，其核心职责包括：
意图识别与任务规划 (Intent Recognition & Task Planning)：当用户输入请求（例如，“请根据这张面部照片和BIA数据，分析该患者的营养不良风险”）时，核心MLLM首先解析用户的自然语言意图，并将其分解为一系列可执行的子任务。例如，上述请求会被分解为：(1) 分析面部图像的视觉特征；(2) 解读BIA数据；(3) 综合视觉和BIA数据，评估营养风险。
工具选择与调度 (Tool Selection & Dispatch)：基于任务规划的结果，MLLM会从其可用的“工具库”中选择最合适的工具来执行每个子任务。它会根据每个工具预先注册的描述信息（例如，“calculate_glim_score：一个用于根据GLIM标准计算营养不良评分的工具”）来做出决策。随后，它会生成一个符合该工具API规范的请求，并将相关数据（如图像、JSON格式的指标）作为参数传递。
结果综合与推理 (Response Synthesis & Reasoning)：在所有被调用的工具完成任务并返回结构化的输出（通常是JSON格式）后，核心MLLM负责收集、整理这些来自不同源头的信息。它将这些碎片化的证据整合在一起，进行最终的综合推理，形成一个连贯、有逻辑的诊断结论和解释。

任务分解与智能路由
本系统遵循“委托认知”（Delegated Cognition）的原则，即MLLM根据输入数据的类型和性质，动态地将认知负荷委托给最适合的专业子系统。这种智能委托机制是系统高效、精准运行的关键，其逻辑流程如下（基于用户提供的路线图）：
当输入数据包含面部照片 (facial_image)：
MLLM决策：识别到图像模态。
动作：调用视觉感知子系统。将图像数据传递给该子系统，并请求返回一份关于面部营养不良相关体征的结构化视觉描述。
当输入数据包含BIA指标、GLIM分析表或其他结构化/数值数据：
MLLM决策：识别到数值或结构化文本数据。
动作：根据数据类型，调用相应的分析工具。例如，将BIA指标传递给计算工具，或将GLIM问卷数据传递给问卷分析工具。
当输入数据包含医学影像 (如CT扫描)：
MLLM决策：识别到专业的医学影像数据（如DICOM格式）。
动作：调用高级的影像分析工具。该工具内部封装了专门的深度学习模型（U-Net），能够自主完成影像的分割和量化分析。
当输入数据包含非结构化文本 (如过往病历)：
MLLM决策：识别到长篇自由文本。
动作：调用文本分析工具，从病历中提取与营养不良病因学相关的关键信息（如食物摄入减少史、慢性消化道疾病、炎症指标等）。
通过这种方式，系统确保了每一种数据都被最专业的模块处理，避免了让一个通用模型去处理其不擅长的任务，从而最大化了整个系统的分析能力和准确性。

视觉感知子系统：面部视觉分析
本子系统的构建并非凭空臆想，而是基于坚实的临床观察和科学研究。大量文献证实，面部形态的变化可以作为评估个体营养状况的有效、无创窗口 。核心原理在于，营养不良，特别是蛋白质-能量营养不良，会导致身体脂肪储备和肌肉总量的减少。这些变化会直接体现在面部，因为面部的皮下脂肪垫（如颊脂垫、眼周脂肪垫）和肌肉（如颞肌、咬肌）的萎缩，会使得下方的骨骼结构（如颧骨、下颌骨）变得更加突出，从而改变面部的整体轮廓和形态 。     
为了引导VLM进行专业、精准的分析，我们不会向其发出“描述这张脸”这样宽泛的指令。相反，我们将构建一个高度结构化的提示（Prompt），该提示将直接嵌入从临床文献和既有知识库中提炼出的专业知识。
提示词遵循以下结构：
# [角色与任务设定]
你是一位临床营养学专家，你的任务是基于公认的医学知识，对提供的面部图像进行详细的形态学评估，以识别与营养不良相关的潜在体征。

# [嵌入的临床知识：评估指南]
请严格按照以下指南进行分析。营养不良通常导致面部脂肪和肌肉流失，使骨骼轮廓更突出。请重点评估以下区域：
1.  **上脸部 (Upper Face)**:
    -   **颞部凹陷 (Temporal Hollowing)**: 观察太阳穴区域是否存在凹陷，这是颞肌萎缩的标志。
    -   **眼窝深陷 (Sunken Orbits)**: 评估眼眶周围脂肪减少导致的眼窝凹陷和黑眼圈。
2.  **中脸部 (Midface)**:
    -   **面颊凹陷 (Cheek Hollowing)**: 检查颊脂垫萎缩导致的面颊塌陷。
    -   **颧骨突出 (Zygomatic Prominence)**: 评估颧骨是否因周围软组织减少而显得异常突出。
3.  **下脸部 (Lower Face)**:
    -   **下颌轮廓清晰度 (Mandibular Definition)**: 评估下颌线是否因皮下脂肪减少而变得异常锐利和清晰。

# [输出格式指令]
请将你的分析结果严格按照以下JSON格式返回，对每个评估区域给出发现和置信度：
{
  "visual_analysis": [
    {"facial_region": "Upper Face", "finding": "描述颞部和眼眶的发现", "confidence": float (0-1)},
    {"facial_region": "Midface", "finding": "描述面颊和颧骨的发现", "confidence": float (0-1)},
    {"facial_region": "Lower Face", "finding": "描述下颌的发现", "confidence": float (0-1)}
  ]
}
通过这种方式，我们将模型的分析过程“锚定”在经过临床验证的知识框架内，极大地提高了输出结果的专业性和相关性。

共识机制：通过投票法减轻幻觉与偏差
为了确保视觉分析的鲁棒性和准确性，本子系统将采用一个由多个视觉-语言模型（Vision-Language Models, VLMs）组成的集成架构，而非依赖单一模型。系统以Qwen-VL系列模型（如Qwen2.5-VL）为主力，后续可集成其他先进的VLM（如LLaVA、Fuyu-8B等）作为辅助验证模型。
采用集成架构的核心是为了降低模型特异性偏差和减轻幻觉风险。任何单一的AI模型都可能存在其独特的“偏见”或在某些情况下产生不准确输出（即“幻觉”）。通过模型的内在随机性（通过设置非零的temperature参数如0.2）生成多个略有差异但各自独立的视觉解读，因此我们可以交叉验证其输出。如果模型多次都识别出同一特征（例如，“颞部凹陷”），那么该特征存在的置信度就远高于单次模型的判断。这种方法类似于在临床诊断中寻求“第二意见”，可有效提升结果的准确性。
并行分析：同一张面部图像和上述结构化提示，被同时发送给系统中的N个VLM。
结构化输出收集：系统收集每个VLM返回的标准化JSON格式的分析结果。
特征级投票：系统对每个具体的形态学特征进行投票。例如，对于“颞部凹陷”这一特征，系统会检查N个模型的输出。如果超过阈值（例如，N个模型中的2/3）的模型报告了“存在显著颞部凹陷”，则该结论被采纳。对于描述性文本，可以采用更复杂的文本相似性算法或由一个上层语言模型来综合。
生成共识报告：系统将所有通过投票的特征及其平均置信度，整合成一份最终的、高度可靠的共识性视觉分析报告（JSON格式）。
这份共识报告，而非任何单个模型的原始输出，将被传递给上层的核心MLLM，作为其综合诊断的视觉证据输入。这一机制有效地过滤了单个模型的随机错误和幻觉，显著提升了视觉分析的整体可靠性。

多层次提示词工程与实施流程：
系统级提示词（System Prompt）： 为主脑MLLM设定核心身份和行为准则：“你是一位名为‘Nutri-Agent’的AI临床营养学专家...”
实施流程：对于测试集中的每个病例，系统将自动化执行以下流程：
任务接收与分解： 主脑MLLM接收到包含面部照片、BIA数据、病历等多模态输入后，识别出需要进行视觉分析和数据计算两项子任务。
并行任务分派：
主脑将面部照片及内嵌了知识库的指令，同时发送给3个Qwen-2.5-VL实例。
同时，主脑将BIA等数据打包，调用计算营养指标等API工具。
结果回收与共识形成：
回收3个VLM返回的视觉描述，通过共识算法（基于关键词匹配和语义相似度）形成一份统一的、高置信度的视觉证据报告。
回收所有API工具返回的结构化数据。
最终推理与报告生成： 主脑MLLM整合视觉证据报告和API工具结果，并结合原始病历信息，进行一步一思考的“思维链”（Chain-of-Thought）推理，最终生成一份包含诊断结论、置信度、详细推理过程和后续建议的结构化JSON报告。

数据流与交互协议
为了确保系统各子系统间的顺畅通信，我们定义了标准化的数据流和交互协议。整个工作流程从用户输入开始，到最终报告生成结束，每一步的数据格式和传输方式都将被明确规定。
一个典型的交互流程如下：
用户输入：用户通过前端界面上传多种数据，如一张JPEG格式的面部照片、一个包含BIA和GLIM数据的JSON文件，以及一段病历文本。
前端处理：前端将这些异构数据打包成一个统一的JSON请求体，发送至后端智能体控制器。
智能体控制器接收：核心MLLM智能体接收到请求，开始进行意图识别和任务规划。
工具调用：智能体根据规划，向各个工具的API端点发起并发或串行调用。
向视觉感知子系统发送一个包含图像数据的POST请求。
向分析工具API发送包含BIA/GLIM数据的JSON负载。
工具响应：每个工具执行完毕后，返回一个标准化的JSON响应。例如，视觉子系统返回{"visual_features":}。
结果综合：智能体控制器收集所有工具的JSON响应。
最终分析与报告生成：智能体将所有收集到的信息结合提示词一同发送给HuatuoGPT-o1（华佗医学推理模型），生成最终的分析报告。
返回用户：将结构化的最终报告（JSON格式）返回给前端，前端负责将其渲染成用户友好的可视化报告。
这种基于API和标准化JSON的交互协议，确保了系统的松耦合和高内聚，使得任何一个组件的升级或替换都不会影响到其他部分。

（4）性能评估
模型输出将与专家共识结果进行对比，开展定量与定性评估：
定量评估指标：三分类宏平均F1-score、AUC、准确率、精确率、召回率；使用配对统计方法（如DeLong检验、Wilcoxon符号秩检验）检验组间性能差异是否显著（P < 0.05）。
定性评估指标：由临床专家抽样审阅模型“推理过程”字段，从逻辑合理性、证据匹配度、语言专业性三方面进行打分。
最终将确定最优提示词策略配置，并总结其结构特征与适用场景。










