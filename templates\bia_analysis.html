<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIA身体成分分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'SimSun', serif;
            line-height: 1.4;
            background-color: #ffffff;
            padding: 20px;
            font-size: 14px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            color: #000;
            margin-bottom: 20px;
            letter-spacing: 1px;
        }
        
        .bia-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
            border: 2px solid #000;
        }
        
        .bia-table th,
        .bia-table td {
            border: 1px solid #000;
            padding: 12px;
            text-align: left;
        }
        
        .bia-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .label-cell {
            width: 150px;
            font-weight: bold;
            background-color: #f8f8f8;
            text-align: center;
        }
        
        .input-cell {
            width: 200px;
        }
        
        .bia-table input {
            border: none;
            background: transparent;
            width: 100%;
            font-size: 14px;
            padding: 4px;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 25px;
            margin: 0 10px;
            border: 1px solid #000;
            background: #f0f0f0;
            font-size: 14px;
            cursor: pointer;
            font-family: inherit;
        }
        
        .btn:hover {
            background: #e0e0e0;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .btn-primary:hover {
            background: #45a049;
        }
        
        .result-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
            display: none;
        }
        
        .result-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
            border: 1px solid #000;
        }
        
        .result-table th,
        .result-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        
        .result-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BIA身体成分分析工具</h1>
        </div>

        <form id="biaForm">
            <!-- 基本信息 -->
            <table class="bia-table">
                <tr>
                    <th colspan="4">患者基本信息</th>
                </tr>
                <tr>
                    <td class="label-cell">姓名</td>
                    <td class="input-cell"><input type="text" id="name" name="name" value="张某某" required></td>
                    <td class="label-cell">年龄</td>
                    <td class="input-cell"><input type="number" id="age" name="age" value="65" required> 岁</td>
                </tr>
                <tr>
                    <td class="label-cell">性别</td>
                    <td class="input-cell">
                        <select id="gender" name="gender" required style="width: 100%; border: none; background: transparent;">
                            <option value="男" selected>男</option>
                            <option value="女">女</option>
                        </select>
                    </td>
                    <td class="label-cell">身高</td>
                    <td class="input-cell"><input type="number" id="height" name="height" value="170" step="0.1" required> cm</td>
                </tr>
                <tr>
                    <td class="label-cell">体重</td>
                    <td class="input-cell"><input type="number" id="weight" name="weight" value="58" step="0.1" required> kg</td>
                    <td class="label-cell">BMI</td>
                    <td class="input-cell"><input type="text" id="bmi" name="bmi" readonly style="background: #f0f0f0;"> kg/m²</td>
                </tr>
            </table>

            <!-- BIA测量数据 -->
            <table class="bia-table">
                <tr>
                    <th colspan="4">BIA测量数据</th>
                </tr>
                <tr>
                    <td class="label-cell">阻抗值(R)</td>
                    <td class="input-cell"><input type="number" id="resistance" name="resistance" value="520" step="0.1" required> Ω</td>
                    <td class="label-cell">电抗值(Xc)</td>
                    <td class="input-cell"><input type="number" id="reactance" name="reactance" value="45" step="0.1" required> Ω</td>
                </tr>
                <tr>
                    <td class="label-cell">相位角</td>
                    <td class="input-cell"><input type="number" id="phase_angle" name="phase_angle" value="4.9" step="0.1" required> °</td>
                    <td class="label-cell">频率</td>
                    <td class="input-cell"><input type="number" id="frequency" name="frequency" value="50" required> kHz</td>
                </tr>
            </table>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button type="button" class="btn" onclick="resetForm()">重置表单</button>
                <button type="submit" class="btn btn-primary">计算分析</button>
            </div>
        </form>

        <!-- 结果显示区域 -->
        <div class="result-section" id="resultSection">
            <div class="result-title">BIA身体成分分析结果</div>
            <div id="biaResults"></div>
        </div>
    </div>

    <script>
        // 自动计算BMI
        function calculateBMI() {
            const height = parseFloat(document.getElementById('height').value);
            const weight = parseFloat(document.getElementById('weight').value);
            
            if (height > 0 && weight > 0) {
                const bmi = (weight / Math.pow(height / 100, 2)).toFixed(1);
                document.getElementById('bmi').value = bmi;
            }
        }

        // 监听身高体重变化
        document.getElementById('height').addEventListener('input', calculateBMI);
        document.getElementById('weight').addEventListener('input', calculateBMI);

        // BIA计算函数
        function calculateBIA() {
            const data = {
                name: document.getElementById('name').value,
                age: parseInt(document.getElementById('age').value),
                gender: document.getElementById('gender').value,
                height: parseFloat(document.getElementById('height').value),
                weight: parseFloat(document.getElementById('weight').value),
                resistance: parseFloat(document.getElementById('resistance').value),
                reactance: parseFloat(document.getElementById('reactance').value),
                phase_angle: parseFloat(document.getElementById('phase_angle').value),
                frequency: parseFloat(document.getElementById('frequency').value)
            };

            // 计算BMI
            const bmi = (data.weight / Math.pow(data.height / 100, 2));

            // 计算总体水分(TBW) - Kushner公式
            const tbw = data.gender === '男' ? 
                (0.396 * Math.pow(data.height, 2) / data.resistance + 0.143 * data.weight + 8.399) :
                (0.372 * Math.pow(data.height, 2) / data.resistance + 0.096 * data.weight + 1.069);

            // 计算去脂体重(FFM)
            const ffm = tbw / 0.732;

            // 计算体脂量(FM)
            const fm = data.weight - ffm;

            // 计算体脂率
            const body_fat_percentage = (fm / data.weight) * 100;

            // 计算骨骼肌量(SMM) - 简化公式
            const smm = data.gender === '男' ?
                (0.244 * Math.pow(data.height, 2) / data.resistance + 0.064 * data.weight + 0.139 * data.age + 5.71) :
                (0.252 * Math.pow(data.height, 2) / data.resistance + 0.138 * data.weight - 0.071 * data.age + 2.94);

            // 计算四肢骨骼肌量(ASM) - 约为SMM的75%
            const asm = smm * 0.75;

            // 计算骨骼肌指数(ASMI)
            const asmi = asm / Math.pow(data.height / 100, 2);

            // 计算内脏脂肪等级(简化估算)
            const visceral_fat_level = Math.round((data.weight * body_fat_percentage / 100 - 10) / 2);

            // 评估肌肉质量
            const muscle_assessment = data.gender === '男' ?
                (asmi < 7.0 ? '肌肉质量减少' : asmi < 8.5 ? '肌肉质量正常偏低' : '肌肉质量正常') :
                (asmi < 5.7 ? '肌肉质量减少' : asmi < 6.8 ? '肌肉质量正常偏低' : '肌肉质量正常');

            // 相位角评估
            const phase_assessment = data.phase_angle < 5.0 ? '偏低' : 
                                   data.phase_angle > 7.0 ? '正常' : '正常偏低';

            // 显示结果
            displayBIAResults({
                ...data,
                bmi: bmi.toFixed(1),
                tbw: tbw.toFixed(1),
                ffm: ffm.toFixed(1),
                fm: fm.toFixed(1),
                body_fat_percentage: body_fat_percentage.toFixed(1),
                smm: smm.toFixed(1),
                asm: asm.toFixed(1),
                asmi: asmi.toFixed(1),
                visceral_fat_level: Math.max(1, visceral_fat_level),
                muscle_assessment,
                phase_assessment
            });
        }

        // 显示BIA结果
        function displayBIAResults(results) {
            const resultsHTML = `
                <table class="result-table">
                    <tr><th colspan="2">基础指标</th></tr>
                    <tr><td>BMI</td><td>${results.bmi} kg/m²</td></tr>
                    <tr><td>体脂率</td><td>${results.body_fat_percentage}%</td></tr>
                    <tr><td>体脂量</td><td>${results.fm} kg</td></tr>
                    <tr><td>去脂体重</td><td>${results.ffm} kg</td></tr>
                    <tr><td>总体水分</td><td>${results.tbw} kg</td></tr>
                    <tr><td>内脏脂肪等级</td><td>${results.visceral_fat_level}级</td></tr>
                </table>
                
                <table class="result-table">
                    <tr><th colspan="2">肌肉质量分析</th></tr>
                    <tr><td>骨骼肌量(SMM)</td><td>${results.smm} kg</td></tr>
                    <tr><td>四肢骨骼肌量(ASM)</td><td>${results.asm} kg</td></tr>
                    <tr><td>骨骼肌指数(ASMI)</td><td>${results.asmi} kg/m²</td></tr>
                    <tr><td>肌肉质量评估</td><td>${results.muscle_assessment}</td></tr>
                </table>
                
                <table class="result-table">
                    <tr><th colspan="2">营养状态指标</th></tr>
                    <tr><td>相位角</td><td>${results.phase_angle}°</td></tr>
                    <tr><td>相位角评估</td><td>${results.phase_assessment}</td></tr>
                    <tr><td>阻抗值</td><td>${results.resistance} Ω</td></tr>
                    <tr><td>电抗值</td><td>${results.reactance} Ω</td></tr>
                </table>
            `;

            document.getElementById('biaResults').innerHTML = resultsHTML;
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 收集BIA数据
        function collectBIAData() {
            // 这里返回计算后的完整数据，供后续大模型分析使用
            return {
                patient_info: {
                    name: document.getElementById('name').value,
                    age: parseInt(document.getElementById('age').value),
                    gender: document.getElementById('gender').value,
                    height: parseFloat(document.getElementById('height').value),
                    weight: parseFloat(document.getElementById('weight').value)
                },
                bia_measurements: {
                    resistance: parseFloat(document.getElementById('resistance').value),
                    reactance: parseFloat(document.getElementById('reactance').value),
                    phase_angle: parseFloat(document.getElementById('phase_angle').value),
                    frequency: parseFloat(document.getElementById('frequency').value)
                },
                calculated_results: {
                    // 这里会包含所有计算结果
                },
                analysis_timestamp: new Date().toISOString()
            };
        }

        // 表单提交处理
        document.getElementById('biaForm').addEventListener('submit', function(e) {
            e.preventDefault();
            calculateBIA();
            
            // 保存数据供其他模块使用
            const biaData = collectBIAData();
            localStorage.setItem('biaData', JSON.stringify(biaData));
            console.log('BIA分析完成:', biaData);
        });

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？')) {
                document.getElementById('biaForm').reset();
                document.getElementById('resultSection').style.display = 'none';
                // 重新设置默认值
                document.getElementById('name').value = '张某某';
                document.getElementById('age').value = '65';
                document.getElementById('gender').value = '男';
                document.getElementById('height').value = '170';
                document.getElementById('weight').value = '58';
                document.getElementById('resistance').value = '520';
                document.getElementById('reactance').value = '45';
                document.getElementById('phase_angle').value = '4.9';
                document.getElementById('frequency').value = '50';
                calculateBMI();
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            calculateBMI();
            console.log('BIA分析工具已初始化');
        });
    </script>
</body>
</html>
