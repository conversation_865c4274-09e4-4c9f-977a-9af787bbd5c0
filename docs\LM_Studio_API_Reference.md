# LM Studio API 完整参考文档

## 概述

LM Studio 提供了多种方式来管理和使用本地LLM模型：

1. **OpenAI兼容的REST API** - 用于聊天完成、嵌入等
2. **LM Studio Python SDK** - 官方Python客户端库
3. **lms CLI工具** - 命令行模型管理工具
4. **LM Studio REST API (beta)** - 原生REST API

## 1. lms CLI 工具

### 安装和配置

```bash
# macOS/Linux
~/.lmstudio/bin/lms bootstrap

# Windows PowerShell
cmd /c %USERPROFILE%/.lmstudio/bin/lms.exe bootstrap
```

### 核心命令

#### 服务器管理
```bash
lms server start    # 启动本地服务器
lms server stop     # 停止本地服务器
lms server status   # 查看服务器状态
```

#### 模型管理
```bash
lms ls              # 列出所有下载的模型
lms ps              # 列出当前加载的模型
lms load <model>    # 加载模型
lms unload <model>  # 卸载模型
lms unload --all    # 卸载所有模型
```

#### 高级模型加载
```bash
# 带GPU配置加载
lms load <model> --gpu=max|auto|0.0-1.0

# 设置上下文长度
lms load <model> --context-length=4096

# 设置TTL（自动卸载时间）
lms load <model> --ttl 3600

# 设置模型别名
lms load <model> --identifier="custom-name"
```

#### 模型下载
```bash
lms get deepseek-r1                    # 通过关键词下载
lms get <hugging-face-url>             # 通过完整URL下载
lms get deepseek-r1 --mlx              # 仅下载MLX格式
```

## 2. Python SDK

### 安装
```bash
pip install lmstudio
```

### 基本用法

#### 获取当前模型
```python
import lmstudio as lms

# 便捷API
model = lms.llm()

# 完整API
client = lms.get_default_client()
model = client.llm.model()
```

#### 加载特定模型
```python
# 如果已加载则返回，否则加载
model = lms.llm("llama-3.2-1b-instruct")

# 强制加载新实例
client = lms.get_default_client()
model = client.llm.load_new_instance("llama-3.2-1b-instruct")
```

#### 卸载模型
```python
model = lms.llm()
model.unload()
```

#### 设置TTL
```python
# 加载模型并设置1小时TTL
model = lms.llm("llama-3.2-1b-instruct", ttl=3600)
```

## 3. OpenAI兼容API

### 基础端点
- **Base URL**: `http://localhost:1234`
- **Chat Completions**: `/v1/chat/completions`
- **Models**: `/v1/models`
- **Embeddings**: `/v1/embeddings`

### TTL功能（LM Studio 0.3.9+）
```json
{
  "model": "deepseek-r1-distill-qwen-7b",
  "messages": [...],
  "ttl": 300
}
```

### 工具调用（LM Studio 0.3.6+）
```json
{
  "model": "model-name",
  "messages": [...],
  "tools": [...],
  "tool_choice": "auto|none|required"
}
```

### 推测解码（LM Studio 0.3.10+）
```json
{
  "model": "main-model",
  "draft_model": "draft-model",
  "messages": [...]
}
```

## 4. 模型管理最佳实践

### 动态加载策略
1. **按需加载** - 仅在需要时加载模型
2. **TTL管理** - 设置合适的自动卸载时间
3. **资源监控** - 监控显存和内存使用

### TTL时间建议
- **视觉模型**: 300-600秒（5-10分钟）
- **文本模型**: 1800-3600秒（30-60分钟）
- **频繁使用**: 不设置TTL或设置较长时间

### 错误处理
- 模型加载失败时的重试机制
- 显存不足时的自动卸载策略
- 网络连接问题的处理

## 5. 高级功能

### MCP服务器集成
LM Studio 0.3.17+ 支持Model Context Protocol (MCP)服务器

### 流式响应
支持Server-Sent Events (SSE)流式响应

### 预设配置
支持通过API指定预设配置

## 6. 常见问题

### 模型路径
- **macOS/Linux**: `~/.lmstudio/models/`
- **Windows**: `%USERPROFILE%\.lmstudio\models\`

### 模型格式支持
- **GGUF** - 主要格式，支持llama.cpp
- **MLX** - Apple Silicon专用格式
- **SafeTensors** - 部分支持

### 系统要求
- **最小内存**: 8GB（推荐16GB+）
- **GPU**: 4GB+ VRAM（可选）
- **存储**: 根据模型大小而定

## 7. API响应格式

### 模型列表响应
```json
{
  "data": [
    {
      "id": "model-name",
      "object": "model",
      "capabilities": ["tool_use", "vision"]
    }
  ]
}
```

### 聊天完成响应
```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "model": "model-name",
  "choices": [...],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

## 8. 版本兼容性

- **TTL功能**: LM Studio 0.3.9+
- **工具调用**: LM Studio 0.3.6+
- **推测解码**: LM Studio 0.3.10+
- **MCP支持**: LM Studio 0.3.17+
- **Python SDK**: 1.5.0+

---

*最后更新: 2025-01-26*
*基于LM Studio官方文档整理*
