#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLIM评估测试脚本
"""

import json
from datetime import datetime

def calculate_glim_assessment():
    """模拟GLIM评估计算"""

    # 模拟用户填写的GLIM表单数据
    form_data = {
        "weight_loss": True,      # 非自主性体重减轻
        "low_bmi": False,         # 低BMI
        "muscle_loss": True,      # 肌肉质量减少
        "food_intake_reduction": True,    # 食物摄入减少或吸收障碍
        "disease_inflammation": False,    # 疾病负担或炎症
        "severe_weight_loss": False,      # 体重显著下降
        "severe_bmi": False,              # 低BMI（严重程度）
        "notes": "患者存在体重减轻和肌肉质量减少，伴有食物摄入不足"
    }

    # 执行GLIM计算逻辑
    phenotypic_criteria = []
    if form_data["weight_loss"]:
        phenotypic_criteria.append('非自主性体重减轻')
    if form_data["low_bmi"]:
        phenotypic_criteria.append('低BMI')
    if form_data["muscle_loss"]:
        phenotypic_criteria.append('肌肉质量减少')

    etiologic_criteria = []
    if form_data["food_intake_reduction"]:
        etiologic_criteria.append('食物摄入减少或吸收障碍')
    if form_data["disease_inflammation"]:
        etiologic_criteria.append('疾病负担或炎症')

    severe_criteria = []
    if form_data["severe_weight_loss"]:
        severe_criteria.append('体重显著下降')
    if form_data["severe_bmi"]:
        severe_criteria.append('低BMI')

    # 第一步：营养不良诊断
    has_phenotypic = len(phenotypic_criteria) > 0
    has_etiologic = len(etiologic_criteria) > 0
    is_malnutrition = has_phenotypic and has_etiologic

    # 第二步：严重程度评级（仅在确诊营养不良后进行）
    severity = None
    if is_malnutrition:
        severity = '重度营养不良' if len(severe_criteria) > 0 else '中度营养不良'

    # 构建完整结果
    results = {
        "form_data": {
            "phenotypic_criteria": {
                "weight_loss": form_data["weight_loss"],
                "low_bmi": form_data["low_bmi"],
                "muscle_loss": form_data["muscle_loss"]
            },
            "etiologic_criteria": {
                "food_intake_reduction": form_data["food_intake_reduction"],
                "disease_inflammation": form_data["disease_inflammation"]
            },
            "severity_criteria": {
                "severe_weight_loss": form_data["severe_weight_loss"],
                "severe_bmi": form_data["severe_bmi"]
            },
            "notes": form_data["notes"]
        },
        "calculation_results": {
            "diagnosis": {
                "is_malnutrition": is_malnutrition,
                "result": severity if is_malnutrition else '未诊断为营养不良'
            },
            "criteria_analysis": {
                "phenotypic_criteria": {
                    "count": len(phenotypic_criteria),
                    "met_criteria": phenotypic_criteria,
                    "sufficient": has_phenotypic
                },
                "etiologic_criteria": {
                    "count": len(etiologic_criteria),
                    "met_criteria": etiologic_criteria,
                    "sufficient": has_etiologic
                },
                "severity_criteria": {
                    "count": len(severe_criteria),
                    "met_criteria": severe_criteria,
                    "indicates_severe": len(severe_criteria) > 0
                }
            },
            "diagnostic_logic": {
                "step1_phenotypic_sufficient": has_phenotypic,
                "step1_etiologic_sufficient": has_etiologic,
                "step1_both_criteria_met": is_malnutrition,
                "step2_severity_assessment": severity
            }
        },
        "form_metadata": {
            "filled_date": datetime.now().strftime("%Y-%m-%d"),
            "filled_time": datetime.now().strftime("%H:%M:%S"),
            "calculation_timestamp": datetime.now().isoformat()
        }
    }

    return results

def print_glim_results(results):
    """打印GLIM评估结果"""
    print("\n" + "="*60)
    print("GLIM营养不良诊断标准评估结果")
    print("="*60)

    # 诊断结果
    diagnosis = results["calculation_results"]["diagnosis"]
    print(f"\n诊断结果: {diagnosis['result']}")
    print(f"是否营养不良: {'是' if diagnosis['is_malnutrition'] else '否'}")

    # 标准分析
    criteria = results["calculation_results"]["criteria_analysis"]

    print(f"\n第一步 - 表型诊断标准:")
    print(f"满足项数: {criteria['phenotypic_criteria']['count']}")
    print(f"满足标准: {', '.join(criteria['phenotypic_criteria']['met_criteria']) if criteria['phenotypic_criteria']['met_criteria'] else '无'}")
    print(f"是否充分: {'是' if criteria['phenotypic_criteria']['sufficient'] else '否'}")

    print(f"\n第一步 - 病因诊断标准:")
    print(f"满足项数: {criteria['etiologic_criteria']['count']}")
    print(f"满足标准: {', '.join(criteria['etiologic_criteria']['met_criteria']) if criteria['etiologic_criteria']['met_criteria'] else '无'}")
    print(f"是否充分: {'是' if criteria['etiologic_criteria']['sufficient'] else '否'}")

    if diagnosis['is_malnutrition']:
        print(f"\n第二步 - 严重程度评级标准:")
        print(f"满足项数: {criteria['severity_criteria']['count']}")
        print(f"满足标准: {', '.join(criteria['severity_criteria']['met_criteria']) if criteria['severity_criteria']['met_criteria'] else '无'}")
        print(f"提示重度: {'是' if criteria['severity_criteria']['indicates_severe'] else '否'}")

    # 诊断逻辑验证
    logic = results["calculation_results"]["diagnostic_logic"]
    print(f"\n诊断逻辑验证:")
    print(f"步骤1 - 表型标准充分: {'是' if logic['step1_phenotypic_sufficient'] else '否'}")
    print(f"步骤1 - 病因标准充分: {'是' if logic['step1_etiologic_sufficient'] else '否'}")
    print(f"步骤1 - 两项标准均满足: {'是' if logic['step1_both_criteria_met'] else '否'}")
    print(f"步骤2 - 严重程度评估: {logic['step2_severity_assessment'] or '不适用'}")

    # 备注
    if results["form_data"]["notes"]:
        print(f"\n备注: {results['form_data']['notes']}")

    print(f"\n评估时间: {results['form_metadata']['calculation_timestamp']}")
    print("="*60)

def main():
    """运行GLIM评估计算测试"""
    print("开始GLIM评估计算测试...")

    # 执行GLIM评估计算
    results = calculate_glim_assessment()

    # 打印结果
    print_glim_results(results)

    # 保存结果到JSON文件
    output_file = "glim_assessment_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n评估结果已保存到: {output_file}")

    return results

if __name__ == "__main__":
    main()
