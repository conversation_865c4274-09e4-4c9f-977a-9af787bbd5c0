#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LM Studio客户端功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.lm_studio_client import LMStudioClient, ModelManager
from loguru import logger

async def test_basic_connection():
    """测试基本连接"""
    print("=== 测试基本连接 ===")
    client = LMStudioClient()
    
    try:
        models = await client.get_available_models()
        print(f"✅ 连接成功，发现模型: {[m['id'] for m in models]}")
        return True
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def test_text_completion():
    """测试文本补全"""
    print("\n=== 测试文本补全 ===")
    client = LMStudioClient()
    
    messages = [
        {"role": "user", "content": "请简单介绍一下营养不良的定义。"}
    ]
    
    try:
        response = await client.chat_completion(messages)
        result = client.extract_response_text(response)
        print(f"✅ 文本补全成功:")
        print(f"回应: {result[:200]}...")
        return True
    except Exception as e:
        print(f"❌ 文本补全失败: {e}")
        return False

async def test_model_manager():
    """测试模型管理器"""
    print("\n=== 测试模型管理器 ===")
    manager = ModelManager()
    
    try:
        await manager.initialize()
        print("✅ 模型管理器初始化成功")
        
        # 测试华佗模型
        response = await manager.get_huatuo_response(
            "什么是GLIM标准？",
            "你是一位营养学专家。"
        )
        print(f"✅ 华佗模型回应: {response[:100]}...")
        return True
    except Exception as e:
        print(f"❌ 模型管理器测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试LM Studio客户端")
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    results = []
    
    # 运行测试
    results.append(await test_basic_connection())
    results.append(await test_text_completion())
    results.append(await test_model_manager())
    
    # 总结结果
    print(f"\n📊 测试结果: {sum(results)}/{len(results)} 通过")
    if all(results):
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查LM Studio服务状态")

if __name__ == "__main__":
    asyncio.run(main())

