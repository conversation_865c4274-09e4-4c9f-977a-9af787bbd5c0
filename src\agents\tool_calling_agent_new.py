#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新设计的工具调用智能体 - 按用户要求的批处理模式
"""
import json
import asyncio
from typing import Dict, Any, List, Optional, Union, Annotated
from datetime import datetime
from loguru import logger

# LangGraph核心导入
from langgraph.graph import StateGraph, END
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage

# 工具和客户端导入
from src.tools.agent_tools import NUTRITION_ANALYSIS_TOOLS, get_tools_description
from src.core.lm_studio_client import LMStudioClient


class ToolCallingAgentState(dict):
    """工具调用智能体状态"""
    messages: List[BaseMessage]
    user_profile: Dict[str, Any]
    uploaded_files: List[Dict[str, Any]]
    analysis_context: Dict[str, Any]
    analysis_plan: Dict[str, Any]
    tool_results: List[Dict[str, Any]]
    current_phase: str
    session_id: str


class HuatuoGPTToolCallingAgent:
    """重新设计的HuatuoGPT工具调用智能体 - 批处理模式"""
    
    def __init__(self):
        self.lm_client = LMStudioClient()
        self.tools = NUTRITION_ANALYSIS_TOOLS
        # 创建工具字典用于查找
        self.tools_dict = {tool.name: tool for tool in self.tools}
        self.workflow = self._build_workflow()
        
        # 编译工作流
        self.app = self.workflow.compile()
        
        logger.info("🤖 HuatuoGPT工具调用智能体初始化完成 (批处理模式)")
    
    def _build_workflow(self) -> StateGraph:
        """构建新的批处理工作流"""
        workflow = StateGraph(ToolCallingAgentState)
        
        # 添加节点
        workflow.add_node("analysis_planning", self._analysis_planning_node)  # 分析规划
        workflow.add_node("batch_tools", self._batch_tools_node)               # 批量工具执行
        workflow.add_node("final_analysis", self._final_analysis_node)         # 最终综合分析
        
        # 设置入口
        workflow.set_entry_point("analysis_planning")
        
        # 线性流程：分析规划 -> 批量工具执行 -> 最终分析 -> 结束
        workflow.add_conditional_edges(
            "analysis_planning",
            self._should_execute_tools,
            {
                "execute": "batch_tools",  # 需要执行工具
                "direct": "final_analysis" # 直接分析，无需工具
            }
        )
        
        workflow.add_edge("batch_tools", "final_analysis")
        workflow.add_edge("final_analysis", END)
        
        return workflow
    
    async def _analysis_planning_node(self, state: ToolCallingAgentState) -> ToolCallingAgentState:
        """分析规划节点 - HuatuoGPT分析所有数据并制定执行计划"""
        try:
            logger.info("🧠 HuatuoGPT开始分析规划阶段")
            
            # 构建规划阶段的系统提示词
            system_prompt = self._build_planning_system_prompt(state)
            
            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt}
            ] + [self._convert_message(msg) for msg in state["messages"]]
            
            # 调用HuatuoGPT制定分析计划
            response = await self._call_huatuogpt_for_planning(messages, state)
            
            if response:
                # 解析HuatuoGPT的分析计划
                analysis_plan = self._parse_analysis_plan(response)
                state["analysis_plan"] = analysis_plan
                
                # 添加规划消息到历史
                planning_message = AIMessage(content=f"📋 分析计划：{response.get('content', '')}")
                state["messages"].append(planning_message)
                
                logger.info(f"✅ 分析计划制定完成，需要执行 {len(analysis_plan.get('tools', []))} 个工具")
            
            return state
            
        except Exception as e:
            logger.error(f"分析规划节点执行失败: {e}")
            raise e
    
    async def _batch_tools_node(self, state: ToolCallingAgentState) -> ToolCallingAgentState:
        """批量工具执行节点 - 串行执行所有计划的工具"""
        try:
            logger.info("🔧 开始批量工具执行阶段")
            
            analysis_plan = state.get("analysis_plan", {})
            tools_to_execute = analysis_plan.get("tools", [])
            
            if not tools_to_execute:
                logger.info("⚠️ 没有需要执行的工具")
                return state
            
            tool_results = []
            
            # 串行执行所有工具
            for i, tool_spec in enumerate(tools_to_execute, 1):
                tool_name = tool_spec.get("name")
                tool_args = tool_spec.get("args", {})
                
                logger.info(f"🔧 执行工具 {i}/{len(tools_to_execute)}: {tool_name}")
                
                # 验证工具执行的合理性
                if not self._validate_tool_execution(tool_name, tool_args, state):
                    logger.error(f"❌ 工具 {tool_name} 数据验证失败，跳过执行")
                    tool_results.append({
                        "tool": tool_name,
                        "result": f"数据验证失败：{tool_name} 工具缺少必要的数据支撑",
                        "success": False
                    })
                    continue
                
                # 增强工具参数
                enhanced_args = self._enhance_tool_arguments(tool_name, tool_args, state)
                
                # 查找并执行工具
                if tool_name in self.tools_dict:
                    tool_obj = self.tools_dict[tool_name]
                    
                    try:
                        # 执行工具
                        if hasattr(tool_obj, 'ainvoke'):
                            result = await tool_obj.ainvoke(enhanced_args)
                        else:
                            result = tool_obj.invoke(enhanced_args)
                        
                        tool_results.append({
                            "tool": tool_name,
                            "result": result,
                            "success": True
                        })
                        
                        logger.info(f"✅ 工具 {tool_name} 执行成功")
                        
                    except Exception as e:
                        error_msg = f"工具 {tool_name} 执行失败: {str(e)}"
                        logger.error(error_msg)
                        tool_results.append({
                            "tool": tool_name,
                            "result": error_msg,
                            "success": False
                        })
                else:
                    error_msg = f"未找到工具: {tool_name}"
                    logger.error(error_msg)
                    tool_results.append({
                        "tool": tool_name,
                        "result": error_msg,
                        "success": False
                    })
            
            # 将所有工具结果保存到状态
            state["tool_results"] = tool_results
            
            # 生成工具结果汇总消息
            summary_message = self._create_tools_summary_message(tool_results)
            state["messages"].append(summary_message)
            
            logger.info(f"✅ 批量工具执行完成，共执行 {len(tool_results)} 个工具")
            
            return state
            
        except Exception as e:
            logger.error(f"批量工具执行节点失败: {e}")
            raise e
    
    async def _final_analysis_node(self, state: ToolCallingAgentState) -> ToolCallingAgentState:
        """最终分析节点 - HuatuoGPT基于所有结果进行综合分析"""
        try:
            logger.info("🎯 开始最终综合分析阶段")
            
            # 构建最终分析的系统提示词
            system_prompt = self._build_final_analysis_system_prompt(state)
            
            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt}
            ] + [self._convert_message(msg) for msg in state["messages"]]
            
            # 调用HuatuoGPT进行最终综合分析（不使用工具）
            response = await self._call_huatuogpt_for_final_analysis(messages, state)
            
            if response:
                # 添加最终分析结果到消息历史
                final_message = AIMessage(content=response.get("content", "分析完成"))
                state["messages"].append(final_message)
                
                logger.info("✅ 最终综合分析完成")
            
            return state
            
        except Exception as e:
            logger.error(f"最终分析节点执行失败: {e}")
            raise e
    
    def _should_execute_tools(self, state: ToolCallingAgentState) -> str:
        """判断是否需要执行工具"""
        analysis_plan = state.get("analysis_plan", {})
        tools_to_execute = analysis_plan.get("tools", [])
        
        if tools_to_execute:
            logger.info(f"🔍 需要执行 {len(tools_to_execute)} 个工具")
            return "execute"
        else:
            logger.info("🔍 无需执行工具，直接进行最终分析")
            return "direct"
    
    def _build_planning_system_prompt(self, state: ToolCallingAgentState) -> str:
        """构建分析规划阶段的系统提示词"""
        user_profile = state.get("user_profile", {})
        uploaded_files = state.get("uploaded_files", [])
        
        # 分析可用工具（已移除 ingest_and_identify_data）
        available_tools = []
        for tool in self.tools:
            available_tools.append(f"- {tool.name}: {tool.description}")
        
        # 构建患者信息，只显示有值的项目
        patient_info_lines = []
        patient_info_lines.append(f"- 年龄：{user_profile.get('age', '未提供')}岁")
        patient_info_lines.append(f"- 性别：{user_profile.get('gender', '未提供')}")
        
        # 只有在身高有意义的值时才显示
        height = user_profile.get('height')
        if height is not None:
            try:
                height_val = float(height) if height != '' else 0
                if height_val > 0:
                    patient_info_lines.append(f"- 身高：{height}cm")
            except (ValueError, TypeError):
                pass  # 忽略无效的身高值
        
        # 只有在体重有意义的值时才显示 - 检查多个可能的字段
        weight = user_profile.get('weight') or user_profile.get('current_weight')
        if weight is not None:
            try:
                weight_val = float(weight) if weight != '' else 0
                if weight_val > 0:
                    patient_info_lines.append(f"- 体重：{weight}kg")
            except (ValueError, TypeError):
                pass  # 忽略无效的体重值
        
        patient_info_text = '\n'.join(patient_info_lines)
        
        return f"""🔧 工具选择任务

**患者信息**: 
{patient_info_text}

**上传文件**: 
{self._analyze_uploaded_files(uploaded_files)}

**可用工具**:
{chr(10).join(available_tools)}

**工具匹配规则**:
- 图像文件 → analyze_facial_nutrition
- Excel/CSV文件(含BIA专业数据) → calculate_bia_metrics  
- GLIM问卷数据 → assess_glim_criteria

⚠️ **重要**: 只有真正上传了对应类型的文件才能调用相关工具！

**输出格式**:
```
TOOLS_PLAN:
[{{"name": "工具名", "args": {{}}}}, ...]
```

如果无合适数据，输出: TOOLS_PLAN: []"""

    def _analyze_uploaded_files(self, uploaded_files: List[Dict[str, Any]]) -> str:
        """分析上传文件并生成描述"""
        if not uploaded_files:
            return "无文件上传"
        
        file_list = []
        for file_info in uploaded_files:
            filename = file_info.get("filename", "unknown")
            size_kb = round(file_info.get("size", 0) / 1024, 1)
            
            if any(ext in filename.lower() for ext in ['.jpg', '.jpeg', '.png', '.bmp']):
                file_type = "图像"
            elif any(ext in filename.lower() for ext in ['.xlsx', '.xls', '.csv']):
                file_type = "数据表格"
            else:
                file_type = "其他"
            
            file_list.append(f"- {filename} ({file_type}, {size_kb}KB)")
        
        return '\n'.join(file_list)
    
    async def _call_huatuogpt_for_planning(self, messages: List[Dict[str, Any]], state: ToolCallingAgentState) -> Dict[str, Any]:
        """调用HuatuoGPT进行分析规划"""
        try:
            # 记录关键输入信息
            user_profile = state.get("user_profile", {})
            uploaded_files = state.get("uploaded_files", [])
            logger.info(f"👤 患者信息: 年龄{user_profile.get('age', '未知')}岁, 性别{user_profile.get('gender', '未知')}")
            logger.info(f"📁 上传文件数量: {len(uploaded_files)}")
            
            # 构建简洁的工具选择提示词（不使用复杂的系统提示词）
            prompt = self._build_planning_system_prompt(state)
            
            # 使用华佗GPT进行工具选择
            logger.info("🤖 调用HuatuoGPT进行简洁工具选择...")
            response = await asyncio.get_event_loop().run_in_executor(
                None, self.lm_client.call_huatuogpt, prompt
            )
            
            if response and response.get("success"):
                # 对于工具选择，直接使用分析结果，不需要复杂的提取
                content = response.get("analysis", "")
                logger.info("✅ HuatuoGPT工具选择完成")
                logger.info(f"📊 选择结果长度: {len(content)} 字符")
                
                return {
                    "content": content,
                    "success": True
                }
            else:
                logger.error(f"❌ HuatuoGPT工具选择失败: {response}")
                return {"content": "工具选择失败", "success": False}
                
        except Exception as e:
            logger.error(f"💥 调用HuatuoGPT工具选择异常: {e}")
            return {"content": f"工具选择异常: {str(e)}", "success": False}
    
    def _parse_analysis_plan(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """解析HuatuoGPT返回的分析计划"""
        content = response.get("content", "")
        logger.info("🔍 开始解析HuatuoGPT分析计划...")
        
        # 查找 TOOLS_PLAN: 部分
        import re
        tools_plan_match = re.search(r'TOOLS_PLAN:\s*(\[.*?\])', content, re.DOTALL)
        
        if tools_plan_match:
            try:
                tools_json = tools_plan_match.group(1)
                logger.info(f"🔧 找到工具计划JSON: {tools_json}")
                
                tools_list = json.loads(tools_json)
                logger.info(f"✅ 成功解析工具计划，共 {len(tools_list)} 个工具:")
                
                for i, tool in enumerate(tools_list, 1):
                    tool_name = tool.get("name", "unknown")
                    logger.info(f"  {i}. {tool_name}")
                    
                    # 特别检查BIA工具调用
                    if tool_name == "calculate_bia_metrics":
                        logger.warning("⚠️ 检测到BIA工具调用 - 请确认是否有BIA数据支撑")
                        bia_data = tool.get("args", {}).get("bia_data", {})
                        if not bia_data or bia_data == {}:
                            logger.error("❌ BIA工具调用无数据支撑！这可能是错误的规划决策")
                
                return {
                    "tools": tools_list,
                    "plan_text": content
                }
            except json.JSONDecodeError as e:
                logger.error(f"❌ 解析工具计划JSON失败: {e}")
                logger.error(f"🔍 尝试解析的JSON: {tools_json}")
        
        # 如果解析失败，尝试简单模式
        logger.warning("⚠️ 未找到TOOLS_PLAN格式，尝试智能解析...")
        return self._intelligent_parse_plan(content)
    
    def _intelligent_parse_plan(self, content: str) -> Dict[str, Any]:
        """智能解析分析计划（备用方案）"""
        tools = []
        
        # 检测提到的工具名称
        tool_names = [tool.name for tool in self.tools]
        
        for tool_name in tool_names:
            if tool_name in content:
                tools.append({
                    "name": tool_name,
                    "args": {}  # 使用默认参数，会在执行时自动增强
                })
        
        return {
            "tools": tools,
            "plan_text": content
        }
    
    def _create_tools_summary_message(self, tool_results: List[Dict[str, Any]]) -> AIMessage:
        """创建工具执行结果汇总消息"""
        summary_parts = ["🔧 工具执行结果汇总：\n"]
        
        for i, result in enumerate(tool_results, 1):
            tool_name = result.get("tool", "unknown")
            success = result.get("success", False)
            result_content = result.get("result", "")
            
            status = "✅ 成功" if success else "❌ 失败"
            summary_parts.append(f"{i}. {tool_name} - {status}")
            
            if success:
                # 截取结果的前500字符避免过长
                if len(str(result_content)) > 500:
                    summary_parts.append(f"   结果: {str(result_content)[:500]}...")
                else:
                    summary_parts.append(f"   结果: {result_content}")
            else:
                summary_parts.append(f"   错误: {result_content}")
            
            summary_parts.append("")  # 空行分隔
        
        summary_parts.append("📊 所有工具执行完成，准备进行综合分析...")
        
        return AIMessage(content="\n".join(summary_parts))
    
    def _build_final_analysis_system_prompt(self, state: ToolCallingAgentState) -> str:
        """构建最终分析阶段的系统提示词"""
        user_profile = state.get("user_profile", {})
        tool_results = state.get("tool_results", [])
        
        # 构建患者信息，只显示有值的项目
        patient_info_lines = []
        patient_info_lines.append(f"- 年龄：{user_profile.get('age', '未提供')}岁")
        patient_info_lines.append(f"- 性别：{user_profile.get('gender', '未提供')}")
        
        # 只有在身高有意义的值时才显示
        height = user_profile.get('height')
        if height is not None:
            try:
                height_val = float(height) if height != '' else 0
                if height_val > 0:
                    patient_info_lines.append(f"- 身高：{height}cm")
            except (ValueError, TypeError):
                pass  # 忽略无效的身高值
        
        # 只有在体重有意义的值时才显示 - 检查多个可能的字段
        weight = user_profile.get('weight') or user_profile.get('current_weight')
        if weight is not None:
            try:
                weight_val = float(weight) if weight != '' else 0
                if weight_val > 0:
                    patient_info_lines.append(f"- 体重：{weight}kg")
            except (ValueError, TypeError):
                pass  # 忽略无效的体重值
            
        # 只有在BMI有意义的值时才显示
        bmi = user_profile.get('bmi')
        if bmi and bmi != '未计算' and bmi != 0:
            patient_info_lines.append(f"- BMI：{bmi}")
        
        patient_info_text = '\n'.join(patient_info_lines)
        
        return f"""你是一位资深的临床营养专家AI助手，现在处于【最终综合分析阶段】。

🎯 **当前任务**
基于所有工具的分析结果，进行专业的营养状况综合评估，并提供临床建议。

👤 **患者信息**
{patient_info_text}

📊 **可用分析数据**
共完成 {len(tool_results)} 项分析，所有结果已在对话历史中提供。

🏥 **专业要求**
1. **综合评估**: 整合所有分析结果，给出营养状况的整体判断
2. **临床诊断**: 基于GLIM标准等国际指南进行诊断
3. **干预建议**: 提供具体的营养干预和治疗建议  
4. **随访计划**: 建议后续监测和复查方案

📝 **输出格式**
请按以下结构提供专业报告：

## 🏥 营养状况综合评估报告

### 📊 分析数据汇总
[简要汇总各项分析的关键发现]

### ⚕️ 营养状况诊断
[基于所有数据的营养状况判断]

### 🎯 临床建议
[具体的营养干预建议]

### 📅 随访计划
[后续监测建议]

⚠️ **重要**：
- 基于循证医学原则
- 明确诊断级别（正常/轻度/中度/重度营养不良）
- 提供可操作的具体建议
- 说明分析的局限性"""

    async def _call_huatuogpt_for_final_analysis(self, messages: List[Dict[str, Any]], state: ToolCallingAgentState) -> Dict[str, Any]:
        """调用HuatuoGPT进行最终综合分析"""
        try:
            # 将messages转换为单个prompt，使用同步调用
            # 构建完整的提示词
            full_prompt = ""
            for msg in messages:
                if msg["role"] == "system":
                    full_prompt += f"System: {msg['content']}\n\n"
                elif msg["role"] == "user":
                    full_prompt += f"User: {msg['content']}\n\n"
                elif msg["role"] == "assistant":
                    full_prompt += f"Assistant: {msg['content']}\n\n"
            
            # 使用同步的call_huatuogpt方法
            response = self.lm_client.call_huatuogpt(
                prompt=full_prompt,
                temperature=0.2,  # 稍高温度增加创造性
                timeout=180,      # 更长超时，因为需要综合分析
                max_tokens=2000   # 允许更长的回复
            )
            
            if response and response.get("success"):
                return {
                    "content": response.get("analysis", ""),
                    "success": True
                }
            else:
                logger.error(f"HuatuoGPT最终分析调用失败: {response}")
                return {"content": "最终分析失败", "success": False}
                
        except Exception as e:
            logger.error(f"调用HuatuoGPT最终分析失败: {e}")
            return {"content": f"最终分析调用异常: {str(e)}", "success": False}
    
    def _validate_tool_execution(self, tool_name: str, tool_args: dict, state: ToolCallingAgentState) -> bool:
        """验证工具执行是否有必要的数据支撑"""
        logger.info(f"🔍 验证工具 {tool_name} 的数据支撑...")
        
        if tool_name == "calculate_bia_metrics":
            # 检查是否有BIA数据文件上传
            uploaded_files = state.get("uploaded_files", [])
            has_bia_file = False
            
            for file_info in uploaded_files:
                filename = file_info.get("filename", "").lower()
                mimetype = file_info.get("mimetype", "")
                
                # 检查是否是Excel/CSV文件（可能包含BIA数据）
                if (filename.endswith(('.xlsx', '.xls', '.csv')) or 
                    'spreadsheet' in mimetype or 
                    'csv' in mimetype):
                    has_bia_file = True
                    logger.info(f"✅ 找到可能的BIA数据文件: {filename}")
                    break
            
            if not has_bia_file:
                logger.error("❌ BIA工具验证失败: 没有找到Excel/CSV数据文件")
                logger.error("💡 建议: BIA工具只应在上传了包含体成分数据的Excel/CSV文件时调用")
                return False
        
        elif tool_name == "analyze_facial_nutrition":
            # 检查是否有面部图像文件
            uploaded_files = state.get("uploaded_files", [])
            has_image_file = False
            
            for file_info in uploaded_files:
                filename = file_info.get("filename", "").lower()
                mimetype = file_info.get("mimetype", "")
                
                if (any(ext in filename for ext in ['.jpg', '.jpeg', '.png', '.bmp']) or 
                    'image' in mimetype):
                    has_image_file = True
                    logger.info(f"✅ 找到面部图像文件: {filename}")
                    break
            
            if not has_image_file:
                logger.error("❌ 面部分析工具验证失败: 没有找到图像文件")
                return False
                
        elif tool_name == "assess_glim_criteria":
            # 检查是否有GLIM相关数据
            # 这个工具通常依赖于用户填写的问卷数据
            # 暂时允许执行，因为可能依赖用户档案信息
            pass
        
        logger.info(f"✅ 工具 {tool_name} 数据验证通过")
        return True
    
    def _enhance_tool_arguments(self, tool_name: str, tool_args: dict, state: ToolCallingAgentState) -> dict:
        """自动增强工具参数 - 从状态中补充必要信息"""
        enhanced_args = tool_args.copy()
        user_profile = state.get("user_profile", {})
        
        if tool_name == "analyze_facial_nutrition":
            # 补充面部图像信息 - 支持多种存储格式
            uploaded_files = state.get("uploaded_files", []) + state.get("uploads_queue", [])
            for file_info in uploaded_files:
                filename = file_info.get("filename", "").lower()
                mimetype = file_info.get("mimetype", "").lower()
                
                # 检查是否为图像文件
                is_image = (
                    any(ext in filename for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.gif']) or
                    any(mime in mimetype for mime in ['image/jpeg', 'image/png', 'image/bmp', 'image/webp', 'image/gif'])
                )
                
                if is_image:
                    # 总是使用实际的文件数据，覆盖智能推断的基本信息
                    enhanced_args["image_info"] = file_info
                    logger.info(f"🖼️ 自动补充图像信息: {filename} (mime: {mimetype})")
                    logger.info(f"🔍 图像数据键: {list(file_info.keys())}")
                    break
            
            # 补充患者信息
            if "patient_profile" not in enhanced_args:
                enhanced_args["patient_profile"] = user_profile
        
        elif tool_name == "calculate_bia_metrics":
            if "patient_info" not in enhanced_args:
                enhanced_args["patient_info"] = user_profile
        
        elif tool_name == "assess_glim_criteria":
            if "patient_data" not in enhanced_args:
                enhanced_args["patient_data"] = user_profile
        
        return enhanced_args
    
    def _convert_message(self, msg: BaseMessage) -> Dict[str, Any]:
        """将LangChain消息转换为API格式"""
        if isinstance(msg, HumanMessage):
            return {"role": "user", "content": msg.content}
        elif isinstance(msg, AIMessage):
            return {"role": "assistant", "content": msg.content}
        else:
            return {"role": "system", "content": str(msg.content)}
    
    async def run_conversation(
        self, 
        user_input: str,
        user_profile: Dict[str, Any] = None,
        uploaded_files: List[Dict[str, Any]] = None,
        session_id: str = None
    ) -> Dict[str, Any]:
        """运行新的批处理工具调用智能体对话"""
        try:
            # 初始化状态
            initial_state = ToolCallingAgentState({
                "messages": [HumanMessage(content=user_input)],
                "user_profile": user_profile or {},
                "uploaded_files": uploaded_files or [],
                "analysis_context": {},
                "analysis_plan": {},
                "tool_results": [],
                "current_phase": "batch_analysis",
                "session_id": session_id or f"session_{datetime.now().timestamp()}"
            })
            
            # 运行工作流
            final_state = await self.app.ainvoke(initial_state)
            
            # 调试：检查final_state的键
            logger.info(f"🔍 工作流完成后的状态键: {list(final_state.keys())}")
            
            # 获取最终回复
            if final_state.get("messages"):
                last_message = final_state["messages"][-1]
                final_response = last_message.content
            else:
                final_response = "分析完成，但未获取到结果。"
            
            # 安全地获取session_id
            result_session_id = final_state.get("session_id")
            if not result_session_id:
                result_session_id = session_id or f"session_{datetime.now().timestamp()}"
                logger.warning(f"⚠️ 工作流中丢失了session_id，使用备用值: {result_session_id}")
            
            return {
                "success": True,
                "response": final_response,
                "messages": final_state.get("messages", []),
                "session_id": result_session_id
            }
            
        except Exception as e:
            logger.error(f"批处理工具调用智能体运行失败: {e}")
            raise e


# 全局智能体实例
_tool_calling_agent = None

def get_tool_calling_agent() -> HuatuoGPTToolCallingAgent:
    """获取工具调用智能体单例"""
    global _tool_calling_agent
    if _tool_calling_agent is None:
        _tool_calling_agent = HuatuoGPTToolCallingAgent()
    return _tool_calling_agent
