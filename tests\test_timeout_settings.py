#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超时设置配置
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from config.settings import settings
    print("✅ 成功导入配置文件")
    
    # 测试超时配置
    timeout_config = settings.get_timeout_config()
    print("\n📊 当前超时配置:")
    for key, value in timeout_config.items():
        minutes = value // 60
        seconds = value % 60
        print(f"  {key}: {value}秒 ({minutes}分{seconds}秒)")
    
    print(f"\n🔧 主要超时设置:")
    print(f"  对话超时: {settings.CONVERSATION_TIMEOUT}秒 (10分钟)")
    print(f"  视觉分析超时: {settings.VISION_ANALYSIS_TIMEOUT}秒 (10分钟)")
    print(f"  综合分析超时: {settings.COMPREHENSIVE_ANALYSIS_TIMEOUT}秒 (10分钟)")
    
except ImportError as e:
    print(f"❌ 配置文件导入失败: {e}")
    print("使用默认超时设置: 600秒 (10分钟)")

try:
    from src.core.lm_studio_client import LMStudioClient
    print("\n✅ 成功导入LM Studio客户端")
    
    # 测试客户端初始化
    client = LMStudioClient()
    print("✅ LM Studio客户端初始化成功")
    
except ImportError as e:
    print(f"❌ LM Studio客户端导入失败: {e}")

print("\n🚀 超时设置配置完成！")
print("现在所有网络请求和模型推理都将使用10分钟超时。")
