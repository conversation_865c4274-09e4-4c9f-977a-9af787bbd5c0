#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理器（兼容性层）
为了向后兼容，此文件重定向到新的统一提示词管理系统
"""

import os
from typing import Dict, Any, Optional
from loguru import logger

# 重定向到新的统一提示词管理系统
try:
    from .unified_prompt_manager import (
        get_prompt_manager as get_unified_manager,
        get_system_prompt,
        get_facial_prompt,
        get_glim_prompt,
        get_comprehensive_prompt
    )
    UNIFIED_SYSTEM_AVAILABLE = True
    logger.info("✅ 已重定向到统一提示词管理系统")
except ImportError as e:
    UNIFIED_SYSTEM_AVAILABLE = False
    logger.error(f"❌ 无法导入统一提示词管理系统: {e}")

class PromptManager:
    """提示词配置管理器（兼容性类）"""
    
    def __init__(self):
        self.config_loaded = UNIFIED_SYSTEM_AVAILABLE
        
        if UNIFIED_SYSTEM_AVAILABLE:
            self.unified_manager = get_unified_manager()
            logger.info("🎯 提示词管理器已连接统一系统")
        else:
            logger.warning("⚠️ 降级到传统提示词管理模式")
            self._load_legacy_prompts()
    
    def _load_legacy_prompts(self):
        """加载传统提示词（降级模式）"""
        # 传统的提示词加载逻辑保持不变
        try:
            # 导入视觉分析提示词
            try:
                from config.vision_analysis_prompts import (
                    VISION_ANALYSIS_PROMPT,
                    get_contextual_vision_prompt,
                    COMPLETE_VISION_ANALYSIS_PROMPT
                )
                self.vision_prompts = {
                    'base_prompt': VISION_ANALYSIS_PROMPT,
                    'complete_prompt': COMPLETE_VISION_ANALYSIS_PROMPT,
                    'contextual_generator': get_contextual_vision_prompt
                }
                logger.info("✅ 视觉分析提示词加载成功")
            except ImportError as e:
                logger.error(f"❌ 视觉分析提示词加载失败: {e}")
                self.vision_prompts = {}
            
            # 导入综合分析提示词
            try:
                from config.comprehensive_analysis_prompts import (
                    get_comprehensive_analysis_system_prompt,
                    build_comprehensive_analysis_prompt,
                    get_enhanced_prompt_for_special_population
                )
                self.comprehensive_prompts = {
                    'system_prompt': get_comprehensive_analysis_system_prompt(),
                    'prompt_builder': build_comprehensive_analysis_prompt,
                    'enhancement_generator': get_enhanced_prompt_for_special_population
                }
                logger.info("✅ 综合分析提示词加载成功")
            except ImportError as e:
                logger.error(f"❌ 综合分析提示词加载失败: {e}")
                self.comprehensive_prompts = {}
            
            self.config_loaded = True
            logger.info("🎯 传统提示词管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 传统提示词管理器初始化失败: {e}")
            self.config_loaded = False
    
    def get_vision_prompt(self, patient_info: Optional[Dict[str, Any]] = None, instance_id: int = 1) -> str:
        """获取视觉分析提示词"""
        if UNIFIED_SYSTEM_AVAILABLE:
            # 使用统一系统
            return get_facial_prompt("system")
        
        # 降级模式
        if not self.config_loaded or not self.vision_prompts:
            error_msg = "❌ 视觉分析提示词配置未加载。请检查 config/vision_analysis_prompts.py 文件是否存在且格式正确。"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        try:
            contextual_generator = self.vision_prompts.get('contextual_generator')
            if contextual_generator:
                return contextual_generator(patient_info, instance_id)
            else:
                base_prompt = self.vision_prompts.get('base_prompt')
                if not base_prompt:
                    error_msg = "❌ 视觉分析基础提示词为空。请检查 config/vision_analysis_prompts.py 中的 VISION_ANALYSIS_PROMPT 配置。"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                return base_prompt
        except Exception as e:
            error_msg = f"❌ 生成视觉提示词失败: {e}。请检查 config/vision_analysis_prompts.py 配置文件。"
            logger.error(error_msg)
            raise ValueError(error_msg)
    
    def get_comprehensive_analysis_prompt(
        self, 
        patient_info: Dict[str, Any], 
        collected_data: Dict[str, Any],
        analysis_timestamp: Optional[str] = None
    ) -> str:
        """获取综合分析提示词"""
        if UNIFIED_SYSTEM_AVAILABLE:
            # 使用统一系统
            return get_comprehensive_prompt(
                "simple",
                patient_info=patient_info,
                collected_data=collected_data,
                analysis_timestamp=analysis_timestamp
            )
        
        # 降级模式
        if not self.config_loaded or not self.comprehensive_prompts:
            logger.warning("⚠️ 综合分析提示词未加载，返回默认提示词")
            return self._get_default_comprehensive_prompt(patient_info, collected_data)
        
        try:
            prompt_builder = self.comprehensive_prompts.get('prompt_builder')
            enhancement_generator = self.comprehensive_prompts.get('enhancement_generator')
            
            if prompt_builder:
                # 构建基础提示词
                base_prompt = prompt_builder(patient_info, collected_data, analysis_timestamp)
                
                # 添加特殊人群增强
                if enhancement_generator:
                    enhanced_prompt = enhancement_generator(base_prompt, patient_info)
                    return enhanced_prompt
                else:
                    return base_prompt
            else:
                return self._get_default_comprehensive_prompt(patient_info, collected_data)
                
        except Exception as e:
            logger.error(f"❌ 生成综合分析提示词失败: {e}")
            return self._get_default_comprehensive_prompt(patient_info, collected_data)
    
    def get_system_prompt(self, prompt_type: str = "comprehensive") -> str:
        """获取系统提示词"""
        if UNIFIED_SYSTEM_AVAILABLE:
            # 使用统一系统
            if prompt_type == "comprehensive":
                return get_comprehensive_prompt("system")
            elif prompt_type == "facial" or prompt_type == "vision":
                return get_facial_prompt("system")
            elif prompt_type == "glim":
                return get_glim_prompt("system")
            else:
                logger.warning(f"⚠️ 未知的提示词类型 {prompt_type}，使用综合分析")
                return get_comprehensive_prompt("system")
        
        # 降级模式
        if prompt_type == "comprehensive" and self.comprehensive_prompts:
            return self.comprehensive_prompts.get('system_prompt', '')
        elif prompt_type == "vision" and self.vision_prompts:
            return self.vision_prompts.get('base_prompt', '')
        else:
            logger.warning(f"⚠️ 未找到类型为 {prompt_type} 的系统提示词")
            return ''
    
    def _get_default_comprehensive_prompt(self, patient_info: Dict[str, Any], collected_data: Dict[str, Any]) -> str:
        """默认综合分析提示词"""
        import json
        
        return f"""请基于以下收集到的多模态数据，进行综合的营养状况评估。

## 患者基本信息
{json.dumps(patient_info, ensure_ascii=False, indent=2)}

## 收集到的评估数据
{json.dumps(collected_data, ensure_ascii=False, indent=2)}

请进行专业分析并按照以下格式输出：

## 🎯 营养状况评估
**诊断结论**：[具体诊断结果]

## 💡 支持证据
[列出支持诊断的关键证据点]

## 🏥 临床建议
[提供具体的营养干预建议]

## ⚠️ 重要提醒
这是基于AI技术的初步筛查结果，请咨询专业医生获取详细诊断。"""
    
    def validate_prompt_quality(self) -> Dict[str, Any]:
        """验证提示词质量"""
        if UNIFIED_SYSTEM_AVAILABLE:
            # 使用统一系统的验证
            validation_result = self.unified_manager.validate_prompts()
            return {
                "overall_status": "excellent" if all(validation_result.values()) else "good",
                "vision_prompts": {
                    "loaded": validation_result.get("facial_system", False),
                    "quality": "good" if validation_result.get("facial_system", False) else "poor"
                },
                "comprehensive_prompts": {
                    "loaded": validation_result.get("comprehensive_system", False), 
                    "quality": "good" if validation_result.get("comprehensive_system", False) else "poor"
                },
                "unified_system": True,
                "recommendations": [] if all(validation_result.values()) else ["建议检查提示词配置"]
            }
        
        # 降级模式验证逻辑保持不变
        validation_result = {
            "overall_status": "unknown",
            "vision_prompts": {
                "loaded": bool(getattr(self, 'vision_prompts', {})),
                "quality": "unknown"
            },
            "comprehensive_prompts": {
                "loaded": bool(getattr(self, 'comprehensive_prompts', {})),
                "quality": "unknown"
            },
            "unified_system": False,
            "recommendations": []
        }
        
        # 检查视觉提示词
        if hasattr(self, 'vision_prompts') and self.vision_prompts:
            base_prompt = self.vision_prompts.get('base_prompt', '')
            if len(base_prompt) > 2000:
                validation_result["vision_prompts"]["quality"] = "good"
            elif len(base_prompt) > 500:
                validation_result["vision_prompts"]["quality"] = "fair"
            else:
                validation_result["vision_prompts"]["quality"] = "poor"
                validation_result["recommendations"].append("视觉提示词过短，可能影响分析质量")
        
        # 检查综合分析提示词
        if hasattr(self, 'comprehensive_prompts') and self.comprehensive_prompts:
            system_prompt = self.comprehensive_prompts.get('system_prompt', '')
            if len(system_prompt) > 1000:
                validation_result["comprehensive_prompts"]["quality"] = "good"
            elif len(system_prompt) > 300:
                validation_result["comprehensive_prompts"]["quality"] = "fair"
            else:
                validation_result["comprehensive_prompts"]["quality"] = "poor"
                validation_result["recommendations"].append("综合分析提示词过短，可能影响诊断质量")
        
        # 整体评估
        if (validation_result["vision_prompts"]["quality"] == "good" and 
            validation_result["comprehensive_prompts"]["quality"] == "good"):
            validation_result["overall_status"] = "excellent"
        elif (validation_result["vision_prompts"]["loaded"] and 
              validation_result["comprehensive_prompts"]["loaded"]):
            validation_result["overall_status"] = "good"
        else:
            validation_result["overall_status"] = "needs_improvement"
            validation_result["recommendations"].append("建议检查提示词配置文件的完整性")
        
        return validation_result
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """获取提示词统计信息"""
        if UNIFIED_SYSTEM_AVAILABLE:
            # 使用统一系统的统计
            stats = self.unified_manager.get_prompt_statistics()
            stats["unified_system"] = True
            stats["compatibility_mode"] = False
            return stats
        
        # 降级模式统计
        stats = {
            "config_loaded": self.config_loaded,
            "total_prompt_types": 0,
            "vision_prompts_available": bool(getattr(self, 'vision_prompts', {})),
            "comprehensive_prompts_available": bool(getattr(self, 'comprehensive_prompts', {})),
            "unified_system": False,
            "compatibility_mode": True
        }
        
        if hasattr(self, 'vision_prompts') and self.vision_prompts:
            stats["total_prompt_types"] += 1
            stats["vision_prompt_length"] = len(self.vision_prompts.get('base_prompt', ''))
        
        if hasattr(self, 'comprehensive_prompts') and self.comprehensive_prompts:
            stats["total_prompt_types"] += 1
            stats["comprehensive_system_prompt_length"] = len(
                self.comprehensive_prompts.get('system_prompt', '')
            )
        
        return stats


# 全局提示词管理器实例
prompt_manager = PromptManager()


def get_vision_analysis_prompt(patient_info: Optional[Dict[str, Any]] = None, instance_id: int = 1) -> str:
    """便捷函数：获取视觉分析提示词"""
    return prompt_manager.get_vision_prompt(patient_info, instance_id)


def get_comprehensive_analysis_prompt(
    patient_info: Dict[str, Any], 
    collected_data: Dict[str, Any],
    analysis_timestamp: Optional[str] = None
) -> str:
    """便捷函数：获取综合分析提示词"""
    return prompt_manager.get_comprehensive_analysis_prompt(
        patient_info, collected_data, analysis_timestamp
    )


# 测试函数
def test_prompt_manager():
    """测试提示词管理器"""
    print("🧪 测试提示词管理器（兼容性层）")
    print("=" * 50)
    
    # 获取统计信息
    stats = prompt_manager.get_prompt_statistics()
    print(f"📊 统计信息: {stats}")
    
    # 验证质量
    validation = prompt_manager.validate_prompt_quality()
    print(f"✅ 质量验证: {validation}")
    
    # 测试获取提示词
    test_patient = {
        "name": "测试患者",
        "age": 70,
        "gender": "男",
        "diseases": ["糖尿病"]
    }
    
    test_data = {
        "glim_results": {"diagnosis": "中度营养不良"},
        "facial_analysis": {"analysis": "面部特征分析"},
        "bia_analysis": {"bmi": 18.0}
    }
    
    # 测试视觉提示词
    vision_prompt = get_vision_analysis_prompt(test_patient, 1)
    print(f"👁️ 视觉提示词长度: {len(vision_prompt)} 字符")
    
    # 测试综合分析提示词
    comprehensive_prompt = get_comprehensive_analysis_prompt(test_patient, test_data)
    print(f"🧠 综合分析提示词长度: {len(comprehensive_prompt)} 字符")
    
    print("=" * 50)
    print("✅ 提示词管理器测试完成")
    
    if UNIFIED_SYSTEM_AVAILABLE:
        print("🎯 使用统一提示词管理系统")
    else:
        print("⚠️ 使用传统兼容模式")


if __name__ == "__main__":
    test_prompt_manager()
