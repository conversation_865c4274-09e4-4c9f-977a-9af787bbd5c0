#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示只选择面部分析时的提示词构建
"""

import sys
import os
sys.path.append('.')

from config.comprehensive_analysis_prompts import build_dynamic_comprehensive_prompt

def demo_facial_only_analysis():
    """演示只选择面部分析时的提示词构建"""

    # 模拟患者信息
    patient_info = '''{
  "name": "陈长生",
  "age": 69,
  "gender": "男"
}'''

    # 模拟只选择面部分析的数据
    available_modules_data = {
        'facial_analysis': '''### 面部分析结果
患者面部形态学评估显示：
- 颞部明显凹陷，颞肌萎缩明显
- 面颊脂肪垫减少，颧骨突出
- 咬肌萎缩，面部轮廓不饱满
- 整体印象：营养不良面容，肌少症表现明显

VLM分析结论：高度疑似营养不良，肌肉质量减少明显''',
        'bia_analysis': None,  # 不使用BIA
        'glim_assessment': None  # 不使用GLIM
    }

    # 构建提示词
    prompt = build_dynamic_comprehensive_prompt(patient_info, available_modules_data)

    print('=== 只选择面部分析时的综合分析提示词 ===')
    print(prompt)

    return prompt

if __name__ == "__main__":
    demo_facial_only_analysis()
