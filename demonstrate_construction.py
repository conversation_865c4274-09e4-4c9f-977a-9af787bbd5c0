#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示面部分析提示词构建过程的详细步骤
"""

import sys
import os
sys.path.append('.')

from config.comprehensive_analysis_prompts import (
    MODULE_PROMPTS,
    ANALYSIS_REQUIREMENTS_TEMPLATE,
    build_dynamic_comprehensive_prompt
)

def demonstrate_facial_analysis_construction():
    """详细演示面部分析提示词的构建过程"""

    print("=" * 60)
    print("面部分析提示词构建过程演示")
    print("=" * 60)

    # 步骤1: 模拟输入数据
    print("\n1️⃣ 输入数据:")
    patient_info = '''{
  "name": "陈长生",
  "age": 69,
  "gender": "男"
}'''

    available_modules_data = {
        'facial_analysis': '''### 面部分析结果
患者面部形态学评估显示：
- 颞部明显凹陷，颞肌萎缩明显
- 面颊脂肪垫减少，颧骨突出
- 咬肌萎缩，面部轮廓不饱满
- 整体印象：营养不良面容，肌少症表现明显

VLM分析结论：高度疑似营养不良，肌肉质量减少明显''',
        'bia_analysis': None,  # 不使用BIA
        'glim_assessment': None  # 不使用GLIM
    }

    print(f"患者信息: {patient_info}")
    print(f"可用模块数据: {list(available_modules_data.keys())}")
    print(f"- facial_analysis: {'✅ 有数据' if available_modules_data['facial_analysis'] else '❌ 无数据'}")
    print(f"- bia_analysis: {'✅ 有数据' if available_modules_data['bia_analysis'] else '❌ 无数据'}")
    print(f"- glim_assessment: {'✅ 有数据' if available_modules_data['glim_assessment'] else '❌ 无数据'}")

    # 步骤2: 确定使用的模块
    print("\n2️⃣ 确定使用的模块:")
    used_modules = []
    for module_name, module_data in available_modules_data.items():
        if module_data is not None:
            used_modules.append(module_name)
            print(f"✅ 包含模块: {module_name}")

    # 步骤3: 构建模块名称映射
    print("\n3️⃣ 构建模块名称映射:")
    module_name_mapping = {
        "facial_analysis": "面部视觉分析",
        "bia_analysis": "BIA体成分分析",
        "glim_assessment": "GLIM量化评估"
    }
    used_module_names = [module_name_mapping.get(mod, mod) for mod in used_modules]
    print(f"使用的模块名称: {used_module_names}")
    print(f"模块列表字符串: {' + '.join(used_module_names)}")

    # 步骤4: 构建模块内容
    print("\n4️⃣ 构建模块内容:")
    modules_content = ""
    for module_name, module_data in available_modules_data.items():
        if module_data is not None:
            print(f"\n📋 处理模块: {module_name}")
            module_prompt = MODULE_PROMPTS.get(module_name, "")
            print(f"获取到的模块提示词模板长度: {len(module_prompt)} 字符")

            formatted_prompt = module_prompt.format(data_content=module_data)
            print(f"格式化后的提示词长度: {len(formatted_prompt)} 字符")

            modules_content += formatted_prompt + "\n\n"
            print("✅ 已添加到模块内容中")

    # 步骤5: 构建基础提示词
    print("\n5️⃣ 构建基础提示词:")
    base_prompt = f"""请基于以下实际收集到的评估数据，进行全面的营养状况综合分析。

## 患者基本信息
{patient_info}

## 实际使用的评估模块
**本次分析使用的模块**: {' + '.join(used_module_names)}
**注意**: 请严格基于实际提供的数据进行分析，不要假设或推测未提供的信息。

## 收集到的评估数据

{modules_content}"""

    print(f"基础提示词总长度: {len(base_prompt)} 字符")

    # 步骤6: 填充分析要求模板
    print("\n6️⃣ 填充分析要求模板:")
    filled_requirements = ANALYSIS_REQUIREMENTS_TEMPLATE.format(
        used_module_names=' + '.join(used_module_names)
    )
    print(f"分析要求模板长度: {len(filled_requirements)} 字符")

    # 步骤7: 组合最终提示词
    print("\n7️⃣ 组合最终提示词:")
    final_prompt = base_prompt + filled_requirements
    print(f"最终提示词总长度: {len(final_prompt)} 字符")

    # 步骤8: 显示关键部分
    print("\n8️⃣ 关键构建结果:")
    print("\n" + "="*50)
    print("最终提示词结构:")
    print("="*50)

    # 分割显示
    lines = final_prompt.split('\n')
    for i, line in enumerate(lines[:20]):  # 只显示前20行
        print("2d")

    if len(lines) > 20:
        print(f"\n... (还有 {len(lines) - 20} 行)")

    print("\n" + "="*50)
    print("构建完成！")
    print("="*50)

    return final_prompt

if __name__ == "__main__":
    demonstrate_facial_analysis_construction()
