#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视觉分析提示词配置
基于项目概况和临床文献优化的多VLM投票式面部营养分析提示词
"""

# 多VLM投票式视觉分析的核心提示词
VISION_ANALYSIS_PROMPT = """"""

# 质量控制提示词
QUALITY_CONTROL_PROMPT = """"""

def get_contextual_vision_prompt(patient_info=None, instance_id=1):

    base_prompt = VISION_ANALYSIS_PROMPT
    
    # 添加投票系统标识
    voting_context = f"这幅图什么内容，5个字以内描述"
    
    
    return voting_context+base_prompt+QUALITY_CONTROL_PROMPT


