#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多VLM投票式视觉分析智能体
实现：图片 → Qwen2.5-VL (3次) → HuatuoGPT (分析整合) → 最终结果
"""
import json
import asyncio
from typing import Dict, List, Any, Union, Optional
from PIL import Image
from loguru import logger

from src.core.lm_studio_client import LMStudioClient
from src.knowledge.nutrition_kb import nutrition_kb


class VisionAnalyzer:
    """多VLM投票式视觉分析器"""
    
    def __init__(self):
        self.lm_client = LMStudioClient()
        logger.info("视觉分析器初始化完成")
    
    async def analyze_facial_nutrition(
        self,
        image_input: Union[str, Image.Image],
        patient_info: Optional[Dict[str, Any]] = None,
        instances: int = 3
    ) -> Dict[str, Any]:
        """
        多VLM投票式面部营养分析
        
        Args:
            image_input: 图片路径或PIL图像对象
            patient_info: 患者信息（用于上下文化提示词）
            instances: VLM投票实例数量
        
        Returns:
            最终的综合分析结果
        """
        logger.info(f"开始多VLM投票式面部营养分析，实例数: {instances}")
        
        try:
            # 第一步：获取上下文化的提示词（每个实例使用独立的提示词）
            # 为多VLM投票系统准备不同的提示词实例
            
            # 第二步：Qwen2.5-VL进行多次独立分析
            vlm_results = await self._multi_vlm_analysis(
                image_input, patient_info, instances
            )
            
            # 第三步：华佗GPT分析和整合结果
            final_result = await self._consensus_analysis_by_huatuo(vlm_results)
            
            # 第四步：验证和后处理
            validated_result = self._validate_and_enhance_result(
                final_result, vlm_results
            )
            
            logger.info("多VLM投票式分析完成")
            return validated_result
            
        except Exception as e:
            logger.error(f"视觉分析失败: {e}")
            return self._create_error_result(str(e))
    
    async def _multi_vlm_analysis(
        self,
        image_input: Union[str, Image.Image],
        patient_info: Optional[Dict[str, Any]],
        instances: int
    ) -> List[Dict[str, Any]]:
        """执行多次VLM分析"""
        logger.info(f"开始{instances}次Qwen2.5-VL独立分析")
        
        # 创建多个异步任务，每个实例使用独立的提示词
        tasks = []
        for i in range(instances):
            task = self._single_vlm_analysis(image_input, patient_info, i+1)
            tasks.append(task)
        
        # 并发执行所有分析
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤成功的结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"VLM实例 {i+1} 分析失败: {result}")
            else:
                valid_results.append(result)
                logger.info(f"VLM实例 {i+1} 分析成功")
        
        if not valid_results:
            raise Exception("所有VLM实例分析都失败了")
        
        logger.info(f"成功完成 {len(valid_results)}/{instances} 次VLM分析")
        return valid_results
    
    async def _single_vlm_analysis(
        self,
        image_input: Union[str, Image.Image],
        patient_info: Optional[Dict[str, Any]],
        instance_id: int
    ) -> Dict[str, Any]:
        """单次VLM分析"""
        try:
            # 为每个实例生成独立的上下文化提示词
            prompt = nutrition_kb.get_contextual_prompt(patient_info, instance_id)
            
            logger.info(f"VLM实例 {instance_id} 使用专业提示词，长度: {len(prompt)} 字符")
            
            # 调用视觉分析模型
            response = self.lm_client.call_vision_model(
                image_data=image_input,
                prompt=prompt,
                temperature=0.2  # 保持一些随机性以支持投票机制
            )
            
            raw_text = response.get('analysis', '') if isinstance(response, dict) else str(response)
            
            # 尝试解析JSON响应
            parsed_result = self._parse_vlm_response(raw_text, instance_id)
            
            return {
                "instance_id": instance_id,
                "raw_response": raw_text,
                "parsed_data": parsed_result,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"VLM实例 {instance_id} 分析异常: {e}")
            return {
                "instance_id": instance_id,
                "raw_response": "",
                "parsed_data": None,
                "success": False,
                "error": str(e)
            }
    
    def _parse_vlm_response(self, raw_text: str, instance_id: int) -> Optional[Dict[str, Any]]:
        """解析VLM响应"""
        try:
            # 查找JSON部分
            if '{' in raw_text and '}' in raw_text:
                start = raw_text.find('{')
                end = raw_text.rfind('}') + 1
                json_str = raw_text[start:end]
                
                parsed = json.loads(json_str)
                
                # 验证响应格式
                validation = nutrition_kb.validate_vision_response(parsed)
                if validation["is_valid"]:
                    return parsed
                else:
                    logger.warning(f"VLM实例 {instance_id} 响应格式验证失败: {validation['errors']}")
            
            # 如果无法解析JSON，创建基础结构
            return self._create_fallback_structure(raw_text, instance_id)
            
        except json.JSONDecodeError as e:
            logger.warning(f"VLM实例 {instance_id} JSON解析失败: {e}")
            return self._create_fallback_structure(raw_text, instance_id)
    
    def _create_fallback_structure(self, raw_text: str, instance_id: int) -> Dict[str, Any]:
        """为无法解析的响应创建备用结构"""
        return {
            "visual_analysis": [
                {
                    "facial_region": "Overall",
                    "finding": raw_text[:200],  # 取前200字符
                    "confidence": 0.5,
                    "severity": "unknown"
                }
            ],
            "overall_assessment": {
                "malnutrition_likelihood": "unknown",
                "confidence": 0.5,
                "key_findings": ["原始响应无法解析为标准格式"]
            },
            "parsing_note": f"VLM实例{instance_id}响应格式异常，使用备用解析"
        }
    
    async def _consensus_analysis_by_huatuo(self, vlm_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用华佗GPT分析和整合VLM结果"""
        logger.info("开始华佗GPT共识分析")
        
        # 构建华佗GPT的分析提示词
        consensus_prompt = self._build_consensus_prompt(vlm_results)
        
        try:
            # 使用统一的面部分析共识提示词
            from config.facial_analysis_prompts import get_facial_consensus_system_prompt
            professional_system_prompt = get_facial_consensus_system_prompt()
        except ImportError:
            logger.warning("无法导入面部分析提示词，使用降级方案")
            # 使用专业的系统提示词
            professional_system_prompt = """你是温州医科大学附属第一医院营养科的资深临床营养学专家，专门负责多模态AI诊断系统的共识分析。

## 你的专业背景
- 临床营养学专家，具有20年以上面部形态学评估经验
- 精通GLIM国际营养不良诊断标准
- 多模态医学AI系统的首席专家
- 专长于营养相关面部体征的识别和分析

## 你的任务
综合分析多个AI视觉专家对同一张面部图像的营养状况评估结果，运用你的专业知识识别共识点和分歧，最终给出权威的综合诊断意见。

## 分析原则
1. **循证医学**：基于权威文献和临床指南
2. **共识优先**：重视多个模型的一致性发现
3. **置信度加权**：根据各模型的置信度进行权重分析
4. **临床相关性**：优先考虑临床意义显著的发现
5. **保守原则**：在不确定时采用保守的诊断策略

请基于你的专业经验，对多个AI专家的分析结果进行权威的综合评估。"""
            
            # 调用华佗GPT进行综合分析  
            huatuo_response = self.lm_client.call_huatuogpt(
                prompt=consensus_prompt,
                system_prompt=professional_system_prompt
            )
            
            # 解析华佗GPT的响应
            final_result = self._parse_huatuo_consensus(huatuo_response, vlm_results)
            
            logger.info("华佗GPT共识分析完成")
            return final_result
            
        except Exception as e:
            logger.error(f"华佗GPT共识分析失败: {e}")
            # 降级处理：使用简单投票机制
            return self._fallback_consensus(vlm_results)
    
    def _build_consensus_prompt(self, vlm_results: List[Dict[str, Any]]) -> str:
        """构建华佗GPT的共识分析提示词"""
        prompt = """请分析以下3个AI视觉模型对同一张面部图像的营养状况评估结果，并给出最终的综合诊断：

"""
        
        # 添加每个VLM的分析结果
        for i, result in enumerate(vlm_results, 1):
            if result["success"] and result["parsed_data"]:
                prompt += f"**模型{i}的分析结果：**\n"
                prompt += f"原始响应: {result['raw_response'][:300]}...\n"
                
                parsed_data = result["parsed_data"]
                if "visual_analysis" in parsed_data:
                    prompt += "详细发现:\n"
                    for analysis in parsed_data["visual_analysis"]:
                        region = analysis.get("facial_region", "未知区域")
                        finding = analysis.get("finding", "无发现")
                        confidence = analysis.get("confidence", 0)
                        severity = analysis.get("severity", "unknown")
                        prompt += f"- {region}: {finding} (置信度: {confidence}, 严重程度: {severity})\n"
                
                if "overall_assessment" in parsed_data:
                    overall = parsed_data["overall_assessment"]
                    likelihood = overall.get("malnutrition_likelihood", "unknown")
                    confidence = overall.get("confidence", 0)
                    prompt += f"整体评估: {likelihood} (置信度: {confidence})\n"
                
                prompt += "\n"
            else:
                prompt += f"**模型{i}的分析结果：** 分析失败\n\n"
        
        prompt += """请基于以上分析结果，进行综合评估：

1. **共识分析**: 识别各模型的共同发现和分歧点
2. **权重评估**: 考虑各模型的置信度和结果一致性
3. **医学判断**: 结合临床营养学知识，判断哪些发现更可信
4. **最终诊断**: 给出综合的营养状况评估

请按以下JSON格式返回结果：
{
  "consensus_analysis": {
    "common_findings": ["各模型的共同发现"],
    "divergent_findings": ["模型间的分歧点"],
    "reliability_assessment": "对各模型结果可靠性的评估"
  },
  "final_assessment": {
    "malnutrition_status": "normal/mild/moderate/severe",
    "confidence_level": float (0-1),
    "key_evidence": ["支持诊断的关键证据"],
    "clinical_recommendations": ["临床建议"]
  },
  "detailed_findings": {
    "upper_face": {"finding": "描述", "severity": "程度", "confidence": float},
    "midface": {"finding": "描述", "severity": "程度", "confidence": float},
    "lower_face": {"finding": "描述", "severity": "程度", "confidence": float}
  }
}"""
        
        return prompt
    
    def _parse_huatuo_consensus(self, huatuo_response: str, vlm_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """解析华佗GPT的共识分析响应"""
        try:
            # 尝试提取JSON
            if '{' in huatuo_response and '}' in huatuo_response:
                start = huatuo_response.find('{')
                end = huatuo_response.rfind('}') + 1
                json_str = huatuo_response[start:end]
                parsed = json.loads(json_str)
                
                # 添加元数据
                parsed["analysis_metadata"] = {
                    "method": "huatuo_consensus",
                    "vlm_instances_used": len([r for r in vlm_results if r["success"]]),
                    "total_vlm_instances": len(vlm_results),
                    "raw_huatuo_response": huatuo_response
                }
                
                return parsed
            else:
                # 无法解析JSON时，提取关键信息
                return self._extract_key_info_from_text(huatuo_response, vlm_results)
                
        except json.JSONDecodeError:
            logger.warning("华佗GPT响应JSON解析失败，尝试文本解析")
            return self._extract_key_info_from_text(huatuo_response, vlm_results)
    
    def _extract_key_info_from_text(self, text: str, vlm_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从华佗GPT的文本响应中提取关键信息"""
        # 简单的关键词提取
        text_lower = text.lower()
        
        # 判断营养状况
        if any(word in text_lower for word in ["正常", "normal", "无异常"]):
            status = "normal"
        elif any(word in text_lower for word in ["重度", "严重", "severe"]):
            status = "severe"
        elif any(word in text_lower for word in ["中度", "moderate"]):
            status = "moderate"
        elif any(word in text_lower for word in ["轻度", "mild", "轻微"]):
            status = "mild"
        else:
            status = "unknown"
        
        return {
            "consensus_analysis": {
                "common_findings": ["基于文本分析提取"],
                "divergent_findings": ["无法从文本精确提取"],
                "reliability_assessment": "华佗GPT文本响应，需要进一步解析"
            },
            "final_assessment": {
                "malnutrition_status": status,
                "confidence_level": 0.6,
                "key_evidence": [text[:200]],
                "clinical_recommendations": ["建议进一步专业评估"]
            },
            "detailed_findings": {
                "upper_face": {"finding": "待进一步分析", "severity": "unknown", "confidence": 0.5},
                "midface": {"finding": "待进一步分析", "severity": "unknown", "confidence": 0.5},
                "lower_face": {"finding": "待进一步分析", "severity": "unknown", "confidence": 0.5}
            },
            "analysis_metadata": {
                "method": "text_extraction_fallback",
                "vlm_instances_used": len([r for r in vlm_results if r["success"]]),
                "raw_huatuo_response": text
            }
        }
    
    def _fallback_consensus(self, vlm_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """降级共识机制：简单投票"""
        logger.info("使用降级共识机制")
        
        # 收集所有成功的评估结果
        assessments = []
        for result in vlm_results:
            if result["success"] and result["parsed_data"]:
                parsed = result["parsed_data"]
                if "overall_assessment" in parsed:
                    assessments.append(parsed["overall_assessment"])
        
        if not assessments:
            return self._create_error_result("所有VLM分析都失败")
        
        # 简单投票：选择最常见的评估结果
        status_votes = {}
        total_confidence = 0
        
        for assessment in assessments:
            status = assessment.get("malnutrition_likelihood", "unknown")
            confidence = assessment.get("confidence", 0)
            
            if status not in status_votes:
                status_votes[status] = []
            status_votes[status].append(confidence)
            total_confidence += confidence
        
        # 选择得票最多的状态
        final_status = max(status_votes.keys(), key=lambda x: len(status_votes[x]))
        avg_confidence = total_confidence / len(assessments) if assessments else 0
        
        return {
            "consensus_analysis": {
                "common_findings": ["使用简单投票机制"],
                "divergent_findings": [f"状态投票分布: {status_votes}"],
                "reliability_assessment": "降级共识机制，可靠性较低"
            },
            "final_assessment": {
                "malnutrition_status": final_status,
                "confidence_level": avg_confidence,
                "key_evidence": ["基于多VLM投票结果"],
                "clinical_recommendations": ["建议人工复核"]
            },
            "analysis_metadata": {
                "method": "simple_voting_fallback",
                "vlm_instances_used": len(assessments),
                "status_votes": status_votes
            }
        }
    
    def _validate_and_enhance_result(
        self,
        final_result: Dict[str, Any],
        vlm_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """验证和增强最终结果"""
        # 添加原始VLM结果供参考
        final_result["vlm_raw_results"] = vlm_results
        
        # 添加时间戳
        import datetime
        final_result["analysis_timestamp"] = datetime.datetime.now().isoformat()
        
        # 添加质量评估
        success_rate = len([r for r in vlm_results if r["success"]]) / len(vlm_results)
        final_result["quality_assessment"] = {
            "vlm_success_rate": success_rate,
            "overall_reliability": "high" if success_rate >= 0.8 else "medium" if success_rate >= 0.5 else "low"
        }
        
        return final_result
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "error": True,
            "error_message": error_message,
            "final_assessment": {
                "malnutrition_status": "error",
                "confidence_level": 0.0,
                "key_evidence": ["分析过程出现错误"],
                "clinical_recommendations": ["请重新进行分析或寻求人工评估"]
            },
            "analysis_timestamp": __import__('datetime').datetime.now().isoformat()
        }
