# 项目文档总结

本文档汇总了工作区中除docs文件夹外的所有.md文件的要点，便于快速了解项目整体情况。

## 1. README.md - 项目主说明

### 项目概述
营养不良诊断智能体系统，基于LangGraph和多模态大语言模型的疾病相关性营养不良(DRM)智能化诊断系统。

### 核心特点
- 🧠 智能体架构：基于LangGraph的多智能体协同系统
- 👁️ 多VLM投票：使用多个视觉语言模型投票机制，提高诊断准确性
- 🔧 工具集成：API工具调用框架，支持BIA数据分析、GLIM评分等
- 📚 知识库驱动：内置营养-面部体征关联知识库
- 🏥 本地部署：支持LM Studio本地模型部署，保护隐私

### 系统架构
```
输入层(多模态数据) → 智能体调度中心(MLLM) → 视觉分析模块/数据分析模块 → 共识机制与信息融合 → 最终推理与报告生成
```

### 技术栈
- 框架：LangGraph, LangChain
- 模型：HuatuoGPT-o1-7B, Qwen2.5-VL-7B
- 部署：LM Studio (本地)
- 数据处理：Pandas, OpenPyXL
- 图像处理：PIL, OpenCV

## 2. README_LANGGRAPH_BRAIN.md - LangGraph + AI主脑版本

### 项目特色
- 🧠 AI主脑架构：通过华佗GPT主脑模型控制对话流程
- 🔄 LangGraph状态管理：专业状态机和工作流引擎
- 🎨 ChatGPT风格界面：现代化交互体验
- 🔬 真实模型调用：本地华佗GPT和视觉分析模型

### 系统架构
```
用户界面 → 统一对话API → LangGraph智能体 → AI主脑决策节点 → 专业分析工具 → 本地大模型
```

### 核心组件
- src/core/lm_studio_client.py：LM Studio客户端
- src/agents/conversation_agent.py：AI主脑智能体
- src/tools/：BIA分析工具、GLIM表单处理等
- templates/：对话界面和日志查看器

### 快速启动
1. 创建虚拟环境并安装依赖
2. 启动LM Studio，加载指定模型
3. 运行python start_brain_system.py
4. 访问http://localhost:8080

## 3. project_structure.md - 项目结构设计

### 目录结构
```
agent/
├── config/          # 配置文件（提示词、设置等）
├── src/            # 源代码
│   ├── core/       # 核心组件
│   ├── agents/     # 智能体组件
│   └── tools/      # 工具模块
├── data/           # 数据目录
├── tests/          # 测试代码
└── examples/       # 示例代码
```

### 核心组件说明
- **对话式智能体**：基于LangGraph状态机的工作流节点
- **前端界面系统**：HTML模板，支持文件上传和响应式设计
- **分析引擎**：GLIM处理器、BIA计算器、数据分析器等
- **AI提示词系统**：多VLM投票式视觉分析、华佗GPT综合分析

### 关键技术特性
- 用户为中心的设计理念
- 模块化架构支持任意数据组合
- 状态持久化和错误恢复
- 基于临床文献的专业提示词工程

## 4. LM_STUDIO_TROUBLESHOOTING.md - LM Studio故障排除

该文件当前为空，无内容可总结。

## 5. CONVERSATION_AGENT_README.md - 对话式营养筛查智能体

### 项目概述
革命性的AI营养顾问系统，将医疗筛查转化为友好对话体验。基于LangGraph状态机，通过智能对话引导用户完成营养评估。

### 核心理念
- 智能体作为向导，用户掌控进程
- 友好对话式体验，告别冰冷医疗表单
- 引导式信息收集，用户始终主导
- 专业分析与人性化表达结合

### 对话体验
- 智能体问候和建档
- 引导式数据选择（GLIM问卷、面部照片、BIA数据）
- 个性化营养评估报告生成

### 技术架构
基于LangGraph的状态机设计，包含完整的状态管理和工作流节点。

### 前端特色
- 美观的聊天界面
- 模态框数据收集
- 响应式设计

### 核心功能
- GLIM标准评估
- BIA数据分析
- 面部特征分析
- 综合智能分析

### 产品亮点
- 用户体验革新
- 技术创新（多模态AI分析、本地化部署）
- 临床价值（标准化评估、早期筛查）

---

## 总结

该项目是一个基于AI的营养不良诊断智能体系统，主要特点包括：
- 多模态数据分析（视觉、问卷、体成分）
- 对话式用户界面
- 本地模型部署确保隐私
- 遵循国际GLIM诊断标准
- 基于LangGraph的智能体架构

项目支持多种启动方式，包括基础版本和AI主脑增强版本，可用于医疗机构、个人用户和科研教育场景。
