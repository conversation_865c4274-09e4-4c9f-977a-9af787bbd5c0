<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营养不良诊断智能体系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimSun', serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .workflow-container {
            padding: 40px;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .workflow-steps::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }
        
        .step {
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #999;
            position: relative;
            z-index: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }
        
        .step.completed {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }
        
        .step-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .step-label {
            width: 80px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
        }
        
        .step-label.active {
            color: #4CAF50;
        }
        
        .step-label.completed {
            color: #2196F3;
        }
        
        .module-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .module-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .module-card.completed {
            background: #e8f5e8;
            border-color: #4CAF50;
        }
        
        .module-card.active {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .module-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .module-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .module-card .status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .status.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .status.active {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .progress-summary {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin-top: 30px;
        }
        
        .progress-summary h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>营养不良诊断智能体系统</h1>
            <p>基于多模态大语言模型的疾病相关性营养不良(DRM)智能化诊断系统</p>
        </div>
        
        <div class="workflow-container">
            <!-- 工作流程步骤 -->
            <div class="workflow-steps">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            
            <div class="step-labels">
                <div class="step-label" id="label1">患者信息</div>
                <div class="step-label" id="label2">GLIM评估</div>
                <div class="step-label" id="label3">面部分析</div>
                <div class="step-label" id="label4">综合诊断</div>
            </div>
            
            <!-- 模块卡片 -->
            <div class="module-cards">
                <!-- 患者信息模块 -->
                <div class="module-card" id="patientInfoCard" onclick="openModule('patient_info')">
                    <div class="status pending" id="patientInfoStatus">待完成</div>
                    <h3>📋 患者基本信息录入</h3>
                    <p>录入患者的基本信息、疾病史和相关临床数据，为后续评估提供基础信息。</p>
                    <button class="btn" id="patientInfoBtn">开始录入</button>
                </div>
                
                <!-- GLIM评估模块 -->
                <div class="module-card" id="glimCard" onclick="openModule('glim')">
                    <div class="status pending" id="glimStatus">待完成</div>
                    <h3>📊 GLIM标准评估</h3>
                    <p>基于全球领导人发起营养不良诊断标准(GLIM)进行表型和病因标准评估。</p>
                    <button class="btn disabled" id="glimBtn">GLIM评估</button>
                </div>
                
                <!-- 面部分析模块 -->
                <div class="module-card" id="visionCard" onclick="openModule('vision')">
                    <div class="status pending" id="visionStatus">待完成</div>
                    <h3>👁️ 面部图像分析</h3>
                    <p>使用多VLM投票机制分析面部图像，识别营养不良相关的视觉体征。</p>
                    <button class="btn disabled" id="visionBtn">面部分析</button>
                </div>
                
                <!-- 综合诊断模块 -->
                <div class="module-card" id="diagnosisCard" onclick="openModule('diagnosis')">
                    <div class="status pending" id="diagnosisStatus">待完成</div>
                    <h3>🧠 智能综合诊断</h3>
                    <p>整合所有评估结果，生成最终的营养状况诊断报告和建议。</p>
                    <button class="btn disabled" id="diagnosisBtn">生成诊断</button>
                </div>
            </div>
            
            <!-- 进度摘要 -->
            <div class="progress-summary">
                <h3>评估进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <p id="progressText">请开始患者信息录入</p>
            </div>
        </div>
    </div>

    <script>
        // 工作流程状态管理
        let workflowState = {
            patientInfo: false,
            glim: false,
            vision: false,
            diagnosis: false
        };

        // 初始化页面
        function initWorkflow() {
            const savedState = localStorage.getItem('workflowState');
            if (savedState) {
                workflowState = JSON.parse(savedState);
            }
            
            const patientInfo = localStorage.getItem('patientInfo');
            if (patientInfo) {
                workflowState.patientInfo = true;
            }
            
            updateWorkflowDisplay();
        }
    </script>
</body>
</html>
