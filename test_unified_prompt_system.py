#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一提示词管理系统测试
验证所有提示词模块是否正常加载和工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

def test_base_prompts():
    """测试基础提示词模块"""
    logger.info("🧪 测试基础提示词模块...")
    try:
        from config.base_prompts import get_base_system_prompt, HOSPITAL_INFO
        
        base_prompt = get_base_system_prompt()
        logger.info(f"✅ 基础提示词加载成功，长度: {len(base_prompt)} 字符")
        logger.info(f"🏥 医院信息: {HOSPITAL_INFO}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 基础提示词模块测试失败: {e}")
        return False

def test_facial_prompts():
    """测试面部分析提示词模块"""
    logger.info("👁️ 测试面部分析提示词模块...")
    try:
        from config.facial_analysis_prompts import (
            get_facial_analysis_system_prompt,
            get_facial_consensus_system_prompt,
            get_simple_facial_prompt
        )
        
        system_prompt = get_facial_analysis_system_prompt()
        consensus_prompt = get_facial_consensus_system_prompt()
        simple_prompt = get_simple_facial_prompt()
        
        logger.info(f"✅ 面部分析系统提示词: {len(system_prompt)} 字符")
        logger.info(f"✅ 面部共识提示词: {len(consensus_prompt)} 字符")
        logger.info(f"✅ 简单面部提示词: {len(simple_prompt)} 字符")
        
        return True
    except Exception as e:
        logger.error(f"❌ 面部分析提示词模块测试失败: {e}")
        return False

def test_glim_prompts():
    """测试GLIM评估提示词模块"""
    logger.info("📋 测试GLIM评估提示词模块...")
    try:
        from config.glim_prompts import (
            get_glim_assessment_system_prompt,
            get_simple_glim_prompt,
            get_glim_form_analysis_prompt
        )
        
        system_prompt = get_glim_assessment_system_prompt()
        simple_prompt = get_simple_glim_prompt({"age": 70, "gender": "男"})
        form_prompt = get_glim_form_analysis_prompt({"weight_loss": "5%"})
        
        logger.info(f"✅ GLIM系统提示词: {len(system_prompt)} 字符")
        logger.info(f"✅ GLIM简单提示词: {len(simple_prompt)} 字符")
        logger.info(f"✅ GLIM表单提示词: {len(form_prompt)} 字符")
        
        return True
    except Exception as e:
        logger.error(f"❌ GLIM评估提示词模块测试失败: {e}")
        return False

def test_comprehensive_prompts():
    """测试综合分析提示词模块"""
    logger.info("🧠 测试综合分析提示词模块...")
    try:
        from config.comprehensive_analysis_prompts import (
            get_comprehensive_analysis_system_prompt,
            build_dynamic_comprehensive_prompt_with_length_control,
            build_comprehensive_analysis_prompt
        )
        
        system_prompt = get_comprehensive_analysis_system_prompt()
        
        # 测试动态构建
        test_patient = "70岁男性患者"
        test_data = {
            "facial_analysis": {"result": "面部特征正常"},
            "glim_assessment": {"diagnosis": "营养不良风险"}
        }
        
        dynamic_prompt = build_dynamic_comprehensive_prompt_with_length_control(
            test_patient, test_data, max_tokens=2000
        )
        
        simple_prompt = build_comprehensive_analysis_prompt(
            {"age": 70, "gender": "男"}, 
            test_data, 
            "2024-01-01"
        )
        
        logger.info(f"✅ 综合分析系统提示词: {len(system_prompt)} 字符")
        logger.info(f"✅ 动态综合提示词: {len(dynamic_prompt)} 字符")
        logger.info(f"✅ 简单综合提示词: {len(simple_prompt)} 字符")
        
        return True
    except Exception as e:
        logger.error(f"❌ 综合分析提示词模块测试失败: {e}")
        return False

def test_unified_manager():
    """测试统一提示词管理器"""
    logger.info("🎯 测试统一提示词管理器...")
    try:
        from config.unified_prompt_manager import (
            get_prompt_manager,
            get_system_prompt,
            get_facial_prompt,
            get_glim_prompt,
            get_comprehensive_prompt
        )
        
        # 测试管理器实例
        manager = get_prompt_manager()
        stats = manager.get_prompt_statistics()
        validation = manager.validate_prompts()
        
        logger.info(f"✅ 管理器统计: {stats}")
        logger.info(f"✅ 提示词验证: {validation}")
        
        # 测试便捷函数
        facial_prompt = get_facial_prompt("system")
        glim_prompt = get_glim_prompt("system", patient_data={"age": 70})
        comp_prompt = get_comprehensive_prompt("system")
        
        logger.info(f"✅ 面部分析提示词: {len(facial_prompt)} 字符")
        logger.info(f"✅ GLIM评估提示词: {len(glim_prompt)} 字符") 
        logger.info(f"✅ 综合分析提示词: {len(comp_prompt)} 字符")
        
        # 测试统一接口
        unified_facial = get_system_prompt("facial", "system")
        unified_glim = get_system_prompt("glim", "system")
        unified_comp = get_system_prompt("comprehensive", "system")
        
        logger.info(f"✅ 统一面部提示词: {len(unified_facial)} 字符")
        logger.info(f"✅ 统一GLIM提示词: {len(unified_glim)} 字符")
        logger.info(f"✅ 统一综合提示词: {len(unified_comp)} 字符")
        
        return True
    except Exception as e:
        logger.error(f"❌ 统一提示词管理器测试失败: {e}")
        return False

def test_integration_with_lm_studio():
    """测试与LM Studio客户端的集成"""
    logger.info("🔗 测试与LM Studio客户端集成...")
    try:
        from src.core.lm_studio_client import LMStudioClient
        
        # 创建客户端实例
        client = LMStudioClient()
        
        # 测试获取系统提示词
        facial_prompt = client.get_system_prompt("facial", "system")
        glim_prompt = client.get_system_prompt("glim", "system")
        comp_prompt = client.get_system_prompt("comprehensive", "system")
        
        logger.info(f"✅ LM Studio客户端集成成功")
        logger.info(f"✅ 面部提示词长度: {len(facial_prompt)} 字符")
        logger.info(f"✅ GLIM提示词长度: {len(glim_prompt)} 字符")
        logger.info(f"✅ 综合提示词长度: {len(comp_prompt)} 字符")
        
        return True
    except Exception as e:
        logger.error(f"❌ LM Studio客户端集成测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    logger.info("=" * 60)
    logger.info("🧪 统一提示词管理系统测试报告")
    logger.info("=" * 60)
    
    tests = [
        ("基础提示词模块", test_base_prompts),
        ("面部分析提示词模块", test_facial_prompts),
        ("GLIM评估提示词模块", test_glim_prompts),
        ("综合分析提示词模块", test_comprehensive_prompts),
        ("统一提示词管理器", test_unified_manager),
        ("LM Studio客户端集成", test_integration_with_lm_studio)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"📊 测试结果: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"❌ 测试异常: {e}")
    
    # 汇总报告
    logger.info("\n" + "=" * 60)
    logger.info("📈 测试汇总")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info("-" * 60)
    logger.info(f"📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！统一提示词管理系统工作正常")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，请检查相关模块")
    
    logger.info("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    generate_test_report()
