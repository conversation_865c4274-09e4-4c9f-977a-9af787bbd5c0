#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示身高体重处理修复效果
"""

def clean_profile_data(profile_data):
    """清理用户档案数据，移除无效的身高体重值"""
    cleaned_data = profile_data.copy()
    
    # 处理身高
    height = profile_data.get('height')
    if height is not None:
        try:
            height_val = float(height) if height != '' else 0
            if height_val <= 0:
                cleaned_data.pop('height', None)
                print(f"✅ 已移除无效的身高值: {height}")
        except (ValueError, TypeError):
            cleaned_data.pop('height', None)
            print(f"✅ 已移除非数字身高值: {height}")
    
    # 处理体重  
    weight = profile_data.get('weight') or profile_data.get('current_weight')
    if weight is not None:
        try:
            weight_val = float(weight) if weight != '' else 0
            if weight_val <= 0:
                cleaned_data.pop('weight', None)
                cleaned_data.pop('current_weight', None)
                print(f"✅ 已移除无效的体重值: {weight}")
        except (ValueError, TypeError):
            cleaned_data.pop('weight', None)
            cleaned_data.pop('current_weight', None)
            print(f"✅ 已移除非数字体重值: {weight}")
    
    return cleaned_data

def build_patient_info(user_profile):
    """构建患者信息显示"""
    patient_info_lines = []
    patient_info_lines.append(f"- 年龄：{user_profile.get('age', '未提供')}岁")
    patient_info_lines.append(f"- 性别：{user_profile.get('gender', '未提供')}")
    
    # 只有在身高有意义的值时才显示
    height = user_profile.get('height')
    if height is not None:
        try:
            height_val = float(height) if height != '' else 0
            if height_val > 0:
                patient_info_lines.append(f"- 身高：{height}cm")
        except (ValueError, TypeError):
            pass  # 忽略无效的身高值
    
    # 只有在体重有意义的值时才显示
    weight = user_profile.get('weight') or user_profile.get('current_weight')
    if weight is not None:
        try:
            weight_val = float(weight) if weight != '' else 0
            if weight_val > 0:
                patient_info_lines.append(f"- 体重：{weight}kg")
        except (ValueError, TypeError):
            pass  # 忽略无效的体重值
    
    return '\n'.join(patient_info_lines)

def demo_fix_effect():
    """演示修复效果"""
    print("=" * 60)
    print("身高体重处理修复效果演示")
    print("=" * 60)
    
    # 测试场景1：包含0值
    print("\n🔍 测试场景1：用户提交身高体重为0")
    original_data = {
        "name": "陈长生",
        "age": 69,
        "gender": "男",
        "height": 0,
        "weight": 0
    }
    
    print(f"原始数据: {original_data}")
    cleaned_data = clean_profile_data(original_data)
    print(f"清理后数据: {cleaned_data}")
    
    print("\n👤 患者信息显示:")
    patient_info = build_patient_info(cleaned_data)
    print(patient_info)
    
    # 测试场景2：包含空字符串
    print("\n" + "-" * 60)
    print("🔍 测试场景2：用户提交空字符串身高体重")
    original_data2 = {
        "name": "张三",
        "age": 45,
        "gender": "女", 
        "height": "",
        "weight": "0"
    }
    
    print(f"原始数据: {original_data2}")
    cleaned_data2 = clean_profile_data(original_data2)
    print(f"清理后数据: {cleaned_data2}")
    
    print("\n👤 患者信息显示:")
    patient_info2 = build_patient_info(cleaned_data2)
    print(patient_info2)
    
    # 测试场景3：包含有效值
    print("\n" + "-" * 60)
    print("🔍 测试场景3：用户提交有效身高体重")
    original_data3 = {
        "name": "李四",
        "age": 35,
        "gender": "男",
        "height": 170,
        "weight": 65
    }
    
    print(f"原始数据: {original_data3}")
    cleaned_data3 = clean_profile_data(original_data3)
    print(f"清理后数据: {cleaned_data3}")
    
    print("\n👤 患者信息显示:")
    patient_info3 = build_patient_info(cleaned_data3)
    print(patient_info3)
    
    print("\n" + "=" * 60)
    print("✅ 修复效果总结")
    print("=" * 60)
    print("1. 无效的身高体重值（0、空字符串、非数字）被正确过滤")
    print("2. 患者信息中不再显示'身高：0cm'或'体重：0kg'")
    print("3. 只有有效的身高体重值才会被显示")
    print("4. BIA工具调用限制已经强化，避免误调用")

if __name__ == "__main__":
    demo_fix_effect()
