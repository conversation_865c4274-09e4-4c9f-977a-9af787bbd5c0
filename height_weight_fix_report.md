# 身高体重处理修复报告

## 修复内容

### 问题描述
1. 当用户没有填身高体重时，前端会发送0值，但显示为"身高：0cm，体重：0kg"
2. 应该不传输这些无效属性，而不是显示0
3. 即使输入了身高体重，也不应该调用BIA工具（BIA需要专业的生物电阻抗数据）

### 修复方案

#### 1. API服务器层面修复 (api_server.py)
- 在`submit_profile`函数中添加数据清理逻辑
- 过滤掉身高体重为0、空字符串或非数字的值
- 只保留有效的身高体重数据

```python
# 检查并移除无效的身高值
height = profile_data.get('height')
if height is not None:
    try:
        height_val = float(height) if height != '' else 0
        if height_val <= 0:
            cleaned_profile_data.pop('height', None)
    except (ValueError, TypeError):
        cleaned_profile_data.pop('height', None)
```

#### 2. 对话智能体层面修复 (conversation_agent.py)
- 在`add_user_input`方法中添加同样的数据清理逻辑
- 确保双重保护，即使API层漏过也能清理

#### 3. 工具调用智能体修复 (tool_calling_agent_new.py)
- 强化患者信息显示逻辑：只有在有有效值时才显示身高体重字段
- 加强BIA工具调用限制的提示词
- 在文件分析中明确提醒：没有Excel/CSV文件就不能调用BIA工具

### 修复效果

#### 之前的问题
```
👤 **患者信息**
- 年龄：69岁
- 性别：男
- 身高：0cm
- 体重：0kg
```

#### 修复后的效果
```
👤 **患者信息**
- 年龄：69岁
- 性别：男
```

### BIA工具调用限制强化

#### 新增的严格限制规则
```
❌ **绝对禁止调用calculate_bia_metrics的情况**：
- 仅有基本信息（年龄、性别）时
- 仅有身高体重数据时  
- 没有上传任何Excel/CSV文件时
- 上传了图片文件时（图片≠BIA数据）
- 用户只填写了GLIM问卷时

⚠️ **关键理解**：身高体重 ≠ BIA数据！BIA是专业的生物电阻抗分析。

✅ **唯一允许调用的情况**：
- 用户明确上传了标注为"BIA"或"体成分"的Excel/CSV文件
- 文件包含专业BIA指标：相位角(PhA)、阻抗值、电抗值等

💡 **判断标准**：如果没有看到Excel/CSV文件上传，就绝不调用BIA工具
```

## 技术实现细节

### 数据清理函数
```python
def clean_profile_data(profile_data):
    """清理用户档案数据，移除无效的身高体重值"""
    cleaned_data = profile_data.copy()
    
    # 处理身高
    height = profile_data.get('height')
    if height is not None:
        try:
            height_val = float(height) if height != '' else 0
            if height_val <= 0:
                cleaned_data.pop('height', None)
        except (ValueError, TypeError):
            cleaned_data.pop('height', None)
    
    # 处理体重  
    weight = profile_data.get('weight') or profile_data.get('current_weight')
    if weight is not None:
        try:
            weight_val = float(weight) if weight != '' else 0
            if weight_val <= 0:
                cleaned_data.pop('weight', None)
                cleaned_data.pop('current_weight', None)
        except (ValueError, TypeError):
            cleaned_data.pop('weight', None)
            cleaned_data.pop('current_weight', None)
    
    return cleaned_data
```

### 患者信息构建逻辑
```python
# 只有在身高有意义的值时才显示
height = user_profile.get('height')
if height is not None:
    try:
        height_val = float(height) if height != '' else 0
        if height_val > 0:
            patient_info_lines.append(f"- 身高：{height}cm")
    except (ValueError, TypeError):
        pass  # 忽略无效的身高值
```

## 测试验证

### 测试场景
1. 用户提交身高=0, 体重=0的情况
2. 用户提交空字符串身高体重的情况  
3. 用户提交有效身高体重的情况
4. 验证BIA工具不会被错误调用

### 预期结果
1. 无效值被正确过滤，不在患者信息中显示
2. 有效值被正确保留和显示
3. BIA工具只有在上传Excel/CSV文件时才可能被调用
4. 系统提示词明确防止错误的BIA工具调用

## 总结

本次修复从多个层面解决了身高体重0值显示和BIA工具误调用的问题：

1. **API层**：过滤无效数据
2. **智能体层**：双重数据清理
3. **显示层**：只显示有效信息
4. **工具调用层**：强化BIA工具调用限制

修复后，系统将更加准确和用户友好，避免显示无意义的"0cm"、"0kg"信息，同时防止AI错误调用不适合的分析工具。
