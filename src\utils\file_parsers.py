"""
文件解析模块
解析不同格式的文件并标准化为统一的数据结构
"""

import json
import os
import pandas as pd
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import io

logger = logging.getLogger(__name__)


def parse_bia_file(file_path: str) -> Dict[str, Any]:
    """
    解析BIA数据文件
    
    Args:
        file_path: BIA数据文件路径
        
    Returns:
        标准化的BIA数据结构
    """
    logger.info(f"📊 开始解析BIA数据文件: {file_path}")
    
    result = {
        "success": False,
        "error": None,
        "bia_raw": {},
        "key_metrics": {},
        "metadata": {
            "source_file": os.path.basename(file_path),
            "parsed_at": datetime.now().isoformat()
        }
    }
    
    try:
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_ext == '.csv':
            df = pd.read_csv(file_path)
        else:
            raise ValueError(f"不支持的BIA文件格式: {file_ext}")
        
        # 解析数据框
        parsed_data = _parse_bia_dataframe(df)
        result.update(parsed_data)
        result["success"] = True
        
        logger.info("✅ BIA数据文件解析成功")
        
    except Exception as e:
        error_msg = f"BIA文件解析失败: {str(e)}"
        logger.error(error_msg)
        result["error"] = error_msg
    
    return result


def _parse_bia_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
    """解析BIA数据框架"""
    
    # 标准化列名映射
    column_mapping = {
        # 英文列名
        'pha': 'PhA',
        'phase_angle': 'PhA',
        'asmi': 'ASMI',
        'bmi': 'BMI',
        'ecw': 'ECW',
        'icw': 'ICW',
        'tbw': 'TBW',
        'protein': 'Protein',
        'mineral': 'Mineral',
        'body_fat': 'Body_Fat',
        'smm': 'SMM',
        
        # 中文列名
        '相位角': 'PhA',
        '体脂率': 'Body_Fat',
        '肌肉质量': 'SMM',
        '蛋白质': 'Protein',
        '矿物质': 'Mineral',
        '体重': 'Weight',
        '身高': 'Height'
    }
    
    # 标准化列名
    normalized_df = df.copy()
    normalized_df.columns = [
        column_mapping.get(col.lower().strip(), col) 
        for col in df.columns
    ]
    
    # 提取原始数据（取第一行作为患者数据）
    if len(normalized_df) > 0:
        raw_data = normalized_df.iloc[0].to_dict()
        # 清理NaN值
        bia_raw = {k: (v if pd.notna(v) else None) for k, v in raw_data.items()}
    else:
        bia_raw = {}
    
    # 提取关键指标
    key_metrics = _extract_bia_key_metrics(bia_raw)
    
    # 计算衍生指标
    derived_metrics = _calculate_bia_derived_metrics(key_metrics)
    key_metrics.update(derived_metrics)
    
    return {
        "bia_raw": bia_raw,
        "key_metrics": key_metrics,
        "metadata": {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "column_names": df.columns.tolist(),
            "normalized_columns": normalized_df.columns.tolist()
        }
    }


def _extract_bia_key_metrics(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """提取BIA关键指标"""
    key_metrics = {}
    
    # 关键指标列表和默认值
    important_metrics = {
        'PhA': None,        # 相位角
        'ASMI': None,       # 阿司米指数
        'BMI': None,        # 体重指数
        'Body_Fat': None,   # 体脂率
        'SMM': None,        # 骨骼肌质量
        'TBW': None,        # 总体水分
        'Protein': None,    # 蛋白质
        'ECW': None,        # 细胞外水分
        'ICW': None,        # 细胞内水分
        'Weight': None,     # 体重
        'Height': None      # 身高
    }
    
    # 从原始数据中提取
    for metric_name in important_metrics:
        if metric_name in raw_data:
            value = raw_data[metric_name]
            if pd.notna(value) and value is not None:
                try:
                    # 尝试转换为数值
                    key_metrics[metric_name] = float(value)
                except (ValueError, TypeError):
                    # 保留原始值
                    key_metrics[metric_name] = value
    
    return key_metrics


def _calculate_bia_derived_metrics(key_metrics: Dict[str, Any]) -> Dict[str, Any]:
    """计算BIA衍生指标"""
    derived = {}
    
    try:
        # 计算体脂质量（如果有体重和体脂率）
        weight = key_metrics.get('Weight')
        body_fat_pct = key_metrics.get('Body_Fat')
        if weight and body_fat_pct:
            derived['Fat_Mass'] = weight * (body_fat_pct / 100)
            derived['Lean_Mass'] = weight - derived['Fat_Mass']
        
        # PhA正常范围判断（一般认为4-8°为正常）
        pha = key_metrics.get('PhA')
        if pha:
            if pha < 4:
                derived['PhA_Status'] = 'low'
            elif pha > 8:
                derived['PhA_Status'] = 'high' 
            else:
                derived['PhA_Status'] = 'normal'
        
        # BMI分类
        bmi = key_metrics.get('BMI')
        if bmi:
            if bmi < 18.5:
                derived['BMI_Category'] = 'underweight'
            elif bmi < 25:
                derived['BMI_Category'] = 'normal'
            elif bmi < 30:
                derived['BMI_Category'] = 'overweight'
            else:
                derived['BMI_Category'] = 'obese'
        
        # ECW/ICW比值（水肿指标）
        ecw = key_metrics.get('ECW')
        icw = key_metrics.get('ICW')
        if ecw and icw and icw > 0:
            derived['ECW_ICW_Ratio'] = ecw / icw
            # 正常比值约为0.38-0.40
            if derived['ECW_ICW_Ratio'] > 0.40:
                derived['Edema_Risk'] = 'high'
            else:
                derived['Edema_Risk'] = 'normal'
    
    except Exception as e:
        logger.warning(f"计算BIA衍生指标时出错: {e}")
    
    return derived


def parse_glim_data(data: Any) -> Dict[str, Any]:
    """
    解析GLIM表单数据
    
    Args:
        data: GLIM数据（可以是dict、JSON字符串或文件路径）
        
    Returns:
        标准化的GLIM数据结构
    """
    logger.info("📋 开始解析GLIM数据")
    
    result = {
        "success": False,
        "error": None,
        "form_data": {},
        "calculation_results": {},
        "metadata": {
            "parsed_at": datetime.now().isoformat()
        }
    }
    
    try:
        # 处理不同输入格式
        if isinstance(data, str):
            if os.path.isfile(data):
                # 文件路径
                with open(data, 'r', encoding='utf-8') as f:
                    glim_data = json.load(f)
                result["metadata"]["source_file"] = os.path.basename(data)
            else:
                # JSON字符串
                glim_data = json.loads(data)
        elif isinstance(data, dict):
            glim_data = data
        else:
            raise ValueError("不支持的GLIM数据格式")
        
        # 解析和标准化
        parsed_data = _parse_glim_structure(glim_data)
        result.update(parsed_data)
        result["success"] = True
        
        logger.info("✅ GLIM数据解析成功")
        
    except Exception as e:
        error_msg = f"GLIM数据解析失败: {str(e)}"
        logger.error(error_msg)
        result["error"] = error_msg
    
    return result


def _parse_glim_structure(glim_data: Dict[str, Any]) -> Dict[str, Any]:
    """解析GLIM数据结构"""
    
    # 标准化表单数据
    form_data = {}
    calculation_results = {}
    
    # 检查是否已经是标准格式
    if "form_data" in glim_data:
        form_data = glim_data["form_data"]
    else:
        # 尝试从原始结构提取
        form_data = _extract_glim_form_data(glim_data)
    
    # 检查是否有计算结果
    if "calculation_results" in glim_data:
        calculation_results = glim_data["calculation_results"]
    elif form_data:
        # 基于表单数据计算结果
        calculation_results = _calculate_glim_results(form_data)
    
    return {
        "form_data": form_data,
        "calculation_results": calculation_results
    }


def _extract_glim_form_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """从原始数据中提取GLIM表单数据"""
    form_data = {
        "phenotypic_criteria": {},
        "etiologic_criteria": {},
        "severity_criteria": {},
        "notes": ""
    }
    
    # 提取表型标准
    phenotypic = form_data["phenotypic_criteria"]
    if "phenotypic_criteria" in raw_data:
        phenotypic.update(raw_data["phenotypic_criteria"])
    else:
        # 从单独字段提取
        phenotypic["weight_loss"] = raw_data.get("weight_loss", False)
        phenotypic["low_bmi"] = raw_data.get("low_bmi", False)
        phenotypic["muscle_loss"] = raw_data.get("muscle_loss", False)
    
    # 提取病因学标准
    etiologic = form_data["etiologic_criteria"]
    if "etiologic_criteria" in raw_data:
        etiologic.update(raw_data["etiologic_criteria"])
    else:
        etiologic["food_intake_reduction"] = raw_data.get("food_intake_reduction", False)
        etiologic["disease_inflammation"] = raw_data.get("disease_inflammation", False)
    
    # 提取严重程度标准
    severity = form_data["severity_criteria"]
    if "severity_criteria" in raw_data:
        severity.update(raw_data["severity_criteria"])
    else:
        severity["severe_weight_loss"] = raw_data.get("severe_weight_loss", False)
        severity["severe_bmi"] = raw_data.get("severe_bmi", False)
    
    # 提取备注
    form_data["notes"] = raw_data.get("notes", "")
    
    return form_data


def _calculate_glim_results(form_data: Dict[str, Any]) -> Dict[str, Any]:
    """根据GLIM表单数据计算诊断结果"""
    
    phenotypic = form_data.get("phenotypic_criteria", {})
    etiologic = form_data.get("etiologic_criteria", {})
    severity = form_data.get("severity_criteria", {})
    
    # 计算表型标准满足情况
    phenotypic_count = sum([
        phenotypic.get("weight_loss", False),
        phenotypic.get("low_bmi", False),
        phenotypic.get("muscle_loss", False)
    ])
    
    phenotypic_met = [
        criteria for criteria, met in {
            "非自主性体重减轻": phenotypic.get("weight_loss", False),
            "低BMI": phenotypic.get("low_bmi", False),
            "肌肉质量减少": phenotypic.get("muscle_loss", False)
        }.items() if met
    ]
    
    # 计算病因学标准满足情况
    etiologic_count = sum([
        etiologic.get("food_intake_reduction", False),
        etiologic.get("disease_inflammation", False)
    ])
    
    etiologic_met = [
        criteria for criteria, met in {
            "食物摄入减少或吸收障碍": etiologic.get("food_intake_reduction", False),
            "疾病负担或炎症": etiologic.get("disease_inflammation", False)
        }.items() if met
    ]
    
    # 计算严重程度
    severity_count = sum([
        severity.get("severe_weight_loss", False),
        severity.get("severe_bmi", False)
    ])
    
    severity_met = [
        criteria for criteria, met in {
            "体重显著下降": severity.get("severe_weight_loss", False),
            "低BMI": severity.get("severe_bmi", False)
        }.items() if met
    ]
    
    # GLIM诊断逻辑
    phenotypic_sufficient = phenotypic_count >= 1
    etiologic_sufficient = etiologic_count >= 1
    both_criteria_met = phenotypic_sufficient and etiologic_sufficient
    
    # 确定诊断结果
    if both_criteria_met:
        if severity_count >= 1:
            diagnosis_result = "重度营养不良"
        else:
            diagnosis_result = "中度营养不良"
        is_malnutrition = True
    else:
        diagnosis_result = "无营养不良"
        is_malnutrition = False
    
    return {
        "diagnosis": {
            "is_malnutrition": is_malnutrition,
            "result": diagnosis_result
        },
        "criteria_analysis": {
            "phenotypic_criteria": {
                "count": phenotypic_count,
                "met_criteria": phenotypic_met,
                "sufficient": phenotypic_sufficient
            },
            "etiologic_criteria": {
                "count": etiologic_count,
                "met_criteria": etiologic_met,
                "sufficient": etiologic_sufficient
            },
            "severity_criteria": {
                "count": severity_count,
                "met_criteria": severity_met,
                "indicates_severe": severity_count >= 1
            }
        },
        "diagnostic_logic": {
            "step1_phenotypic_sufficient": phenotypic_sufficient,
            "step1_etiologic_sufficient": etiologic_sufficient,
            "step1_both_criteria_met": both_criteria_met,
            "step2_severity_assessment": diagnosis_result
        }
    }


def extract_text_content(file_path: str) -> str:
    """
    从文档中提取文本内容
    
    Args:
        file_path: 文档文件路径
        
    Returns:
        提取的文本内容
    """
    logger.info(f"📄 开始提取文档文本: {file_path}")
    
    try:
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        elif file_ext == '.md':
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        elif file_ext == '.pdf':
            content = _extract_pdf_text(file_path)
        elif file_ext in ['.doc', '.docx']:
            content = _extract_word_text(file_path)
        else:
            raise ValueError(f"不支持的文档格式: {file_ext}")
        
        logger.info(f"✅ 文档文本提取成功，长度: {len(content)} 字符")
        return content
        
    except Exception as e:
        error_msg = f"文档文本提取失败: {str(e)}"
        logger.error(error_msg)
        return ""


def _extract_pdf_text(file_path: str) -> str:
    """从PDF中提取文本"""
    try:
        import PyPDF2
        content = ""
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            for page in reader.pages:
                content += page.extract_text() + "\n"
        return content.strip()
    except ImportError:
        logger.warning("PyPDF2未安装，无法解析PDF文件")
        return ""
    except Exception as e:
        logger.warning(f"PDF文本提取失败: {e}")
        return ""


def _extract_word_text(file_path: str) -> str:
    """从Word文档中提取文本"""
    try:
        import docx
        doc = docx.Document(file_path)
        content = ""
        for paragraph in doc.paragraphs:
            content += paragraph.text + "\n"
        return content.strip()
    except ImportError:
        logger.warning("python-docx未安装，无法解析Word文件")
        return ""
    except Exception as e:
        logger.warning(f"Word文本提取失败: {e}")
        return ""


def validate_parsed_data(data: Dict[str, Any], data_type: str) -> bool:
    """
    验证解析后数据的完整性和有效性
    
    Args:
        data: 解析后的数据
        data_type: 数据类型（bia, glim, text）
        
    Returns:
        是否有效
    """
    logger.info(f"🔍 验证 {data_type} 数据有效性")
    
    try:
        if data_type == "bia":
            return _validate_bia_data(data)
        elif data_type == "glim":
            return _validate_glim_data(data)
        elif data_type == "text":
            return _validate_text_data(data)
        else:
            logger.warning(f"未知数据类型: {data_type}")
            return False
            
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return False


def _validate_bia_data(data: Dict[str, Any]) -> bool:
    """验证BIA数据"""
    if not data.get("success", False):
        return False
    
    key_metrics = data.get("key_metrics", {})
    
    # 检查是否有足够的关键指标
    required_metrics = ["PhA", "BMI", "ASMI"]  # 至少需要这些指标之一
    has_required = any(metric in key_metrics for metric in required_metrics)
    
    # 检查数值的合理性
    valid_ranges = {
        "PhA": (0, 20),      # 相位角范围
        "BMI": (10, 50),     # BMI范围
        "ASMI": (0, 20),     # ASMI范围
        "Body_Fat": (0, 100) # 体脂率范围
    }
    
    values_valid = True
    for metric, value in key_metrics.items():
        if metric in valid_ranges and value is not None:
            min_val, max_val = valid_ranges[metric]
            if not (min_val <= value <= max_val):
                logger.warning(f"BIA指标 {metric} 值 {value} 超出合理范围 [{min_val}, {max_val}]")
                values_valid = False
    
    is_valid = has_required and values_valid
    logger.info(f"BIA数据验证结果: {'有效' if is_valid else '无效'}")
    return is_valid


def _validate_glim_data(data: Dict[str, Any]) -> bool:
    """验证GLIM数据"""
    if not data.get("success", False):
        return False
    
    form_data = data.get("form_data", {})
    
    # 检查必需字段
    required_sections = ["phenotypic_criteria", "etiologic_criteria"]
    has_required_sections = all(section in form_data for section in required_sections)
    
    if not has_required_sections:
        return False
    
    # 检查是否至少填写了一些标准
    phenotypic = form_data["phenotypic_criteria"]
    etiologic = form_data["etiologic_criteria"]
    
    has_phenotypic = any(phenotypic.values()) if phenotypic else False
    has_etiologic = any(etiologic.values()) if etiologic else False
    
    is_valid = has_phenotypic or has_etiologic
    logger.info(f"GLIM数据验证结果: {'有效' if is_valid else '无效'}")
    return is_valid


def _validate_text_data(data: str) -> bool:
    """验证文本数据"""
    is_valid = isinstance(data, str) and len(data.strip()) > 0
    logger.info(f"文本数据验证结果: {'有效' if is_valid else '无效'}")
    return is_valid


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 测试GLIM数据解析
    test_glim = {
        "phenotypic_criteria": {
            "weight_loss": True,
            "low_bmi": True,
            "muscle_loss": False
        },
        "etiologic_criteria": {
            "food_intake_reduction": True,
            "disease_inflammation": True
        },
        "severity_criteria": {
            "severe_weight_loss": True,
            "severe_bmi": False
        }
    }
    
    result = parse_glim_data(test_glim)
    print("GLIM解析结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
