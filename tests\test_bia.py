#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BIA分析测试脚本
"""

import sys
import json
from pathlib import Path

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

from tools.bia_calculator import BIACalculator

def print_bia_calculation_results(results):
    """打印BIA计算结果"""
    if "error" in results:
        print(f"BIA计算失败: {results['error']}")
        return

    print("\n" + "="*50)
    print("BIA身体成分计算结果")
    print("="*50)

    # 患者信息
    patient = results["patient_info"]
    print(f"\n患者信息:")
    print(f"病案号: {patient['patient_id']}")
    print(f"姓名: {patient['name']}")
    print(f"性别: {patient['gender']}")
    print(f"年龄: {patient['age']}岁")
    print(f"身高: {patient['height']}cm")
    print(f"体重: {patient['weight']}kg")

    # BIA测量数据
    bia = results["bia_measurements"]
    print(f"\nBIA测量数据:")
    print(f"BMI: {bia['bmi']} kg/m²")
    print(f"相位角: {bia['phase_angle']}°")
    print(f"ASMI: {bia['asmi']} kg/m²")
    print(f"体脂率: {bia['body_fat_percentage']}%")
    print(f"体脂量: {bia['fat_mass']} kg")
    print(f"去脂体重: {bia['fat_free_mass']} kg")
    print(f"骨骼肌量: {bia['skeletal_muscle_mass']} kg")
    print(f"总水分: {bia['total_body_water']} L")
    print(f"细胞内水分: {bia['intracellular_water']} L")
    print(f"细胞外水分: {bia['extracellular_water']} L")
    print(f"内脏脂肪面积: {bia['visceral_fat_area']} cm²")
    print(f"腰臀比: {bia['waist_hip_ratio']}")
    print(f"浮肿指数: {bia['edema_index']}")
    print(f"基础代谢: {bia['basal_metabolic_rate']} kcal")

    print(f"\n计算时间: {results['calculation_timestamp']}")
    print("="*50)

def main():
    """运行BIA分析测试"""
    print("开始BIA分析测试...")
    
    # 创建BIA计算器
    calculator = BIACalculator()
    
    # 运行计算
    results = calculator.run_bia_calculation()

    # 打印结果
    print_bia_calculation_results(results)
    
    # 保存结果到JSON文件
    output_file = "bia_test_results.json"

    # 转换numpy类型为Python原生类型
    def convert_numpy_types(obj):
        if hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        else:
            return obj

    results_serializable = convert_numpy_types(results)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results_serializable, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    main()
