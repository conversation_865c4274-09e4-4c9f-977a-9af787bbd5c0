<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型检测测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #1a1a1a;
            color: #e0e0e0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .model-item {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #666;
        }
        .model-item.vision {
            border-left-color: #4CAF50;
        }
        .model-item.huatuo {
            border-left-color: #2196F3;
        }
        .model-item.other {
            border-left-color: #FF9800;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.loaded {
            background: #4CAF50;
            color: white;
        }
        .status.not-loaded {
            background: #f44336;
            color: white;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #1976D2;
        }
        pre {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 模型检测测试工具</h1>
        
        <div class="section">
            <h2>📊 当前状态</h2>
            <button onclick="testModelDetection()">🔄 刷新检测</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
            
            <div id="statusDisplay">
                <p>点击"刷新检测"开始测试...</p>
            </div>
        </div>
        
        <div class="section">
            <h2>📋 检测详情</h2>
            <div id="detectionDetails"></div>
        </div>
        
        <div class="section">
            <h2>📝 原始API响应</h2>
            <pre id="rawResponse">等待API响应...</pre>
        </div>
        
        <div class="section">
            <h2>🔍 检测日志</h2>
            <pre id="detectionLog"></pre>
        </div>
    </div>

    <script>
        let logContent = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('detectionLog').textContent = logContent;
        }
        
        function clearLog() {
            logContent = '';
            document.getElementById('detectionLog').textContent = '';
        }
        
        async function testModelDetection() {
            log('🔄 开始模型检测测试...');
            
            try {
                const response = await fetch('http://localhost:5000/api/model-status');
                const data = await response.json();
                
                log(`✅ API响应成功: ${response.status}`);
                
                // 显示原始响应
                document.getElementById('rawResponse').textContent = JSON.stringify(data, null, 2);
                
                if (data.success) {
                    const currentModels = data.current_model ? data.current_model.split(', ') : [];
                    const visionModel = data.vision_model || 'mimo-vl-7b-rl';
                    const huatuoModel = data.huatuo_model || 'freedomintelligence.huatuogpt-o1-7b';
                    
                    log(`📋 当前加载的模型数量: ${currentModels.length}`);
                    log(`📋 模型列表: ${JSON.stringify(currentModels)}`);
                    log(`🎯 目标视觉模型: ${visionModel}`);
                    log(`🎯 目标华佗模型: ${huatuoModel}`);
                    
                    // 详细检测每个模型
                    let detailsHTML = '';
                    let visionFound = false;
                    let huatuoFound = false;
                    
                    currentModels.forEach((model, index) => {
                        log(`🔍 检测模型 ${index + 1}: "${model}"`);
                        
                        // 视觉模型检测 - 更严格的匹配
                        const isVision = (
                            model.toLowerCase().includes('mimo-vl') ||
                            model.toLowerCase().includes('qwen2.5-vl') ||
                            model.toLowerCase().includes('qwen-vl') ||
                            model.toLowerCase().includes('llava') ||
                            model.toLowerCase().includes('cogvlm') ||
                            (model.toLowerCase().includes('vl-') &&
                             !model.toLowerCase().includes('huatuogpt') &&
                             !model.toLowerCase().includes('embed'))
                        );

                        // 华佗模型检测
                        const isHuatuo = (
                            model.toLowerCase().includes('huatuogpt') ||
                            model.toLowerCase().includes('huatuo')
                        );
                        
                        log(`   - 是否为视觉模型: ${isVision}`);
                        log(`   - 是否为华佗模型: ${isHuatuo}`);
                        
                        if (isVision) visionFound = true;
                        if (isHuatuo) huatuoFound = true;
                        
                        let modelClass = 'other';
                        if (isVision) modelClass = 'vision';
                        if (isHuatuo) modelClass = 'huatuo';
                        
                        detailsHTML += `
                            <div class="model-item ${modelClass}">
                                <strong>${model}</strong><br>
                                视觉模型: ${isVision ? '✅' : '❌'} | 
                                华佗模型: ${isHuatuo ? '✅' : '❌'}
                            </div>
                        `;
                    });
                    
                    log(`📊 最终检测结果:`);
                    log(`   - 视觉模型已加载: ${visionFound}`);
                    log(`   - 华佗模型已加载: ${huatuoFound}`);
                    
                    // 显示状态
                    const statusHTML = `
                        <div style="display: flex; gap: 20px; margin: 20px 0;">
                            <div>
                                <strong>视觉模型:</strong>
                                <span class="status ${visionFound ? 'loaded' : 'not-loaded'}">
                                    ${visionFound ? '✅ 已加载' : '❌ 未加载'}
                                </span>
                            </div>
                            <div>
                                <strong>华佗模型:</strong>
                                <span class="status ${huatuoFound ? 'loaded' : 'not-loaded'}">
                                    ${huatuoFound ? '✅ 已加载' : '❌ 未加载'}
                                </span>
                            </div>
                        </div>
                        <p><strong>总计:</strong> ${currentModels.length} 个模型已加载</p>
                    `;
                    
                    document.getElementById('statusDisplay').innerHTML = statusHTML;
                    document.getElementById('detectionDetails').innerHTML = detailsHTML;
                    
                } else {
                    log(`❌ API返回错误: ${data.error}`);
                    document.getElementById('statusDisplay').innerHTML = `<p style="color: #f44336;">❌ 检测失败: ${data.error}</p>`;
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
                document.getElementById('statusDisplay').innerHTML = `<p style="color: #f44336;">❌ 请求失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动检测
        window.addEventListener('load', testModelDetection);
    </script>
</body>
</html>
