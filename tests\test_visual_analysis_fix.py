#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视觉分析结果格式化修复
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.conversation_agent import ConversationAgent

def test_json_parsing_fix():
    """测试JSON解析修复"""
    print("🧪 测试视觉分析结果格式化修复")
    print("=" * 60)

    agent = ConversationAgent()

    # 测试用例1：正常的单个JSON块
    print("\n📋 测试用例1：正常的单个JSON块")
    normal_json = '''{
  "visual_analysis": [
    {
      "facial_region": "Upper Face",
      "findings": {
        "temporal_hollowing": {
          "present": false,
          "severity": "normal",
          "description": "太阳穴区域饱满",
          "confidence": 0.8
        }
      },
      "region_summary": "上脸部正常",
      "region_confidence": 0.8
    }
  ],
  "overall_assessment": {
    "malnutrition_likelihood": "normal",
    "confidence": 0.8,
    "key_findings": ["面部特征正常"],
    "clinical_notes": "营养状况良好",
    "image_quality_assessment": "good",
    "facial_overall_description": "面部饱满，营养状况良好"
  },
  "expert_metadata": {
    "analysis_approach": "systematic_morphological_assessment",
    "primary_indicators_used": ["temporal_hollowing"],
    "limitations": "基于单张图像的分析"
  }
}'''

    result1 = agent._format_visual_analysis_result(normal_json)
    print("✅ 正常JSON解析结果：")
    print(result1[:200] + "..." if len(result1) > 200 else result1)

    # 测试用例2：包含多个JSON块的情况
    print("\n📋 测试用例2：包含多个JSON块的情况")
    multiple_json = '''分析开始...

{
  "visual_analysis": [
    {
      "facial_region": "Upper Face",
      "findings": {
        "temporal_hollowing": {
          "present": true,
          "severity": "mild",
          "description": "轻度太阳穴凹陷",
          "confidence": 0.7
        }
      },
      "region_summary": "上脸部有轻微异常",
      "region_confidence": 0.7
    }
  ],
  "overall_assessment": {
    "malnutrition_likelihood": "mild",
    "confidence": 0.7,
    "key_findings": ["轻度营养不良征象"],
    "clinical_notes": "建议进一步检查",
    "image_quality_assessment": "good",
    "facial_overall_description": "面部略显消瘦"
  },
  "expert_metadata": {
    "analysis_approach": "systematic_morphological_assessment",
    "primary_indicators_used": ["temporal_hollowing"],
    "limitations": "基于单张图像的分析"
  }
}

分析完成。

{
  "visual_analysis": [
    {
      "facial_region": "Upper Face",
      "findings": {
        "temporal_hollowing": {
          "present": false,
          "severity": "normal",
          "description": "正常",
          "confidence": 0.6
        }
      }
    }
  ],
  "overall_assessment": {
    "malnutrition_likelihood": "normal",
    "confidence": 0.6
  }
}
'''

    result2 = agent._format_visual_analysis_result(multiple_json)
    print("✅ 多个JSON块解析结果：")
    print(result2[:200] + "..." if len(result2) > 200 else result2)

    # 测试用例3：无效JSON的情况
    print("\n📋 测试用例3：无效JSON的情况")
    invalid_json = "这不是一个有效的JSON格式的分析结果。图像显示患者面部特征正常。"

    result3 = agent._format_visual_analysis_result(invalid_json)
    print("✅ 无效JSON处理结果：")
    print(result3[:200] + "..." if len(result3) > 200 else result3)

    print("\n🎉 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_json_parsing_fix()
