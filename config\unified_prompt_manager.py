#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一提示词管理器
集中管理所有系统提示词，提供统一的访问接口
"""

from typing import Dict, Any, Optional
from loguru import logger

class UnifiedPromptManager:
    """统一提示词管理器"""
    
    def __init__(self):
        """初始化提示词管理器"""
        self._prompts_loaded = False
        self._facial_prompts = {}
        self._glim_prompts = {}
        self._comprehensive_prompts = {}
        self._load_all_prompts()
    
    def _load_all_prompts(self):
        """加载所有提示词模块"""
        try:
            # 加载面部分析提示词
            from .facial_analysis_prompts import (
                get_facial_analysis_system_prompt,
                get_facial_consensus_system_prompt,
                get_simple_facial_prompt
            )
            self._facial_prompts = {
                'system_prompt': get_facial_analysis_system_prompt(),
                'consensus_prompt': get_facial_consensus_system_prompt(),
                'simple_prompt_func': get_simple_facial_prompt
            }
            
            # 加载GLIM评估提示词
            from .glim_prompts import (
                get_glim_assessment_system_prompt,
                get_simple_glim_prompt,
                get_glim_form_analysis_prompt
            )
            self._glim_prompts = {
                'system_prompt': get_glim_assessment_system_prompt(),
                'simple_prompt_func': get_simple_glim_prompt,
                'form_analysis_func': get_glim_form_analysis_prompt
            }
            
            # 加载综合分析提示词
            from .comprehensive_analysis_prompts import (
                get_comprehensive_analysis_system_prompt,
                build_dynamic_comprehensive_prompt_with_length_control,
                build_comprehensive_analysis_prompt
            )
            self._comprehensive_prompts = {
                'system_prompt': get_comprehensive_analysis_system_prompt(),
                'dynamic_builder': build_dynamic_comprehensive_prompt_with_length_control,
                'simple_builder': build_comprehensive_analysis_prompt
            }
            
            self._prompts_loaded = True
            logger.info("✅ 统一提示词系统加载完成")
            
        except ImportError as e:
            logger.error(f"提示词模块加载失败: {e}")
            self._prompts_loaded = False
        except Exception as e:
            logger.error(f"提示词系统初始化失败: {e}")
            self._prompts_loaded = False
    
    def get_facial_analysis_prompt(self, prompt_type: str = "system") -> str:
        """
        获取面部分析提示词
        
        Args:
            prompt_type: 提示词类型 ("system", "consensus", "simple")
        
        Returns:
            相应的提示词
        """
        if not self._prompts_loaded:
            return self._get_fallback_facial_prompt(prompt_type)
        
        if prompt_type == "system":
            return self._facial_prompts.get('system_prompt', '')
        elif prompt_type == "consensus":
            return self._facial_prompts.get('consensus_prompt', '')
        elif prompt_type == "simple":
            simple_func = self._facial_prompts.get('simple_prompt_func')
            return simple_func() if simple_func else ''
        else:
            logger.warning(f"未知的面部分析提示词类型: {prompt_type}")
            return self._facial_prompts.get('system_prompt', '')
    
    def get_glim_assessment_prompt(self, prompt_type: str = "system", **kwargs) -> str:
        """
        获取GLIM评估提示词
        
        Args:
            prompt_type: 提示词类型 ("system", "simple", "form")
            **kwargs: 额外参数
        
        Returns:
            相应的提示词
        """
        if not self._prompts_loaded:
            return self._get_fallback_glim_prompt(prompt_type)
        
        if prompt_type == "system":
            return self._glim_prompts.get('system_prompt', '')
        elif prompt_type == "simple":
            simple_func = self._glim_prompts.get('simple_prompt_func')
            patient_data = kwargs.get('patient_data', {})
            return simple_func(patient_data) if simple_func else ''
        elif prompt_type == "form":
            form_func = self._glim_prompts.get('form_analysis_func')
            form_data = kwargs.get('form_data', {})
            return form_func(form_data) if form_func else ''
        else:
            logger.warning(f"未知的GLIM提示词类型: {prompt_type}")
            return self._glim_prompts.get('system_prompt', '')
    
    def get_comprehensive_analysis_prompt(self, prompt_type: str = "system", **kwargs) -> str:
        """
        获取综合分析提示词
        
        Args:
            prompt_type: 提示词类型 ("system", "dynamic", "simple")
            **kwargs: 额外参数
        
        Returns:
            相应的提示词
        """
        if not self._prompts_loaded:
            return self._get_fallback_comprehensive_prompt(prompt_type)
        
        if prompt_type == "system":
            return self._comprehensive_prompts.get('system_prompt', '')
        elif prompt_type == "dynamic":
            builder_func = self._comprehensive_prompts.get('dynamic_builder')
            if builder_func:
                patient_info = kwargs.get('patient_info', '')
                available_modules_data = kwargs.get('available_modules_data', {})
                max_tokens = kwargs.get('max_tokens', 3500)
                return builder_func(patient_info, available_modules_data, max_tokens)
            return ''
        elif prompt_type == "simple":
            builder_func = self._comprehensive_prompts.get('simple_builder')
            if builder_func:
                patient_info = kwargs.get('patient_info', {})
                collected_data = kwargs.get('collected_data', {})
                analysis_timestamp = kwargs.get('analysis_timestamp')
                return builder_func(patient_info, collected_data, analysis_timestamp)
            return ''
        else:
            logger.warning(f"未知的综合分析提示词类型: {prompt_type}")
            return self._comprehensive_prompts.get('system_prompt', '')
    
    def _get_fallback_facial_prompt(self, prompt_type: str) -> str:
        """面部分析降级提示词"""
        return """你是温州医科大学附属第一医院营养科的面部营养评估专家。
请分析面部图像中的营养相关特征，重点观察颞部、面颊、下颌等区域，
评估营养状况并提供专业建议。"""
    
    def _get_fallback_glim_prompt(self, prompt_type: str) -> str:
        """GLIM评估降级提示词"""
        return """你是温州医科大学附属第一医院营养科的GLIM标准专家。
请基于GLIM国际标准进行营养不良评估，检查表型标准和病因学标准，
给出准确的诊断结论。"""
    
    def _get_fallback_comprehensive_prompt(self, prompt_type: str) -> str:
        """综合分析降级提示词"""
        return """你是温州医科大学附属第一医院营养科的综合诊断专家。
请整合多模态评估数据，基于GLIM标准进行综合分析，
生成完整的营养状况诊断报告。"""
    
    def get_system_prompt(self, module: str, prompt_type: str = "system", **kwargs) -> str:
        """
        统一获取系统提示词的接口
        
        Args:
            module: 模块名称 ("facial", "glim", "comprehensive")
            prompt_type: 提示词类型
            **kwargs: 额外参数
        
        Returns:
            相应的系统提示词
        """
        if module == "facial":
            return self.get_facial_analysis_prompt(prompt_type)
        elif module == "glim":
            return self.get_glim_assessment_prompt(prompt_type, **kwargs)
        elif module == "comprehensive":
            return self.get_comprehensive_analysis_prompt(prompt_type, **kwargs)
        else:
            logger.error(f"未知的模块名称: {module}")
            return "你是温州医科大学附属第一医院营养科的专家。"
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """获取提示词统计信息"""
        stats = {
            "prompts_loaded": self._prompts_loaded,
            "facial_prompts_count": len(self._facial_prompts),
            "glim_prompts_count": len(self._glim_prompts),
            "comprehensive_prompts_count": len(self._comprehensive_prompts)
        }
        
        if self._prompts_loaded:
            # 添加提示词长度统计
            stats["facial_system_prompt_length"] = len(
                self._facial_prompts.get('system_prompt', '')
            )
            stats["glim_system_prompt_length"] = len(
                self._glim_prompts.get('system_prompt', '')
            )
            stats["comprehensive_system_prompt_length"] = len(
                self._comprehensive_prompts.get('system_prompt', '')
            )
        
        return stats
    
    def validate_prompts(self) -> Dict[str, bool]:
        """验证提示词完整性"""
        validation_results = {}
        
        # 验证面部分析提示词
        validation_results["facial_system"] = bool(
            self._facial_prompts.get('system_prompt') and 
            len(self._facial_prompts['system_prompt']) > 500
        )
        validation_results["facial_consensus"] = bool(
            self._facial_prompts.get('consensus_prompt') and
            len(self._facial_prompts['consensus_prompt']) > 500
        )
        
        # 验证GLIM评估提示词
        validation_results["glim_system"] = bool(
            self._glim_prompts.get('system_prompt') and
            len(self._glim_prompts['system_prompt']) > 500
        )
        
        # 验证综合分析提示词
        validation_results["comprehensive_system"] = bool(
            self._comprehensive_prompts.get('system_prompt') and
            len(self._comprehensive_prompts['system_prompt']) > 500
        )
        
        return validation_results


# 全局提示词管理器实例
_global_prompt_manager = None

def get_prompt_manager() -> UnifiedPromptManager:
    """获取全局提示词管理器实例"""
    global _global_prompt_manager
    if _global_prompt_manager is None:
        _global_prompt_manager = UnifiedPromptManager()
    return _global_prompt_manager

# 便捷函数
def get_system_prompt(module: str, prompt_type: str = "system", **kwargs) -> str:
    """便捷函数：获取系统提示词"""
    return get_prompt_manager().get_system_prompt(module, prompt_type, **kwargs)

def get_facial_prompt(prompt_type: str = "system") -> str:
    """便捷函数：获取面部分析提示词"""
    return get_prompt_manager().get_facial_analysis_prompt(prompt_type)

def get_glim_prompt(prompt_type: str = "system", **kwargs) -> str:
    """便捷函数：获取GLIM评估提示词"""
    return get_prompt_manager().get_glim_assessment_prompt(prompt_type, **kwargs)

def get_comprehensive_prompt(prompt_type: str = "system", **kwargs) -> str:
    """便捷函数：获取综合分析提示词"""
    return get_prompt_manager().get_comprehensive_analysis_prompt(prompt_type, **kwargs)
