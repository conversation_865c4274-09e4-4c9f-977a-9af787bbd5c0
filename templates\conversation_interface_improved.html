<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营养诊断智能体对话界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #f7f7f8;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部工具栏 */
        .toolbar {
            background: white;
            height: 64px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0 20px;
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .toolbar-btn {
            background: #f4f4f5;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .toolbar-btn:hover {
            background: #e4e4e7;
        }
        
        .toolbar-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        /* 主容器 - 水平布局 */
        .main-container {
            flex: 1;
            display: flex;
            padding-top: 64px; /* 为工具栏留出空间 */
            height: 100vh;
        }
        
        /* 左侧自动模式面板 */
        .auto-mode-sidebar {
            width: 380px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }
        
        .auto-mode-sidebar.hidden {
            transform: translateX(-100%);
            position: absolute;
            z-index: -1;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .sidebar-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 文件上传区域 */
        .upload-section {
            margin-bottom: 24px;
        }
        
        .upload-zone {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 32px 16px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .upload-zone.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .upload-text h4 {
            color: #374151;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .upload-text p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .file-types-info {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-bottom: 16px;
        }
        
        .file-type-tag {
            background: #f3f4f6;
            color: #4b5563;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .browse-files-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .browse-files-btn:hover {
            background: #2563eb;
        }
        
        /* 已选文件列表 */
        .selected-files {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .file-icon {
            font-size: 20px;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .file-details {
            font-size: 12px;
            color: #6b7280;
        }
        
        .file-remove {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }
        
        .file-remove:hover {
            background: #fee2e2;
        }
        
        /* 分析状态区域 */
        .analysis-status {
            margin-bottom: 24px;
        }
        
        .status-item {
            background: #f9fafb;
            border-left: 4px solid #e5e7eb;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
        }
        
        .status-item.active {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }
        
        .status-item.completed {
            border-left-color: #10b981;
            background: #ecfdf5;
        }
        
        .status-item.error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        
        .status-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .status-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.pending {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .status-badge.active {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .status-badge.completed {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-badge.error {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .status-description {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        
        /* 操作按钮 */
        .action-buttons {
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .start-analysis-btn {
            width: 100%;
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .start-analysis-btn:hover:not(:disabled) {
            background: #059669;
        }
        
        .start-analysis-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .clear-btn {
            width: 100%;
            background: #f3f4f6;
            color: #6b7280;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 8px;
        }
        
        .clear-btn:hover {
            background: #e5e7eb;
        }
        
        /* 右侧对话区域 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            transition: margin-left 0.3s ease;
        }
        
        .chat-area.full-width {
            margin-left: -380px;
        }
        
        .chat-container {
            flex: 1;
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px 0;
            scroll-behavior: smooth;
        }
        
        /* 消息样式 */
        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
            align-items: flex-start;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .message.assistant .message-avatar {
            background: #19c37d;
            color: white;
        }
        
        .message.user .message-avatar {
            background: #343541;
            color: white;
        }
        
        .message-content {
            flex: 1;
            min-width: 0;
        }
        
        .message-text {
            color: #374151;
            line-height: 1.6;
            word-wrap: break-word;
        }
        
        /* 输入区域 */
        .input-area {
            display: flex;
            gap: 12px;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .input-container {
            flex: 1;
            position: relative;
        }
        
        .user-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            font-size: 16px;
            resize: none;
            outline: none;
            font-family: inherit;
        }
        
        .user-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .send-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
        }
        
        .send-btn:hover:not(:disabled) {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .auto-mode-sidebar {
                width: 320px;
            }
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .auto-mode-sidebar {
                width: 100%;
                height: 50vh;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }
            
            .auto-mode-sidebar.hidden {
                transform: translateY(-100%);
                height: 0;
            }
            
            .chat-area.full-width {
                margin-left: 0;
            }
        }
        
        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="toolbar">
        <div class="toolbar-left">
            <h2>🧠 营养诊断智能体</h2>
        </div>
        <div>
            <button class="toolbar-btn" id="modeToggleBtn" onclick="toggleMode()">
                <span id="modeToggleText">🤖 切换到自动模式</span>
            </button>
            <button class="toolbar-btn" onclick="clearConversation()">
                🗑️ 清空对话
            </button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧自动模式面板 -->
        <div class="auto-mode-sidebar hidden" id="autoModeSidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">🤖 智能批量分析模式</div>
                <div class="sidebar-subtitle">一次性上传所有数据，系统将自动识别文件类型并进行智能分析</div>
            </div>
            
            <div class="sidebar-content">
                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <div class="section-title">📂 文件上传</div>
                    <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">📂</div>
                        <div class="upload-text">
                            <h4>拖拽文件到此处 或 点击选择</h4>
                            <p>支持：面部图片、BIA数据、GLIM表单等</p>
                            <div class="file-types-info">
                                <span class="file-type-tag">📷 图片</span>
                                <span class="file-type-tag">📊 表格</span>
                                <span class="file-type-tag">📋 表单</span>
                                <span class="file-type-tag">📄 文档</span>
                            </div>
                        </div>
                        <button class="browse-files-btn">选择文件</button>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.xlsx,.xls,.csv,.json,.pdf,.doc,.docx" style="display: none;">
                </div>
                
                <!-- 已选文件列表 -->
                <div class="selected-files" id="selectedFiles" style="display: none;">
                    <div class="section-title">📋 已选择的文件</div>
                    <div id="filesList"></div>
                </div>
                
                <!-- 分析状态 -->
                <div class="analysis-status" id="analysisStatus" style="display: none;">
                    <div class="section-title">📊 分析进度</div>
                    
                    <div class="status-item" id="planningStatus">
                        <div class="status-header">
                            <div class="status-title">🧠 分析规划</div>
                            <div class="status-badge pending" id="planningBadge">等待中</div>
                        </div>
                        <div class="status-description" id="planningDesc">等待开始分析...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="planningProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="status-item" id="toolsStatus">
                        <div class="status-header">
                            <div class="status-title">🔧 工具执行</div>
                            <div class="status-badge pending" id="toolsBadge">等待中</div>
                        </div>
                        <div class="status-description" id="toolsDesc">等待规划完成...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="toolsProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="status-item" id="finalStatus">
                        <div class="status-header">
                            <div class="status-title">🎯 综合分析</div>
                            <div class="status-badge pending" id="finalBadge">等待中</div>
                        </div>
                        <div class="status-description" id="finalDesc">等待工具执行完成...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="finalProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="start-analysis-btn" id="startAnalysisBtn" onclick="startAnalysis()" disabled>
                    🚀 开始智能分析
                </button>
                <button class="clear-btn" onclick="clearFiles()">清空文件</button>
            </div>
        </div>
        
        <!-- 右侧对话区域 -->
        <div class="chat-area" id="chatArea">
            <div class="chat-container">
                <div class="messages-area" id="messagesArea">
                    <div class="message assistant">
                        <div class="message-avatar">AI</div>
                        <div class="message-content">
                            <div class="message-text">您好！我是营养诊断智能体，可以帮助您进行营养状况评估。您可以选择手动对话模式或切换到自动分析模式批量处理数据。</div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="input-area" id="inputArea">
                    <div class="input-container">
                        <textarea class="user-input" id="userInput" placeholder="请输入您的问题..." rows="1"></textarea>
                    </div>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let isAutoMode = false;
        let selectedFiles = [];
        let analysisInProgress = false;
        let statusPollingInterval = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            checkModelStatus();
        });
        
        // 事件监听器初始化
        function initializeEventListeners() {
            // 文件输入事件
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
            
            // 拖拽事件
            const uploadZone = document.getElementById('uploadZone');
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleFileDrop);
            
            // 输入框自动调整高度
            const userInput = document.getElementById('userInput');
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // 回车发送消息
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }
        
        // 模式切换
        function toggleMode() {
            isAutoMode = !isAutoMode;
            const sidebar = document.getElementById('autoModeSidebar');
            const chatArea = document.getElementById('chatArea');
            const modeToggleBtn = document.getElementById('modeToggleBtn');
            const modeToggleText = document.getElementById('modeToggleText');
            const inputArea = document.getElementById('inputArea');
            
            if (isAutoMode) {
                sidebar.classList.remove('hidden');
                chatArea.classList.remove('full-width');
                modeToggleBtn.classList.add('active');
                modeToggleText.textContent = '📋 切换到手动模式';
                inputArea.style.display = 'none';
                
                // 添加欢迎消息
                addMessage('assistant', '已切换到自动分析模式。请在左侧上传需要分析的文件，系统将自动识别文件类型并进行智能分析。');
            } else {
                sidebar.classList.add('hidden');
                chatArea.classList.add('full-width');
                modeToggleBtn.classList.remove('active');
                modeToggleText.textContent = '🤖 切换到自动模式';
                inputArea.style.display = 'flex';
                
                // 清理自动模式状态
                clearFiles();
                stopStatusPolling();
                
                addMessage('assistant', '已切换到手动对话模式。您可以直接与我对话，询问营养相关问题。');
            }
        }
        
        // 文件处理
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }
        
        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }
        
        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            addFiles(files);
        }
        
        function addFiles(files) {
            files.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });
            updateFilesList();
            updateStartButton();
        }
        
        function updateFilesList() {
            const filesList = document.getElementById('filesList');
            const selectedFilesSection = document.getElementById('selectedFiles');
            
            if (selectedFiles.length === 0) {
                selectedFilesSection.style.display = 'none';
                return;
            }
            
            selectedFilesSection.style.display = 'block';
            filesList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const fileIcon = getFileIcon(file.type, file.name);
                const fileSize = formatFileSize(file.size);
                const fileType = getFileType(file.type, file.name);
                
                fileItem.innerHTML = `
                    <div class="file-icon">${fileIcon}</div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-details">${fileType} • ${fileSize}</div>
                    </div>
                    <button class="file-remove" onclick="removeFile(${index})">✕</button>
                `;
                
                filesList.appendChild(fileItem);
            });
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFilesList();
            updateStartButton();
        }
        
        function clearFiles() {
            selectedFiles = [];
            updateFilesList();
            updateStartButton();
            hideAnalysisStatus();
        }
        
        function updateStartButton() {
            const startBtn = document.getElementById('startAnalysisBtn');
            startBtn.disabled = selectedFiles.length === 0 || analysisInProgress;
        }
        
        // 文件工具函数
        function getFileIcon(type, name) {
            if (type.startsWith('image/')) return '📷';
            if (name.endsWith('.xlsx') || name.endsWith('.xls') || name.endsWith('.csv')) return '📊';
            if (name.endsWith('.json')) return '📋';
            if (name.endsWith('.pdf')) return '📄';
            if (name.endsWith('.doc') || name.endsWith('.docx')) return '📝';
            return '📎';
        }
        
        function getFileType(type, name) {
            if (type.startsWith('image/')) return '图片文件';
            if (name.endsWith('.xlsx') || name.endsWith('.xls')) return 'Excel表格';
            if (name.endsWith('.csv')) return 'CSV数据';
            if (name.endsWith('.json')) return 'JSON数据';
            if (name.endsWith('.pdf')) return 'PDF文档';
            if (name.endsWith('.doc') || name.endsWith('.docx')) return 'Word文档';
            return '文档文件';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 分析流程
        async function startAnalysis() {
            if (selectedFiles.length === 0 || analysisInProgress) return;
            
            analysisInProgress = true;
            updateStartButton();
            showAnalysisStatus();
            
            try {
                // 显示开始分析消息
                addMessage('assistant', `开始智能分析 ${selectedFiles.length} 个文件，请稍候...`);
                
                // 生成会话ID
                const sessionId = getCurrentSessionId();
                
                // 上传文件
                const formData = new FormData();
                formData.append('session_id', sessionId);
                selectedFiles.forEach(file => {
                    formData.append('files[]', file);
                });
                
                // 更新状态：规划阶段
                updateAnalysisStatus('planning', 'active', '正在制定分析计划...', 30);
                
                const response = await fetch('/api/upload-batch', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`上传失败: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    // 开始轮询状态
                    startStatusPolling(sessionId);
                } else {
                    throw new Error(result.message || '分析启动失败');
                }
                
            } catch (error) {
                console.error('Analysis error:', error);
                analysisInProgress = false;
                updateStartButton();
                updateAnalysisStatus('planning', 'error', `分析失败: ${error.message}`, 0);
                addMessage('assistant', `分析过程出现错误: ${error.message}`);
            }
        }
        
        function showAnalysisStatus() {
            document.getElementById('analysisStatus').style.display = 'block';
            resetAnalysisStatus();
        }
        
        function hideAnalysisStatus() {
            document.getElementById('analysisStatus').style.display = 'none';
        }
        
        function resetAnalysisStatus() {
            updateAnalysisStatus('planning', 'pending', '等待开始分析...', 0);
            updateAnalysisStatus('tools', 'pending', '等待规划完成...', 0);
            updateAnalysisStatus('final', 'pending', '等待工具执行完成...', 0);
        }
        
        function updateAnalysisStatus(stage, status, description, progress) {
            const stageElement = document.getElementById(`${stage}Status`);
            const badgeElement = document.getElementById(`${stage}Badge`);
            const descElement = document.getElementById(`${stage}Desc`);
            const progressElement = document.getElementById(`${stage}Progress`);
            
            // 更新状态项样式
            stageElement.className = `status-item ${status}`;
            
            // 更新徽章
            badgeElement.className = `status-badge ${status}`;
            badgeElement.textContent = getStatusText(status);
            
            // 更新描述
            descElement.textContent = description;
            
            // 更新进度条
            progressElement.style.width = `${progress}%`;
        }
        
        function getStatusText(status) {
            switch (status) {
                case 'pending': return '等待中';
                case 'active': return '进行中';
                case 'completed': return '已完成';
                case 'error': return '失败';
                default: return '未知';
            }
        }
        
        // 状态轮询
        function startStatusPolling(sessionId) {
            statusPollingInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/session-status/${sessionId}`);
                    const status = await response.json();
                    
                    if (status.success) {
                        updateAnalysisStatusFromServer(status.data);
                        
                        // 检查是否完成
                        if (status.data.status === 'completed' || status.data.status === 'failed') {
                            stopStatusPolling();
                            analysisInProgress = false;
                            updateStartButton();
                            
                            if (status.data.status === 'completed') {
                                addMessage('assistant', status.data.final_response || '分析完成！');
                            } else {
                                addMessage('assistant', '分析失败，请重试。');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Status polling error:', error);
                }
            }, 2000);
        }
        
        function stopStatusPolling() {
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
                statusPollingInterval = null;
            }
        }
        
        function updateAnalysisStatusFromServer(data) {
            // 根据服务器返回的状态更新UI
            if (data.current_phase === 'analysis_planning') {
                updateAnalysisStatus('planning', 'active', '正在制定分析计划...', 50);
            } else if (data.current_phase === 'batch_tools') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'active', '正在执行分析工具...', 30);
            } else if (data.current_phase === 'final_analysis') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'completed', '工具执行已完成', 100);
                updateAnalysisStatus('final', 'active', '正在进行综合分析...', 50);
            } else if (data.status === 'completed') {
                updateAnalysisStatus('planning', 'completed', '分析计划已完成', 100);
                updateAnalysisStatus('tools', 'completed', '工具执行已完成', 100);
                updateAnalysisStatus('final', 'completed', '综合分析已完成', 100);
            }
        }
        
        // 消息处理
        function addMessage(sender, content) {
            const messagesArea = document.getElementById('messagesArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = sender === 'assistant' ? 'AI' : 'You';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${content}</div>
                </div>
            `;
            
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        async function sendMessage() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage('user', message);
            userInput.value = '';
            userInput.style.height = 'auto';
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            
            try {
                const response = await fetch('/api/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: getCurrentSessionId()
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addMessage('assistant', result.response);
                } else {
                    addMessage('assistant', '抱歉，处理您的消息时出现了错误，请重试。');
                }
            } catch (error) {
                console.error('Send message error:', error);
                addMessage('assistant', '网络错误，请检查连接后重试。');
            } finally {
                sendBtn.disabled = false;
            }
        }
        
        function clearConversation() {
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.innerHTML = `
                <div class="message assistant">
                    <div class="message-avatar">AI</div>
                    <div class="message-content">
                        <div class="message-text">对话已清空。您好！我是营养诊断智能体，可以帮助您进行营养状况评估。</div>
                    </div>
                </div>
            `;
        }
        
        // 工具函数
        function getCurrentSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        async function checkModelStatus() {
            try {
                const response = await fetch('/api/model-status');
                const status = await response.json();
                console.log('Model status:', status);
            } catch (error) {
                console.error('Model status check failed:', error);
            }
        }
        
        // 页面卸载清理
        window.addEventListener('beforeunload', function() {
            stopStatusPolling();
        });
    </script>
</body>
</html>
