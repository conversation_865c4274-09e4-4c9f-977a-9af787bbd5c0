╔═══════════════════════════════════════════════════════════════════════════════════════╗
║                          🚀 营养不良诊断智能体系统架构流程图                              ║
║                     基于LangGraph + 多模态LLM + 本地化部署                              ║
╚═══════════════════════════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  🎯 系统输入层                                        │
└─────────────────────────────────────────────────────────────────────────────────────┘
        │
        ├── 📷 面部照片 (PNG/JPG)     ├── 📋 GLIM评估表单     ├── 📊 BIA数据 (Excel)
        │   • 正面清晰照片            │   • 用户基本信息        │   • 生物电阻抗数据
        │   • 营养相关面部特征        │   • 体重变化记录        │   • 体成分分析
        │   • 多角度可选              │   • 疾病史信息          │   • 肌肉质量数据
        │
        ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               🔄 系统启动与初始化                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
        │
        │ 
        │ • 清理历史模型状态
        │ • 初始化AgentState
        │ • 建立会话ID
        │ • 设置当前工作阶段="greeting"
        │
        ▼
╔═══════════════════════════════════════════════════════════════════════════════════════╗
║                            🧠 AI主脑决策中心 (brain_decision)                         ║
║                                   [核心路由节点]                                     ║
╚═══════════════════════════════════════════════════════════════════════════════════════╝
        │
        │ 根据用户输入智能路由：
        │
        ├─────────────────────────────────────────────────────────────────────────────┐
        │                             📝 数据收集阶段                                  │
        │                        [greeting/data_collection]                          │
        └─────────────────────────────────────────────────────────────────────────────┘
        │                                  │
        │ [简化状态机逻辑]                    │
        │ • 问候用户                         │
        │ • 收集用户档案                     │
        │ • 引导数据选择                     │
        │ • 处理用户输入                     │
        │                                  │
        ▼                                  ▼
    ┌─────────────┐                ┌─────────────┐
    │ 👥 对话模式    │                │ 🤖 自动模式    │
    │ [手动引导]     │ ←─────────────→ │ [自动调用工具处理]     │
    └─────────────┘                └─────────────┘
        │                                  │
        │                                  │
        │ • GLIM问卷填写                    │ • 文件批量上传
        │ • 面部照片上传                    │ • 自动数据识别
        │ • BIA数据上传                     │ • 智能类型检测
        │ • 逐步收集确认                    │ •串行处理分析
        │                                  │
        ▼                                  ▼
┌─────────────────────────────────┐  ┌─────────────────────────────────┐
│        传统分析路径              │  │        自动模式路径              │
├─────────────────────────────────┤  ├─────────────────────────────────┤
│ 📊 call_bia_analysis           │  │ 🔄 auto_ingestion               │
│ 👁️ call_vision_analysis        │  │ 📈 auto_serial_analysis         │
│ 📋 call_comprehensive_analysis │  │                                 │
│ 🏁 finalize_conversation       │  │                                 │
└─────────────────────────────────┘  └─────────────────────────────────┘
        │                                  │
        │ [每个分析后直接END]                 │ [分析后回到brain_decision]
        │                                  │
        ▼                                  ▼
    ┌─────────┐                    ┌─────────────────┐
    │   END   │◄───────────────────│  brain_decision │
    └─────────┘                    └─────────────────┘
                                           │
                                           │ [满足工具调用条件时]
                                           ▼
╔═══════════════════════════════════════════════════════════════════════════════════════╗
║                         🤖 智能体调用工具 (tool_calling_agent)                        ║
║                              [HuatuoGPT]                               ║
╚═══════════════════════════════════════════════════════════════════════════════════════╝
                                           │
                                           │
    ┌─────────────────────────────────────────────────────────────────────────────────┐
    │                           🔧 智能体调用工具内部工作流                             │
    └─────────────────────────────────────────────────────────────────────────────────┘
                                           │
                                           ▼
    ╔═════════════════════════════════════════════════════════════════════════════════╗
    ║                       📋 分析规划节点 (analysis_planning)                       ║
    ║                            [HuatuoGPT制定执行计划]                             ║
    ╚═════════════════════════════════════════════════════════════════════════════════╝
                                           │
                                           │ HuatuoGPT分析：
                                           │ • 评估所有上传数据
                                           │ • 制定分析策略
                                           │ • 确定需要调用的工具
                                           │ • 规划执行顺序
                                           │
                                           ▼
                                 ┌─────────────────┐
                                 │  需要工具执行？  │
                                 └─────────────────┘
                                      │         │
                                   Yes│         │No
                                      │         │
                                      ▼         ▼
    ╔═════════════════════════════════════╗   ╔═════════════════════════════════════╗
    ║      🔧 批量工具执行节点             ║   ║       📊 最终分析节点                ║
    ║      (batch_tools)                 ║   ║      (final_analysis)              ║
    ║                                    ║   ║                                    ║
    ║  串行执行所有计划的工具：             ║   ║  HuatuoGPT综合分析：                ║
    ║                                    ║   ║  • 基于现有数据                     ║
    ║  🔹 GLIM处理工具                    ║   ║  • 生成诊断结论                     ║
    ║    • GLIMFormProcessor             ║   ║  • 提供专业建议                     ║
    ║    • 标准化评估逻辑                 ║   ║  • 个性化报告                       ║
    ║    • 诊断结果生成                   ║───┤                                    ║
    ║                                    ║   ║                                    ║
    ║  🔹 面部分析工具                    ║   ║                                    ║
    ║    • 多VLM投票机制                  ║   ║                                    ║
    ║    • 营养特征识别                   ║   ║                                    ║
    ║    • 置信度评估                     ║   ║                                    ║
    ║                                    ║   ║                                    ║
    ║  🔹 BIA数据分析工具                 ║   ║                                    ║
    ║    • BIACalculator                 ║   ║                                    ║
    ║    • 体成分计算                     ║   ║                                    ║
    ║    • 营养风险评估                   ║   ║                                    ║
    ║                                    ║   ║                                    ║
    ║  🔹 知识库查询工具                  ║   ║                                    ║
    ║    • 营养-症状关联                  ║   ║                                    ║
    ║    • 临床指导原则                   ║   ║                                    ║
    ╚═════════════════════════════════════╝   ╚═════════════════════════════════════╝
                                           │
                                           ▼
╔═══════════════════════════════════════════════════════════════════════════════════════╗
║                            📊 最终综合分析与报告生成                                   ║
║                              [HuatuoGPT综合推理]                                    ║
╚═══════════════════════════════════════════════════════════════════════════════════════╝
                                           │
                                           │ 多模态数据融合：
                                           │ • GLIM标准诊断结果
                                           │ • 面部分析置信度评估
                                           │ • BIA体成分指标分析
                                           │ • 跨模态一致性验证
                                           │ • 个体化因素考量
                                           │
                                           ▼
    ┌─────────────────────────────────────────────────────────────────────────────────┐
    │                              📄 最终诊断报告                                      │
    ├─────────────────────────────────────────────────────────────────────────────────┤
    │ 🎯 诊断结论：                                                                    │
    │   • 营养状况评估 (正常/轻度/中度/重度营养不良)                     │
    │   • GLIM诊断标准符合性分析                                                 │
    │   • 主要营养问题识别                                                            │
    │                                                                                          │
    │ 📊 支持证据：                                                                    │
    │   • GLIM表型标准 (BMI、体重减轻、肌肉质量)                        │
    │   • GLIM病因学标准 (摄食障碍、疾病负担)                              │
    │   • 面部特征分析结果与置信度                                               │
    │   • BIA体成分指标详细分析                                                    │
    │                                                                                           │
    │ 💡 个性化建议：                                                                 │
    │   • 营养干预策略                                                                  │
    │   • 饮食调整建议                                                                  │
    │   • 运动康复方案                                                                 │
    │   • 随访监测计划                                                                  │
    │   • 专科转诊建议                                                                  │
    │                                                                                         │
    │ ⚠️ 重要声明：                                                                     │
    │   • AI辅助筛查，不可替代医生诊断                                        │
    │   • 建议携带报告咨询营养科医生                                            │
    └─────────────────────────────────────────────────────────────────────────────────┘
                                           │
                                           ▼
                                    ┌─────────────┐
                                    │     END     │
                                    │  [会话结束]  │
                                    └─────────────┘