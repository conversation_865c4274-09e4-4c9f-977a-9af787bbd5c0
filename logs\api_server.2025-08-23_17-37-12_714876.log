2025-08-23 17:37:12.713 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755941832142_2jrmg6zxc, 消息长度: 0
2025-08-23 17:37:12.714 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-23 17:37:12.757 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:37:12.757 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-23 17:37:12.759 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:44:07.040 | INFO     | __main__:submit_profile:442 - 收到用户档案提交，会话: session_1755941832142_2jrmg6zxc
2025-08-23 17:44:07.045 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:44:07.046 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-23 17:44:07.047 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-23 17:44:09.070 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755941832142_2jrmg6zxc, 消息长度: 8
2025-08-23 17:44:09.070 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-23 17:44:09.084 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:44:09.084 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-23 17:44:09.085 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:44:19.435 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755941832142_2jrmg6zxc, 消息长度: 11
2025-08-23 17:44:19.435 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-23 17:44:19.435 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-23 17:44:19.436 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-23 17:44:19.437 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:44:19.438 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-23 17:44:19.440 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:44:24.503 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755941832142_2jrmg6zxc, 消息长度: 5
2025-08-23 17:44:24.503 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-23 17:44:24.505 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:44:24.505 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:44:24.505 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-23 17:44:24.505 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-23 17:44:24.505 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-23 17:44:24.505 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-23 17:44:24.507 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:45:02.627 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755941832142_2jrmg6zxc，文件: 用户相关数据.xlsx
2025-08-23 17:45:02.632 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:562 - 执行BIA数据分析
2025-08-23 17:45:02.646 | ERROR    | src.tools.bia_calculator:load_bia_data:69 - 加载BIA数据失败: Missing optional dependency 'openpyxl'.  Use pip or conda to install openpyxl.
2025-08-23 17:45:02.646 | ERROR    | src.agents.conversation_agent:call_bia_analysis_node:591 - BIA分析异常: BIA数据文件读取失败
2025-08-23 17:45:02.658 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:45:02.659 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:45:02.659 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-23 17:45:02.659 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-23 17:45:02.659 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: 📊 已上传BIA数据：xlsx
2025-08-23 17:45:02.659 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-23 17:45:02.661 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 失败
2025-08-23 17:49:13.013 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755942552442_rrupglvq6, 消息长度: 0
2025-08-23 17:49:13.014 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-23 17:49:13.024 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:49:13.024 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-23 17:49:13.027 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:49:17.920 | INFO     | __main__:submit_profile:442 - 收到用户档案提交，会话: session_1755942552442_rrupglvq6
2025-08-23 17:49:17.921 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:49:17.921 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-23 17:49:17.921 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-23 17:49:20.009 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755942552442_rrupglvq6, 消息长度: 8
2025-08-23 17:49:20.009 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-23 17:49:20.012 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:49:20.013 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-23 17:49:20.014 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:49:28.574 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755942552442_rrupglvq6, 消息长度: 11
2025-08-23 17:49:28.574 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-23 17:49:28.574 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-23 17:49:28.574 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-23 17:49:28.574 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:49:28.579 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-23 17:49:28.580 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:49:30.472 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755942552442_rrupglvq6, 消息长度: 5
2025-08-23 17:49:30.472 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-23 17:49:30.473 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:49:30.473 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:49:30.473 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-23 17:49:30.473 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-23 17:49:30.473 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-23 17:49:30.473 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-23 17:49:30.476 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:49:37.995 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755942552442_rrupglvq6，文件: 用户相关数据.xlsx
2025-08-23 17:49:37.998 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:562 - 执行BIA数据分析
2025-08-23 17:49:38.531 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-23 17:49:38.547 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-23 17:49:38.548 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:586 - BIA数据分析完成
2025-08-23 17:49:38.551 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:49:38.551 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:49:38.551 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-23 17:49:38.551 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-23 17:49:38.551 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: 📊 已上传BIA数据：xlsx
2025-08-23 17:49:38.552 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-23 17:49:38.553 | INFO     | __main__:upload_bia:389 - 开始转换BIA分析数据为JSON可序列化格式
2025-08-23 17:49:38.554 | INFO     | __main__:upload_bia:391 - BIA数据转换完成
2025-08-23 17:49:38.554 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
2025-08-23 17:56:55.235 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755943014649_kig46tgi9, 消息长度: 0
2025-08-23 17:56:55.235 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-23 17:56:55.246 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:56:55.246 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-23 17:56:55.249 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:56:59.435 | INFO     | __main__:submit_profile:445 - 收到用户档案提交，会话: session_1755943014649_kig46tgi9
2025-08-23 17:56:59.437 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-23 17:56:59.437 | INFO     | src.agents.conversation_agent:brain_decision_node:278 - 用户已填写档案，提供数据收集选项
2025-08-23 17:56:59.438 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-23 17:57:01.241 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755943014649_kig46tgi9, 消息长度: 8
2025-08-23 17:57:01.241 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-23 17:57:01.243 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:57:01.243 | INFO     | src.agents.conversation_agent:brain_decision_node:243 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-23 17:57:01.243 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:57:10.849 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755943014649_kig46tgi9, 消息长度: 11
2025-08-23 17:57:10.849 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-23 17:57:10.849 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-23 17:57:10.850 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-23 17:57:10.851 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:57:10.851 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-23 17:57:10.852 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:57:18.480 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755943014649_kig46tgi9, 消息长度: 5
2025-08-23 17:57:18.480 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-23 17:57:18.482 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:57:18.482 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:57:18.482 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-23 17:57:18.482 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-23 17:57:18.483 | INFO     | src.agents.conversation_agent:_fallback_decision:476 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-23 17:57:18.483 | INFO     | src.agents.conversation_agent:_fallback_decision:486 - 🚀 降级模式：设置BIA上传前端动作
2025-08-23 17:57:18.484 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-23 17:57:24.768 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755943014649_kig46tgi9，文件: 用户相关数据.xlsx
2025-08-23 17:57:24.784 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:605 - 执行BIA数据分析
2025-08-23 17:57:25.039 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-23 17:57:25.039 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-23 17:57:25.040 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:629 - BIA数据分析完成
2025-08-23 17:57:25.041 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-23 17:57:25.042 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-23 17:57:25.042 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-23 17:57:25.042 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-23 17:57:25.042 | INFO     | src.agents.conversation_agent:_fallback_decision:463 - 🔄 降级模式：用户完成BIA数据上传，用户输入: 📊 已上传BIA数据：xlsx
2025-08-23 17:57:25.043 | INFO     | __main__:upload_bia:361 - === BIA数据提取结果 ===
2025-08-23 17:57:25.043 | INFO     | __main__:upload_bia:362 - BIA分析数据: {'patient_id': 'unknown', 'basic_info': {'姓名': '陈生长', '性别': '男', '年龄': 69, '身高': 170.0, '体重': 72.6, 'BMI': 25.12, '诊断': '升结肠恶性肿瘤', 'NRS2002': 3, 'GLIM': '营养不良'}, 'nutritional_indicators': {'相位角': {'值': 17.48, '状态': '高', '临床意义': '可能存在脱水或炎症状态'}, 'BMI': {'值': 25.12, '状态': '超重', '分类': '超重'}, '基础代谢': {'值': 1638.85, '评估': '需结合年龄性别评估'}}, 'body_composition': {'体脂': {'百分比': 19.09, '正常范围': '10.0-20.0%', '绝对量': 13.86, '状态': '正常'}, '去脂体重': {'值': 58.74, '正常范围': '46.98-57.42kg', '状态': '偏高'}, '内脏脂肪': {'面积': 5.0, '评估': '正常'}, '腰臀比': {'值': 0.78, '正常范围': '0.8-0.9', '状态': '偏低'}}, 'muscle_assessment': {'ASMI': {'值': 14.78, '状态': '正常', '临床意义': '四肢骨骼肌质量指数，用于肌少症诊断'}, '骨骼肌': {'总量': 39.65, '正常范围': '26.12-31.93kg', '状态': '偏高'}, '肌肉分布': {'总肌肉量': 53.27, '右上肢': 4.98, '左上肢': 5.24, '躯干': 39.92, '右下肢': 17.33, '左下肢': 15.16}, '肌肉功能': {'上臂围度': 38.28, '上臂肌肉围度': 37.88}}, 'hydration_status': {'总水分': {'值': 38.76, '正常范围': '34.53-42.2L', '状态': '正常'}, '细胞内水分': {'值': 31.93, '正常范围': '21.41-26.17L'}, '细胞外水分': {'值': 6.83, '正常范围': '13.12-16.04L'}, '浮肿评估': {'浮肿指数': 0.176, '状态': '正常'}}, 'risk_assessment': {'风险等级': '中风险', '风险因素': ['NRS2002评分≥3分，存在营养风险', 'GLIM诊断为营养不良'], 'NRS2002评分': 3, 'GLIM诊断': '营养不良', '健康评估分数': 82.95}, 'clinical_recommendations': []}
2025-08-23 17:57:25.044 | INFO     | __main__:upload_bia:392 - 开始转换BIA分析数据为JSON可序列化格式
2025-08-23 17:57:25.044 | INFO     | __main__:upload_bia:394 - BIA数据转换完成
2025-08-23 17:57:25.044 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
2025-08-24 11:53:00.688 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 0
2025-08-24 11:53:00.692 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-24 11:53:00.734 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-24 11:53:00.735 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-24 11:53:00.737 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:53:40.086 | INFO     | __main__:submit_profile:445 - 收到用户档案提交，会话: session_1756007580102_9y7b4ett1
2025-08-24 11:53:40.088 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-24 11:53:40.088 | INFO     | src.agents.conversation_agent:brain_decision_node:278 - 用户已填写档案，提供数据收集选项
2025-08-24 11:53:40.089 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-24 11:53:42.801 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 8
2025-08-24 11:53:42.801 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-24 11:53:42.803 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:53:42.804 | INFO     | src.agents.conversation_agent:brain_decision_node:243 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-24 11:53:42.808 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:53:53.192 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 11
2025-08-24 11:53:53.192 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-24 11:53:53.192 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-24 11:53:53.193 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-24 11:53:53.195 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:53:53.195 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-24 11:53:53.196 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:53:54.954 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 5
2025-08-24 11:53:54.954 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-24 11:53:54.958 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:53:54.958 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:53:54.958 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:53:54.958 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-24 11:53:54.958 | INFO     | src.agents.conversation_agent:_fallback_decision:476 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-24 11:53:54.959 | INFO     | src.agents.conversation_agent:_fallback_decision:486 - 🚀 降级模式：设置BIA上传前端动作
2025-08-24 11:53:54.960 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:54:06.638 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1756007580102_9y7b4ett1，文件: 用户相关数据.xlsx
2025-08-24 11:54:06.642 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:605 - 执行BIA数据分析
2025-08-24 11:54:06.657 | ERROR    | src.tools.bia_calculator:load_bia_data:69 - 加载BIA数据失败: Missing optional dependency 'openpyxl'.  Use pip or conda to install openpyxl.
2025-08-24 11:54:06.658 | ERROR    | src.agents.conversation_agent:call_bia_analysis_node:634 - BIA分析异常: BIA数据文件读取失败
2025-08-24 11:54:06.680 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:54:06.680 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:54:06.680 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:54:06.680 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-24 11:54:06.681 | INFO     | src.agents.conversation_agent:_fallback_decision:463 - 🔄 降级模式：用户完成BIA数据上传，用户输入: 📊 已上传BIA数据：xlsx
2025-08-24 11:54:06.682 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 失败
2025-08-24 11:54:50.929 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 4
2025-08-24 11:54:50.929 | INFO     | __main__:conversation_step:142 - 用户消息内容: '重新上传'
2025-08-24 11:54:50.929 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:54:50.929 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:54:50.929 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:54:50.929 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: '重新上传'
2025-08-24 11:54:50.934 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:54:52.954 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 5
2025-08-24 11:54:52.954 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-24 11:54:52.954 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:54:52.954 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:54:52.954 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:54:52.954 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-24 11:54:52.954 | INFO     | src.agents.conversation_agent:_fallback_decision:476 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-24 11:54:52.957 | INFO     | src.agents.conversation_agent:_fallback_decision:486 - 🚀 降级模式：设置BIA上传前端动作
2025-08-24 11:54:52.958 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:55:04.604 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1756007580102_9y7b4ett1，文件: 用户相关数据.xlsx
2025-08-24 11:55:04.609 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:605 - 执行BIA数据分析
2025-08-24 11:55:04.633 | ERROR    | src.tools.bia_calculator:load_bia_data:69 - 加载BIA数据失败: Missing optional dependency 'openpyxl'.  Use pip or conda to install openpyxl.
2025-08-24 11:55:04.633 | ERROR    | src.agents.conversation_agent:call_bia_analysis_node:634 - BIA分析异常: BIA数据文件读取失败
2025-08-24 11:55:04.634 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:55:04.634 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:55:04.634 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:55:04.634 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-24 11:55:04.634 | INFO     | src.agents.conversation_agent:_fallback_decision:463 - 🔄 降级模式：用户完成BIA数据上传，用户输入: 📊 已上传BIA数据：xlsx
2025-08-24 11:55:04.637 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 失败
2025-08-24 11:55:08.117 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007580102_9y7b4ett1, 消息长度: 6
2025-08-24 11:55:08.118 | INFO     | __main__:conversation_step:142 - 用户消息内容: '选择其他选项'
2025-08-24 11:55:08.133 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:55:08.134 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-24 11:55:08.134 | WARNING  | src.agents.conversation_agent:_fallback_decision:434 - 使用降级决策流程
2025-08-24 11:55:08.134 | INFO     | src.agents.conversation_agent:_fallback_decision:440 - 🔍 降级模式：检查用户输入: '选择其他选项'
2025-08-24 11:55:08.136 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:58:28.668 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007908095_u4iwxl2xl, 消息长度: 0
2025-08-24 11:58:28.669 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-24 11:58:28.710 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-24 11:58:28.710 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-24 11:58:28.713 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:58:34.881 | INFO     | __main__:submit_profile:448 - 收到用户档案提交，会话: session_1756007908095_u4iwxl2xl
2025-08-24 11:58:34.896 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-24 11:58:34.897 | INFO     | src.agents.conversation_agent:brain_decision_node:278 - 用户已填写档案，提供数据收集选项
2025-08-24 11:58:34.898 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-24 11:58:39.007 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1756007908095_u4iwxl2xl, 消息长度: 5
2025-08-24 11:58:39.007 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-24 11:58:39.010 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:58:39.010 | INFO     | src.agents.conversation_agent:brain_decision_node:265 - 🎯 正常模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-24 11:58:39.010 | INFO     | src.agents.conversation_agent:brain_decision_node:273 - 🚀 返回BIA上传响应: {'action': 'continue_conversation', 'next_phase': 'data_collection', 'response': '好的，请您上传BIA体成分分析数据。我将分析您的身体成分指标。', 'frontend_action': {'type': 'show_bia_upload'}, 'waiting_for': 'bia_data'}
2025-08-24 11:58:39.011 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-24 11:58:49.118 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1756007908095_u4iwxl2xl，文件: 用户相关数据.xlsx
2025-08-24 11:58:49.119 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:605 - 执行BIA数据分析
2025-08-24 11:58:49.608 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-24 11:58:49.609 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-24 11:58:49.609 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:629 - BIA数据分析完成
2025-08-24 11:58:49.611 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-24 11:58:49.612 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户完成BIA数据上传，用户输入: 📊 已上传BIA数据：xlsx
2025-08-24 11:58:49.613 | INFO     | __main__:upload_bia:357 - 🔍 BIA分析状态检查: bia_analysis = {'analysis': {'patient_id': 'unknown', 'basic_info': {'姓名': '陈生长', '性别': '男', '年龄': 69, '身高': 170.0, '体重': 72.6, 'BMI': 25.12, '诊断': '升结肠恶性肿瘤', 'NRS2002': 3, 'GLIM': '营养不良'}, 'nutritional_indicators': {'相位角': {'值': 17.48, '状态': '高', '临床意义': '可能存在脱水或炎症状态'}, 'BMI': {'值': 25.12, '状态': '超重', '分类': '超重'}, '基础代谢': {'值': 1638.85, '评估': '需结合年龄性别评估'}}, 'body_composition': {'体脂': {'百分比': 19.09, '正常范围': '10.0-20.0%', '绝对量': 13.86, '状态': '正常'}, '去脂体重': {'值': 58.74, '正常范围': '46.98-57.42kg', '状态': '偏高'}, '内脏脂肪': {'面积': 5.0, '评估': '正常'}, '腰臀比': {'值': 0.78, '正常范围': '0.8-0.9', '状态': '偏低'}}, 'muscle_assessment': {'ASMI': {'值': 14.78, '状态': '正常', '临床意义': '四肢骨骼肌质量指数，用于肌少症诊断'}, '骨骼肌': {'总量': 39.65, '正常范围': '26.12-31.93kg', '状态': '偏高'}, '肌肉分布': {'总肌肉量': 53.27, '右上肢': 4.98, '左上肢': 5.24, '躯干': 39.92, '右下肢': 17.33, '左下肢': 15.16}, '肌肉功能': {'上臂围度': 38.28, '上臂肌肉围度': 37.88}}, 'hydration_status': {'总水分': {'值': 38.76, '正常范围': '34.53-42.2L', '状态': '正常'}, '细胞内水分': {'值': 31.93, '正常范围': '21.41-26.17L'}, '细胞外水分': {'值': 6.83, '正常范围': '13.12-16.04L'}, '浮肿评估': {'浮肿指数': 0.176, '状态': '正常'}}, 'risk_assessment': {'风险等级': '中风险', '风险因素': ['NRS2002评分≥3分，存在营养风险', 'GLIM诊断为营养不良'], 'NRS2002评分': 3, 'GLIM诊断': '营养不良', '健康评估分数': 82.95}, 'clinical_recommendations': []}, 'timestamp': '2025-08-24T11:58:49.609332'}
2025-08-24 11:58:49.614 | INFO     | __main__:upload_bia:358 - 🔍 collected_data = {'glim_results': None, 'facial_analysis': None, 'bia_analysis': {'analysis': {'patient_id': 'unknown', 'basic_info': {'姓名': '陈生长', '性别': '男', '年龄': 69, '身高': 170.0, '体重': 72.6, 'BMI': 25.12, '诊断': '升结肠恶性肿瘤', 'NRS2002': 3, 'GLIM': '营养不良'}, 'nutritional_indicators': {'相位角': {'值': 17.48, '状态': '高', '临床意义': '可能存在脱水或炎症状态'}, 'BMI': {'值': 25.12, '状态': '超重', '分类': '超重'}, '基础代谢': {'值': 1638.85, '评估': '需结合年龄性别评估'}}, 'body_composition': {'体脂': {'百分比': 19.09, '正常范围': '10.0-20.0%', '绝对量': 13.86, '状态': '正常'}, '去脂体重': {'值': 58.74, '正常范围': '46.98-57.42kg', '状态': '偏高'}, '内脏脂肪': {'面积': 5.0, '评估': '正常'}, '腰臀比': {'值': 0.78, '正常范围': '0.8-0.9', '状态': '偏低'}}, 'muscle_assessment': {'ASMI': {'值': 14.78, '状态': '正常', '临床意义': '四肢骨骼肌质量指数，用于肌少症诊断'}, '骨骼肌': {'总量': 39.65, '正常范围': '26.12-31.93kg', '状态': '偏高'}, '肌肉分布': {'总肌肉量': 53.27, '右上肢': 4.98, '左上肢': 5.24, '躯干': 39.92, '右下肢': 17.33, '左下肢': 15.16}, '肌肉功能': {'上臂围度': 38.28, '上臂肌肉围度': 37.88}}, 'hydration_status': {'总水分': {'值': 38.76, '正常范围': '34.53-42.2L', '状态': '正常'}, '细胞内水分': {'值': 31.93, '正常范围': '21.41-26.17L'}, '细胞外水分': {'值': 6.83, '正常范围': '13.12-16.04L'}, '浮肿评估': {'浮肿指数': 0.176, '状态': '正常'}}, 'risk_assessment': {'风险等级': '中风险', '风险因素': ['NRS2002评分≥3分，存在营养风险', 'GLIM诊断为营养不良'], 'NRS2002评分': 3, 'GLIM诊断': '营养不良', '健康评估分数': 82.95}, 'clinical_recommendations': []}, 'timestamp': '2025-08-24T11:58:49.609332'}}
2025-08-24 11:58:49.614 | INFO     | __main__:upload_bia:364 - === BIA数据提取结果 ===
2025-08-24 11:58:49.615 | INFO     | __main__:upload_bia:365 - BIA分析数据: {'patient_id': 'unknown', 'basic_info': {'姓名': '陈生长', '性别': '男', '年龄': 69, '身高': 170.0, '体重': 72.6, 'BMI': 25.12, '诊断': '升结肠恶性肿瘤', 'NRS2002': 3, 'GLIM': '营养不良'}, 'nutritional_indicators': {'相位角': {'值': 17.48, '状态': '高', '临床意义': '可能存在脱水或炎症状态'}, 'BMI': {'值': 25.12, '状态': '超重', '分类': '超重'}, '基础代谢': {'值': 1638.85, '评估': '需结合年龄性别评估'}}, 'body_composition': {'体脂': {'百分比': 19.09, '正常范围': '10.0-20.0%', '绝对量': 13.86, '状态': '正常'}, '去脂体重': {'值': 58.74, '正常范围': '46.98-57.42kg', '状态': '偏高'}, '内脏脂肪': {'面积': 5.0, '评估': '正常'}, '腰臀比': {'值': 0.78, '正常范围': '0.8-0.9', '状态': '偏低'}}, 'muscle_assessment': {'ASMI': {'值': 14.78, '状态': '正常', '临床意义': '四肢骨骼肌质量指数，用于肌少症诊断'}, '骨骼肌': {'总量': 39.65, '正常范围': '26.12-31.93kg', '状态': '偏高'}, '肌肉分布': {'总肌肉量': 53.27, '右上肢': 4.98, '左上肢': 5.24, '躯干': 39.92, '右下肢': 17.33, '左下肢': 15.16}, '肌肉功能': {'上臂围度': 38.28, '上臂肌肉围度': 37.88}}, 'hydration_status': {'总水分': {'值': 38.76, '正常范围': '34.53-42.2L', '状态': '正常'}, '细胞内水分': {'值': 31.93, '正常范围': '21.41-26.17L'}, '细胞外水分': {'值': 6.83, '正常范围': '13.12-16.04L'}, '浮肿评估': {'浮肿指数': 0.176, '状态': '正常'}}, 'risk_assessment': {'风险等级': '中风险', '风险因素': ['NRS2002评分≥3分，存在营养风险', 'GLIM诊断为营养不良'], 'NRS2002评分': 3, 'GLIM诊断': '营养不良', '健康评估分数': 82.95}, 'clinical_recommendations': []}
2025-08-24 11:58:49.615 | INFO     | __main__:upload_bia:395 - 开始转换BIA分析数据为JSON可序列化格式
2025-08-24 11:58:49.615 | INFO     | __main__:upload_bia:397 - BIA数据转换完成
2025-08-24 11:58:49.616 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
