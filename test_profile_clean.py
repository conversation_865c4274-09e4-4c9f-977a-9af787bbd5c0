#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试用户档案清理
"""

import json

def test_profile_cleaning():
    """模拟数据清理过程"""
    print("🧪 测试用户档案清理逻辑")
    print("=" * 50)
    
    # 原始档案数据
    profile_data = {
        "age": 69,
        "gender": "女", 
        "height": "",    # 空字符串
        "weight": "",    # 空字符串
        "disease_history": "高血压",
        "symptoms": "疲劳"
    }
    
    print("📋 原始档案:")
    print(json.dumps(profile_data, ensure_ascii=False, indent=2))
    
    # 清理逻辑
    cleaned_profile_data = profile_data.copy()
    
    # 处理身高
    height = cleaned_profile_data.get('height')
    if height is not None:
        try:
            if height == '' or height == 0 or float(height) <= 0:
                cleaned_profile_data.pop('height', None)
                print("✅ 移除无效身高")
        except (ValueError, TypeError):
            cleaned_profile_data.pop('height', None)
            print("✅ 移除无效身高（转换异常）")
    else:
        cleaned_profile_data.pop('height', None)
        print("✅ 移除空身高")
    
    # 处理体重
    weight = cleaned_profile_data.get('weight') or cleaned_profile_data.get('current_weight')
    if weight is not None and 'weight' in cleaned_profile_data:
        try:
            if weight == '' or weight == 0 or float(weight) <= 0:
                cleaned_profile_data.pop('weight', None)
                cleaned_profile_data.pop('current_weight', None)
                print("✅ 移除无效体重")
        except (ValueError, TypeError):
            cleaned_profile_data.pop('weight', None)
            cleaned_profile_data.pop('current_weight', None)
            print("✅ 移除无效体重（转换异常）")
    else:
        cleaned_profile_data.pop('weight', None)
        cleaned_profile_data.pop('current_weight', None)
        print("✅ 移除空体重")
    
    print("\n📋 清理后档案:")
    print(json.dumps(cleaned_profile_data, ensure_ascii=False, indent=2))
    
    print("\n🔍 键名检查:")
    print(f"包含height键: {'height' in cleaned_profile_data}")
    print(f"包含weight键: {'weight' in cleaned_profile_data}")
    
    # 模拟患者信息构建
    print("\n👤 患者信息构建测试:")
    patient_info_lines = []
    patient_info_lines.append(f"- 年龄：{cleaned_profile_data.get('age', '未提供')}岁")
    patient_info_lines.append(f"- 性别：{cleaned_profile_data.get('gender', '未提供')}")
    
    # 只有在身高有意义的值时才显示
    height = cleaned_profile_data.get('height')
    print(f"height值: {height} (类型: {type(height)})")
    if height is not None:
        try:
            height_val = float(height) if height != '' else 0
            if height_val > 0:
                patient_info_lines.append(f"- 身高：{height}cm")
                print("✅ 添加身高信息")
            else:
                print("⚠️ 身高值<=0，不添加")
        except (ValueError, TypeError):
            print("⚠️ 身高转换失败，不添加")
    else:
        print("✅ 身高键不存在，不添加")
    
    # 只有在体重有意义的值时才显示
    weight = cleaned_profile_data.get('weight') or cleaned_profile_data.get('current_weight')
    print(f"weight值: {weight} (类型: {type(weight)})")
    if weight is not None:
        try:
            weight_val = float(weight) if weight != '' else 0
            if weight_val > 0:
                patient_info_lines.append(f"- 体重：{weight}kg")
                print("✅ 添加体重信息")
            else:
                print("⚠️ 体重值<=0，不添加")
        except (ValueError, TypeError):
            print("⚠️ 体重转换失败，不添加")
    else:
        print("✅ 体重键不存在，不添加")
    
    patient_info_text = '\n'.join(patient_info_lines)
    
    print("\n📄 最终患者信息:")
    print(patient_info_text)

if __name__ == "__main__":
    test_profile_cleaning()
