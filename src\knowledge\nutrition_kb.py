#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
营养知识库管理系统
"""
import json
import os
from typing import Dict, List, Any, Optional
from loguru import logger

from config.settings import settings


class NutritionKnowledgeBase:
    """营养知识库管理器"""
    
    def __init__(self):
        self.facial_features_data = None
        self.knowledge_loaded = False
        self._load_knowledge()
    
    def _load_knowledge(self):
        """加载知识库数据"""
        try:
            kb_path = os.path.join(
                os.path.dirname(__file__),
                "facial_features.json"
            )
            
            with open(kb_path, 'r', encoding='utf-8') as f:
                self.facial_features_data = json.load(f)
            
            self.knowledge_loaded = True
            logger.info("营养知识库加载成功")
            
        except Exception as e:
            logger.error(f"知识库加载失败: {e}")
            self.knowledge_loaded = False
    
    def get_vision_prompt_template(self) -> str:
        """获取视觉分析提示词模板"""
        if not self.knowledge_loaded:
            return "请分析面部图像中的营养相关特征。"
        
        return self.facial_features_data.get("vision_prompt_template", "")
    
    def get_facial_regions(self) -> Dict[str, Any]:
        """获取面部区域信息"""
        if not self.knowledge_loaded:
            return {}
        
        return self.facial_features_data.get("facial_regions", {})
    
    def get_assessment_guidelines(self) -> Dict[str, Any]:
        """获取评估指南"""
        if not self.knowledge_loaded:
            return {}
        
        return self.facial_features_data.get("assessment_guidelines", {})
    
    def get_severity_mapping(self) -> Dict[str, Any]:
        """获取严重程度映射"""
        guidelines = self.get_assessment_guidelines()
        return guidelines.get("severity_mapping", {})
    
    def calculate_malnutrition_score(self, regional_findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根据区域发现计算营养不良评分"""
        severity_scores = {
            "normal": 0,
            "mild": 1,
            "moderate": 2,
            "severe": 3
        }
        
        total_score = 0
        valid_findings = 0
        region_scores = {}
        
        for finding in regional_findings:
            severity = finding.get("severity", "normal")
            region = finding.get("facial_region", "unknown")
            confidence = finding.get("confidence", 0.0)
            
            if severity in severity_scores and confidence > 0.3:  # 置信度阈值
                score = severity_scores[severity] * confidence
                total_score += score
                valid_findings += 1
                region_scores[region] = {
                    "severity": severity,
                    "score": score,
                    "confidence": confidence
                }
        
        # 计算平均分
        if valid_findings > 0:
            average_score = total_score / valid_findings
        else:
            average_score = 0
        
        # 根据评分确定营养状况
        severity_mapping = self.get_severity_mapping()
        nutrition_status = "normal"
        
        for status, info in severity_mapping.items():
            score_range = info.get("score_range", [0, 0])
            if score_range[0] <= average_score <= score_range[1]:
                nutrition_status = status.replace("_malnutrition", "").replace("_", " ")
                break
        
        return {
            "total_score": round(total_score, 2),
            "average_score": round(average_score, 2),
            "nutrition_status": nutrition_status,
            "region_scores": region_scores,
            "valid_findings": valid_findings
        }
    
    def get_feature_details(self, region: str, feature: str) -> Optional[Dict[str, Any]]:
        """获取特定特征的详细信息"""
        regions = self.get_facial_regions()
        region_data = regions.get(region, {})
        features = region_data.get("features", {})
        return features.get(feature)
    
    def get_all_features_summary(self) -> List[Dict[str, Any]]:
        """获取所有特征的摘要"""
        regions = self.get_facial_regions()
        features_summary = []
        
        for region_key, region_data in regions.items():
            region_name = region_data.get("name", region_key)
            features = region_data.get("features", {})
            
            for feature_key, feature_data in features.items():
                features_summary.append({
                    "region": region_name,
                    "region_key": region_key,
                    "feature": feature_data.get("name", feature_key),
                    "feature_key": feature_key,
                    "description": feature_data.get("description", ""),
                    "references": feature_data.get("references", [])
                })
        
        return features_summary
    
    def validate_vision_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """验证视觉分析响应格式"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查必需字段
        required_fields = ["visual_analysis", "overall_assessment"]
        for field in required_fields:
            if field not in response:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"缺少必需字段: {field}")
        
        # 检查visual_analysis格式
        if "visual_analysis" in response:
            visual_analysis = response["visual_analysis"]
            if not isinstance(visual_analysis, list):
                validation_result["is_valid"] = False
                validation_result["errors"].append("visual_analysis必须是列表格式")
            else:
                expected_regions = ["Upper Face", "Midface", "Lower Face"]
                found_regions = [item.get("facial_region") for item in visual_analysis]
                
                for region in expected_regions:
                    if region not in found_regions:
                        validation_result["warnings"].append(f"缺少区域分析: {region}")
        
        # 检查overall_assessment格式
        if "overall_assessment" in response:
            overall = response["overall_assessment"]
            if not isinstance(overall, dict):
                validation_result["is_valid"] = False
                validation_result["errors"].append("overall_assessment必须是字典格式")
            else:
                required_overall_fields = ["malnutrition_likelihood", "confidence"]
                for field in required_overall_fields:
                    if field not in overall:
                        validation_result["warnings"].append(f"overall_assessment缺少字段: {field}")
        
        return validation_result
    
    def get_contextual_prompt(self, patient_info: Optional[Dict[str, Any]] = None, instance_id: int = 1) -> str:
        """根据患者信息获取上下文化的视觉分析提示词（使用新的专业提示词）"""
        try:
            # 使用新的专业提示词系统
            from config.vision_analysis_prompts import get_contextual_vision_prompt
            return get_contextual_vision_prompt(patient_info, instance_id)
        except ImportError:
            logger.warning("无法导入新的提示词配置，使用传统提示词")
            # 降级到传统提示词
            base_prompt = self.get_vision_prompt_template()
            
            if patient_info:
                context_additions = []
                
                # 根据年龄调整
                age = patient_info.get("age")
                if age and age > 65:
                    context_additions.append(
                        "\n特别注意：患者年龄超过65岁，请特别关注与年龄相关的肌少症体征，"
                        "如颞肌和咬肌的萎缩可能更为明显。"
                    )
                
                # 根据性别调整
                gender = patient_info.get("gender")
                if gender:
                    if gender.lower() in ["male", "男"]:
                        context_additions.append(
                            "\n注意：男性患者的面部肌肉萎缩可能更容易观察到。"
                        )
                    elif gender.lower() in ["female", "女"]:
                        context_additions.append(
                            "\n注意：女性患者的面部脂肪垫变化可能更为明显。"
                        )
                
                # 根据疾病史调整
                diseases = patient_info.get("diseases", [])
                if diseases:
                    disease_context = "\n已知疾病史：" + "、".join(diseases)
                    disease_context += "\n请结合这些疾病可能对营养状况的影响进行分析。"
                    context_additions.append(disease_context)
                
                if context_additions:
                    base_prompt += "\n\n" + "".join(context_additions)
            
            return base_prompt


# 全局知识库实例
nutrition_kb = NutritionKnowledgeBase()

