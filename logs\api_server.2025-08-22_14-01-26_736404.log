2025-08-22 14:07:12.151 | INFO     | __main__:conversation_step:120 - 收到对话请求，会话: session_1755842831567_pugd61knz, 消息长度: 0
2025-08-22 14:07:12.154 | ERROR    | __main__:conversation_step:175 - 对话处理异常: 'coroutine' object is not subscriptable
2025-08-22 14:07:12.157 | ERROR    | __main__:conversation_step:176 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 140, in conversation_step
    for msg in reversed(state['messages']):
                        ~~~~~^^^^^^^^^^^^
TypeError: 'coroutine' object is not subscriptable

2025-08-22 14:53:50.836 | INFO     | __main__:conversation_step:120 - 收到对话请求，会话: session_1755845630256_ukdytl8c6, 消息长度: 0
2025-08-22 14:53:50.839 | ERROR    | __main__:conversation_step:175 - 对话处理异常: 'coroutine' object is not subscriptable
2025-08-22 14:53:50.840 | ERROR    | __main__:conversation_step:176 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 140, in conversation_step
    for msg in reversed(state['messages']):
                        ~~~~~^^^^^^^^^^^^
TypeError: 'coroutine' object is not subscriptable

2025-08-22 14:54:07.475 | INFO     | __main__:submit_profile:376 - 收到用户档案提交，会话: session_1755845630256_ukdytl8c6
2025-08-22 14:54:07.475 | ERROR    | __main__:submit_profile:423 - 档案提交异常: 'coroutine' object is not subscriptable
2025-08-22 14:54:07.475 | ERROR    | __main__:submit_profile:424 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 403, in submit_profile
    for msg in reversed(state['messages']):
                        ~~~~~^^^^^^^^^^^^
TypeError: 'coroutine' object is not subscriptable

2025-08-22 14:55:49.913 | INFO     | __main__:conversation_step:120 - 收到对话请求，会话: session_1755845630256_ukdytl8c6, 消息长度: 3
2025-08-22 14:55:49.914 | ERROR    | __main__:conversation_step:175 - 对话处理异常: 'coroutine' object is not subscriptable
2025-08-22 14:55:49.917 | ERROR    | __main__:conversation_step:176 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 140, in conversation_step
    for msg in reversed(state['messages']):
                        ~~~~~^^^^^^^^^^^^
TypeError: 'coroutine' object is not subscriptable

2025-08-22 15:00:39.501 | ERROR    | __main__:wrapper:59 - 异步路由执行错误: name 'asyncio' is not defined
2025-08-22 15:00:51.742 | ERROR    | __main__:wrapper:59 - 异步路由执行错误: name 'asyncio' is not defined
2025-08-22 15:01:31.571 | ERROR    | __main__:wrapper:59 - 异步路由执行错误: name 'asyncio' is not defined
2025-08-22 15:02:15.128 | ERROR    | __main__:conversation_step:193 - 对话处理异常: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-08-22 15:02:15.155 | ERROR    | __main__:conversation_step:194 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 129, in conversation_step
    data = request.get_json()
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.

2025-08-22 15:02:15.159 | ERROR    | __main__:wrapper:54 - 异步路由执行错误: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-08-22 15:02:19.869 | ERROR    | __main__:conversation_step:193 - 对话处理异常: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-08-22 15:02:19.870 | ERROR    | __main__:conversation_step:194 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 129, in conversation_step
    data = request.get_json()
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.

2025-08-22 15:02:19.873 | ERROR    | __main__:wrapper:54 - 异步路由执行错误: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-08-22 15:02:29.043 | ERROR    | __main__:submit_profile:444 - 档案提交异常: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-08-22 15:02:29.044 | ERROR    | __main__:submit_profile:445 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 389, in submit_profile
    data = request.get_json()
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\werkzeug\local.py", line 519, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.

2025-08-22 15:02:29.046 | ERROR    | __main__:wrapper:54 - 异步路由执行错误: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-08-22 15:09:53.391 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755846592725_prbngt0rs, 消息长度: 0
2025-08-22 15:09:53.464 | INFO     | src.agents.conversation_agent:brain_decision_node:182 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:09:53.477 | ERROR    | src.agents.conversation_agent:process_conversation_turn:624 - 对话处理失败: '\n  "action"'
2025-08-22 15:09:53.479 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:10:02.088 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: test_session, 消息长度: 0
2025-08-22 15:10:02.094 | INFO     | src.agents.conversation_agent:brain_decision_node:182 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:10:02.096 | ERROR    | src.agents.conversation_agent:process_conversation_turn:624 - 对话处理失败: '\n  "action"'
2025-08-22 15:10:02.097 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:10:06.747 | INFO     | __main__:submit_profile:392 - 收到用户档案提交，会话: session_1755846592725_prbngt0rs
2025-08-22 15:10:06.758 | INFO     | src.agents.conversation_agent:brain_decision_node:182 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:10:06.759 | ERROR    | src.agents.conversation_agent:process_conversation_turn:624 - 对话处理失败: '\n  "action"'
2025-08-22 15:10:06.761 | INFO     | __main__:log_api_call:80 - API调用记录: submit-profile - 成功
2025-08-22 15:12:35.938 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755846755340_ke404y80f, 消息长度: 0
2025-08-22 15:12:35.974 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:12:36.039 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:12:36.057 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 340
2025-08-22 15:12:39.788 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: test_session_new, 消息长度: 0
2025-08-22 15:12:39.791 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:12:39.791 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:12:39.798 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 340
2025-08-22 15:12:43.997 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 636
2025-08-22 15:12:43.998 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 331, 'total_tokens': 788}
2025-08-22 15:12:43.999 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:12:44.002 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:12:44.003 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:12:44.020 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:12:52.019 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 710
2025-08-22 15:12:52.019 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 362, 'total_tokens': 819}
2025-08-22 15:12:52.021 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:12:52.022 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:12:52.023 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:12:52.047 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:03.444 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 960
2025-08-22 15:13:03.445 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 507, 'total_tokens': 964}
2025-08-22 15:13:03.445 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:03.625 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:03.626 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:03.631 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:13.046 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 830
2025-08-22 15:13:13.046 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 426, 'total_tokens': 883}
2025-08-22 15:13:13.046 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:13.050 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:13.051 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:13.076 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:22.525 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 762
2025-08-22 15:13:22.526 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 414, 'total_tokens': 871}
2025-08-22 15:13:22.527 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:22.530 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:22.530 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:22.551 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:33.251 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 881
2025-08-22 15:13:33.251 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 471, 'total_tokens': 928}
2025-08-22 15:13:33.251 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:33.254 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:33.254 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:33.258 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:42.105 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 687
2025-08-22 15:13:42.106 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 362, 'total_tokens': 819}
2025-08-22 15:13:42.107 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:42.111 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:42.112 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:42.118 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:13:50.488 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 674
2025-08-22 15:13:50.488 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 363, 'total_tokens': 820}
2025-08-22 15:13:50.488 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:13:50.490 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:13:50.490 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:13:50.496 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:01.533 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 913
2025-08-22 15:14:01.534 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 490, 'total_tokens': 947}
2025-08-22 15:14:01.534 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:01.536 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:01.536 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:01.540 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:10.453 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 736
2025-08-22 15:14:10.454 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 399, 'total_tokens': 856}
2025-08-22 15:14:10.454 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:10.456 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:10.457 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:10.479 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:17.124 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755846856514_cgnup1ayj, 消息长度: 0
2025-08-22 15:14:17.130 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:14:17.130 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:17.141 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 340
2025-08-22 15:14:19.910 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 780
2025-08-22 15:14:19.911 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 410, 'total_tokens': 867}
2025-08-22 15:14:19.911 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:19.912 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:19.913 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:19.919 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:28.792 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 750
2025-08-22 15:14:28.792 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 397, 'total_tokens': 854}
2025-08-22 15:14:28.793 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:28.795 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:28.795 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:28.800 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:38.085 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 775
2025-08-22 15:14:38.085 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 415, 'total_tokens': 872}
2025-08-22 15:14:38.085 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:38.089 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:38.089 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:38.096 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:45.965 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 662
2025-08-22 15:14:45.966 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 345, 'total_tokens': 802}
2025-08-22 15:14:45.967 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:45.970 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:45.970 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:45.996 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:14:53.110 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 616
2025-08-22 15:14:53.111 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 318, 'total_tokens': 775}
2025-08-22 15:14:53.112 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:14:53.116 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:14:53.117 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:14:53.146 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:15:02.776 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 825
2025-08-22 15:15:02.777 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 435, 'total_tokens': 892}
2025-08-22 15:15:02.777 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:15:02.781 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:15:02.782 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:15:02.818 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:15:10.284 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 640
2025-08-22 15:15:10.285 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 332, 'total_tokens': 789}
2025-08-22 15:15:10.285 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:15:10.288 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:15:10.289 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:15:10.312 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:15:18.732 | INFO     | src.core.lm_studio_client:call_huatuogpt:208 - 华佗GPT响应成功，输出长度: 680
2025-08-22 15:15:18.733 | INFO     | src.core.lm_studio_client:call_huatuogpt:210 - Token使用情况: {'prompt_tokens': 457, 'completion_tokens': 378, 'total_tokens': 835}
2025-08-22 15:15:18.733 | INFO     | src.agents.conversation_agent:brain_decision_node:167 - AI主脑决策：continue_conversation -> data_collection
2025-08-22 15:15:18.736 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:15:18.736 | INFO     | src.core.lm_studio_client:call_huatuogpt:142 - 调用华佗GPT主脑模型进行综合分析
2025-08-22 15:15:18.743 | INFO     | src.core.lm_studio_client:call_huatuogpt:194 - 发送华佗GPT请求，使用模型: freedomintelligence.huatuogpt-o1-7b，提示词长度: 347
2025-08-22 15:22:52.863 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 0
2025-08-22 15:22:52.896 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:22:52.897 | INFO     | src.agents.conversation_agent:brain_decision_node:149 - 首次启动，生成问候消息
2025-08-22 15:22:52.910 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:22:59.941 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: test_fix, 消息长度: 0
2025-08-22 15:22:59.943 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:22:59.944 | INFO     | src.agents.conversation_agent:brain_decision_node:149 - 首次启动，生成问候消息
2025-08-22 15:22:59.947 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:23:35.876 | INFO     | __main__:submit_profile:392 - 收到用户档案提交，会话: session_1755847372266_sw6uoz8dx
2025-08-22 15:23:35.881 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:23:35.881 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 15:23:35.886 | INFO     | __main__:log_api_call:80 - API调用记录: submit-profile - 成功
2025-08-22 15:23:49.244 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:23:49.250 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:23:49.250 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 15:23:49.253 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:23:52.027 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:23:52.034 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:23:52.034 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 15:23:52.039 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:23:55.135 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:23:55.140 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:23:55.141 | WARNING  | src.agents.conversation_agent:brain_decision_node:144 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 15:23:55.141 | WARNING  | src.agents.conversation_agent:_fallback_decision:326 - 使用降级决策流程
2025-08-22 15:23:55.143 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:23:57.492 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:23:57.498 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:23:57.498 | WARNING  | src.agents.conversation_agent:brain_decision_node:144 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 15:23:57.499 | WARNING  | src.agents.conversation_agent:_fallback_decision:326 - 使用降级决策流程
2025-08-22 15:23:57.501 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:23:59.560 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:23:59.567 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:23:59.568 | WARNING  | src.agents.conversation_agent:brain_decision_node:144 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 15:23:59.568 | WARNING  | src.agents.conversation_agent:_fallback_decision:326 - 使用降级决策流程
2025-08-22 15:23:59.572 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:24:00.828 | INFO     | __main__:conversation_step:136 - 收到对话请求，会话: session_1755847372266_sw6uoz8dx, 消息长度: 8
2025-08-22 15:24:00.835 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 15:24:00.836 | WARNING  | src.agents.conversation_agent:brain_decision_node:144 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 15:24:00.836 | WARNING  | src.agents.conversation_agent:_fallback_decision:326 - 使用降级决策流程
2025-08-22 15:24:00.840 | INFO     | __main__:log_api_call:80 - API调用记录: conversation - 成功
2025-08-22 15:24:45.054 | INFO     | __main__:submit_profile:392 - 收到用户档案提交，会话: test_fix
2025-08-22 15:24:45.057 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 15:24:45.057 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 15:24:45.058 | INFO     | __main__:log_api_call:80 - API调用记录: submit-profile - 成功
2025-08-22 19:01:13.211 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860472623_y5kr9k2kr, 消息长度: 0
2025-08-22 19:01:13.252 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:01:13.253 | INFO     | src.agents.conversation_agent:brain_decision_node:149 - 首次启动，生成问候消息
2025-08-22 19:01:13.255 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:01:20.321 | INFO     | __main__:submit_profile:396 - 收到用户档案提交，会话: session_1755860472623_y5kr9k2kr
2025-08-22 19:01:20.325 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:01:20.325 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 19:01:20.328 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:01:37.884 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860472623_y5kr9k2kr, 消息长度: 8
2025-08-22 19:01:37.888 | INFO     | src.agents.conversation_agent:brain_decision_node:139 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:01:37.888 | INFO     | src.agents.conversation_agent:brain_decision_node:171 - 用户已填写档案，提供数据收集选项
2025-08-22 19:01:37.889 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:07:22.807 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860842234_vx5wfhrnv, 消息长度: 0
2025-08-22 19:07:22.815 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:07:22.816 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 19:07:22.818 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:07:30.746 | INFO     | __main__:submit_profile:396 - 收到用户档案提交，会话: session_1755860842234_vx5wfhrnv
2025-08-22 19:07:30.759 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:07:30.759 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:07:30.762 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:07:33.909 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860842234_vx5wfhrnv, 消息长度: 8
2025-08-22 19:07:33.913 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:07:33.913 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:07:33.914 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:07:48.451 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860868118_ngtt3opiq, 消息长度: 0
2025-08-22 19:07:48.453 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:07:48.453 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 19:07:48.455 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:08:27.223 | INFO     | __main__:submit_profile:396 - 收到用户档案提交，会话: session_1755860868118_ngtt3opiq
2025-08-22 19:08:27.225 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:08:27.225 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:08:27.226 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:08:29.460 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755860868118_ngtt3opiq, 消息长度: 8
2025-08-22 19:08:29.462 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:08:29.462 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:08:29.463 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:11:35.414 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861094839_8ozcnmspi, 消息长度: 0
2025-08-22 19:11:35.414 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 19:11:35.428 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:11:35.428 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 19:11:35.431 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:11:40.103 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755861094839_8ozcnmspi
2025-08-22 19:11:40.104 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:11:40.104 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:11:40.104 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:11:42.106 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861094839_8ozcnmspi, 消息长度: 8
2025-08-22 19:11:42.106 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:11:42.106 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:11:42.106 | INFO     | src.agents.conversation_agent:brain_decision_node:179 - 用户已填写档案，提供数据收集选项
2025-08-22 19:11:42.106 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:13:16.208 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861195629_4xdtrnlkz, 消息长度: 0
2025-08-22 19:13:16.209 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 19:13:16.221 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:13:16.222 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 19:13:16.224 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:13:20.384 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755861195629_4xdtrnlkz
2025-08-22 19:13:20.386 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:13:20.388 | INFO     | src.agents.conversation_agent:brain_decision_node:217 - 用户已填写档案，提供数据收集选项
2025-08-22 19:13:20.390 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:13:21.869 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861195629_4xdtrnlkz, 消息长度: 8
2025-08-22 19:13:21.869 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:13:21.871 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:13:21.871 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 19:13:21.871 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:14:03.382 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861195629_4xdtrnlkz, 消息长度: 8
2025-08-22 19:14:03.382 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:14:03.406 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:14:03.406 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 19:14:03.408 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:17:59.364 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861195629_4xdtrnlkz, 消息长度: 4
2025-08-22 19:17:59.364 | INFO     | __main__:conversation_step:141 - 用户消息内容: '完成收集'
2025-08-22 19:17:59.367 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:17:59.367 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 19:17:59.368 | WARNING  | src.agents.conversation_agent:_fallback_decision:373 - 使用降级决策流程
2025-08-22 19:17:59.369 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:45:19.349 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755861195629_4xdtrnlkz, 消息长度: 8
2025-08-22 19:45:19.349 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:45:19.351 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:45:19.351 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 19:45:19.352 | WARNING  | src.agents.conversation_agent:_fallback_decision:373 - 使用降级决策流程
2025-08-22 19:45:19.353 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:59:10.029 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755863949439_69figy2jm, 消息长度: 0
2025-08-22 19:59:10.030 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 19:59:10.038 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:59:10.039 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 19:59:10.041 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:59:17.911 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755863949439_69figy2jm
2025-08-22 19:59:17.914 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 19:59:17.914 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 19:59:17.915 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 19:59:20.196 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755863949439_69figy2jm, 消息长度: 8
2025-08-22 19:59:20.196 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:59:20.200 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:59:20.201 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 19:59:20.203 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 19:59:38.540 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755863949439_69figy2jm, 消息长度: 8
2025-08-22 19:59:38.540 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 19:59:38.543 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 19:59:38.543 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 19:59:38.545 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:01:56.441 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755864115854_7g57tixyy, 消息长度: 0
2025-08-22 20:01:56.442 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:01:56.452 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:01:56.452 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:01:56.455 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:02:22.185 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755864141860_mk8axoi4w, 消息长度: 0
2025-08-22 20:02:22.185 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:02:22.188 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:02:22.188 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:02:22.190 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:10:21.417 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755864141860_mk8axoi4w
2025-08-22 20:10:21.419 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:10:21.419 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:10:21.420 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:10:23.169 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755864141860_mk8axoi4w, 消息长度: 8
2025-08-22 20:10:23.169 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:10:23.170 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:10:23.171 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:10:23.172 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:11:00.780 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755864660454_k9l0zru18, 消息长度: 0
2025-08-22 20:11:00.780 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:11:00.782 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:11:00.782 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:11:00.783 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:11:04.994 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755864660454_k9l0zru18
2025-08-22 20:11:04.997 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:11:04.998 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:11:04.999 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:11:17.841 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755864660454_k9l0zru18, 消息长度: 8
2025-08-22 20:11:17.842 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:11:17.844 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:11:17.844 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:11:17.846 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:27:01.099 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755865620517_tmttpogbs, 消息长度: 0
2025-08-22 20:27:01.099 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:27:01.111 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:27:01.111 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:27:01.115 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:27:05.342 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755865620517_tmttpogbs
2025-08-22 20:27:05.344 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:27:05.344 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:27:05.345 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:27:07.184 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755865620517_tmttpogbs, 消息长度: 8
2025-08-22 20:27:07.184 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:27:07.186 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:27:07.186 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:27:07.187 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:30:52.703 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755865852121_zwpeto4yj, 消息长度: 0
2025-08-22 20:30:52.703 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:30:52.711 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:30:52.711 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:30:52.713 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:32:25.055 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755865852121_zwpeto4yj
2025-08-22 20:32:25.057 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:32:25.057 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:32:25.058 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:32:26.986 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755865852121_zwpeto4yj, 消息长度: 8
2025-08-22 20:32:26.987 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:32:26.988 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:32:26.988 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:32:26.989 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:34:46.648 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755866086060_i0pscs0o1, 消息长度: 0
2025-08-22 20:34:46.648 | INFO     | __main__:conversation_step:141 - 用户消息内容: ''
2025-08-22 20:34:46.649 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:34:46.649 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:34:46.651 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:34:58.270 | INFO     | __main__:submit_profile:397 - 收到用户档案提交，会话: session_1755866086060_i0pscs0o1
2025-08-22 20:34:58.273 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:34:58.273 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:34:58.274 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:34:59.872 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755866086060_i0pscs0o1, 消息长度: 8
2025-08-22 20:34:59.872 | INFO     | __main__:conversation_step:141 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:34:59.896 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:34:59.897 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:34:59.898 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:35:10.794 | INFO     | __main__:conversation_step:140 - 收到对话请求，会话: session_1755866086060_i0pscs0o1, 消息长度: 11
2025-08-22 20:35:10.796 | INFO     | __main__:conversation_step:141 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:35:10.797 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:35:10.798 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: 已完成GLIM评估问卷
2025-08-22 20:35:10.799 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:38:38.005 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866317426_9drfv3369, 消息长度: 0
2025-08-22 20:38:38.007 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 20:38:38.012 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:38:38.012 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:38:38.017 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:38:51.085 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755866317426_9drfv3369
2025-08-22 20:38:51.098 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:38:51.099 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:38:51.100 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:38:52.884 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866317426_9drfv3369, 消息长度: 8
2025-08-22 20:38:52.885 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:38:52.899 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:38:52.899 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:38:52.900 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:39:02.839 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866317426_9drfv3369, 消息长度: 11
2025-08-22 20:39:02.840 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:39:02.840 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:39:02.842 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:39:02.855 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:39:02.855 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户选择GLIM评估问卷，用户输入: 已完成GLIM评估问卷
2025-08-22 20:39:02.856 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:39:08.187 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866317426_9drfv3369, 消息长度: 11
2025-08-22 20:39:08.187 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:39:08.188 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:39:08.188 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:39:08.190 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:39:08.191 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:39:08.191 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:39:08.192 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:40:30.255 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866429687_8ki0fnbvp, 消息长度: 0
2025-08-22 20:40:30.256 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 20:40:30.264 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:40:30.264 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:40:30.267 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:41:13.040 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755866429687_8ki0fnbvp
2025-08-22 20:41:13.042 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:41:13.042 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:41:13.044 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:41:15.039 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866429687_8ki0fnbvp, 消息长度: 8
2025-08-22 20:41:15.039 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:41:15.041 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:41:15.042 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:41:15.042 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:41:24.036 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866429687_8ki0fnbvp, 消息长度: 11
2025-08-22 20:41:24.036 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:41:24.036 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:41:24.038 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:41:24.039 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:41:24.039 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 20:41:24.042 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:41:30.939 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866429687_8ki0fnbvp, 消息长度: 5
2025-08-22 20:41:30.940 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:41:30.942 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:41:30.942 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:41:30.942 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:41:30.943 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:43:25.442 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866429687_8ki0fnbvp, 消息长度: 5
2025-08-22 20:43:25.442 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:43:25.460 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:43:25.460 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:43:25.460 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:43:25.461 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:46:29.701 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866789134_e7ju34vw8, 消息长度: 0
2025-08-22 20:46:29.702 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 20:46:29.709 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:46:29.709 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:46:29.711 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:46:33.800 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755866789134_e7ju34vw8
2025-08-22 20:46:33.802 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:46:33.803 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:46:33.805 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:46:35.746 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866789134_e7ju34vw8, 消息长度: 5
2025-08-22 20:46:35.747 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:46:35.749 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:46:35.749 | INFO     | src.agents.conversation_agent:brain_decision_node:235 - 用户选择BIA数据分析，用户输入: BIA数据
2025-08-22 20:46:35.750 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:46:40.922 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866789134_e7ju34vw8, 消息长度: 8
2025-08-22 20:46:40.923 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:46:40.924 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:46:40.924 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:46:40.926 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:46:55.446 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866789134_e7ju34vw8, 消息长度: 11
2025-08-22 20:46:55.447 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:46:55.447 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:46:55.448 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:46:55.450 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:46:55.450 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:46:55.450 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:46:55.452 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:49:55.447 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866994870_rtw6cxbs6, 消息长度: 0
2025-08-22 20:49:55.448 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 20:49:55.457 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:49:55.457 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:49:55.460 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:49:59.791 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755866994870_rtw6cxbs6
2025-08-22 20:49:59.796 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:49:59.797 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:49:59.799 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:50:02.333 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866994870_rtw6cxbs6, 消息长度: 8
2025-08-22 20:50:02.334 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:50:02.337 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:50:02.337 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:50:02.339 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:50:14.774 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866994870_rtw6cxbs6, 消息长度: 11
2025-08-22 20:50:14.774 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:50:14.774 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:50:14.776 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:50:14.777 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:50:14.778 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 20:50:14.780 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:50:18.961 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866994870_rtw6cxbs6, 消息长度: 5
2025-08-22 20:50:18.961 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:50:18.965 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:50:18.965 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:50:18.966 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:50:18.967 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:55:19.024 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755866994870_rtw6cxbs6, 消息长度: 5
2025-08-22 20:55:19.025 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:55:19.026 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:55:19.027 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:55:19.027 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:55:19.028 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:57:31.413 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867450850_qaxhyyjlt, 消息长度: 0
2025-08-22 20:57:31.413 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 20:57:31.419 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:57:31.419 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 20:57:31.426 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:58:23.831 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755867450850_qaxhyyjlt
2025-08-22 20:58:23.833 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 20:58:23.833 | INFO     | src.agents.conversation_agent:brain_decision_node:247 - 用户已填写档案，提供数据收集选项
2025-08-22 20:58:23.834 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 20:58:25.909 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867450850_qaxhyyjlt, 消息长度: 8
2025-08-22 20:58:25.910 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 20:58:25.911 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:58:25.911 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 20:58:25.911 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:58:36.413 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867450850_qaxhyyjlt, 消息长度: 11
2025-08-22 20:58:36.414 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 20:58:36.414 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 20:58:36.414 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 20:58:36.416 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:58:36.416 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 20:58:36.418 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 20:58:44.594 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867450850_qaxhyyjlt, 消息长度: 5
2025-08-22 20:58:44.595 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 20:58:44.624 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 20:58:44.625 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 20:58:44.625 | WARNING  | src.agents.conversation_agent:_fallback_decision:403 - 使用降级决策流程
2025-08-22 20:58:44.626 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:02:23.659 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867743096_zpw0yezoq, 消息长度: 0
2025-08-22 21:02:23.660 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 21:02:23.670 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:02:23.670 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 21:02:23.673 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:08:09.608 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755867743096_zpw0yezoq
2025-08-22 21:08:09.610 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:08:09.611 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-22 21:08:09.611 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 21:08:11.880 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867743096_zpw0yezoq, 消息长度: 8
2025-08-22 21:08:11.880 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 21:08:11.883 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:08:11.883 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 21:08:11.884 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:08:28.205 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867743096_zpw0yezoq, 消息长度: 11
2025-08-22 21:08:28.206 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 21:08:28.206 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 21:08:28.208 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 21:08:28.209 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:08:28.209 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 21:08:28.210 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:08:30.937 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755867743096_zpw0yezoq, 消息长度: 5
2025-08-22 21:08:30.937 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 21:08:30.940 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:08:30.940 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:08:30.940 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:08:30.941 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:11:36.375 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868295800_i7nvay0bb, 消息长度: 0
2025-08-22 21:11:36.376 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 21:11:36.385 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:11:36.385 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 21:11:36.389 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:11:43.171 | INFO     | __main__:submit_profile:410 - 收到用户档案提交，会话: session_1755868295800_i7nvay0bb
2025-08-22 21:11:43.184 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:11:43.185 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-22 21:11:43.185 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 21:11:44.983 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868295800_i7nvay0bb, 消息长度: 8
2025-08-22 21:11:44.983 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 21:11:45.008 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:11:45.008 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 21:11:45.009 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:11:54.283 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868295800_i7nvay0bb, 消息长度: 11
2025-08-22 21:11:54.283 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 21:11:54.283 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 21:11:54.285 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 21:11:54.287 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:11:54.287 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 21:11:54.288 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:11:56.528 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868295800_i7nvay0bb, 消息长度: 5
2025-08-22 21:11:56.529 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 21:11:56.534 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:11:56.534 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:11:56.535 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:11:56.535 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-22 21:11:56.535 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-22 21:11:56.535 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:11:56.537 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:12:13.518 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755868295800_i7nvay0bb，文件: 用户相关数据.xlsx
2025-08-22 21:12:13.520 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:562 - 执行BIA数据分析
2025-08-22 21:12:13.790 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-22 21:12:13.791 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-22 21:12:13.791 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:586 - BIA数据分析完成
2025-08-22 21:12:13.794 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:12:13.794 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:12:13.794 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:12:13.794 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-22 21:12:13.794 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: 📊 已上传BIA数据：xlsx
2025-08-22 21:12:13.796 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:12:13.797 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
2025-08-22 21:12:13.797 | ERROR    | __main__:upload_bia:391 - BIA上传异常: Object of type int64 is not JSON serializable
2025-08-22 21:12:13.877 | ERROR    | __main__:upload_bia:392 - Traceback (most recent call last):
  File "F:\大创\agent\api_server.py", line 383, in upload_bia
    return jsonify(response)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flask\json\__init__.py", line 170, in jsonify
    return current_app.json.response(*args, **kwargs)  # type: ignore[return-value]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flask\json\provider.py", line 214, in response
    f"{self.dumps(obj, **dump_args)}\n", mimetype=self.mimetype
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flask\json\provider.py", line 179, in dumps
    return json.dumps(obj, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\flask\json\provider.py", line 121, in _default
    raise TypeError(f"Object of type {type(o).__name__} is not JSON serializable")
TypeError: Object of type int64 is not JSON serializable

2025-08-22 21:14:36.432 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868475857_rf5fqjjcl, 消息长度: 0
2025-08-22 21:14:36.432 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 21:14:36.441 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:14:36.441 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 21:14:36.445 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:14:54.559 | INFO     | __main__:submit_profile:430 - 收到用户档案提交，会话: session_1755868475857_rf5fqjjcl
2025-08-22 21:14:54.561 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:14:54.561 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-22 21:14:54.563 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 21:14:56.359 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868475857_rf5fqjjcl, 消息长度: 8
2025-08-22 21:14:56.359 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 21:14:56.362 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:14:56.362 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 21:14:56.362 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:15:06.935 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868475857_rf5fqjjcl, 消息长度: 11
2025-08-22 21:15:06.935 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 21:15:06.935 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 21:15:06.935 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 21:15:06.935 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:15:06.935 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 21:15:06.941 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:15:08.512 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868475857_rf5fqjjcl, 消息长度: 5
2025-08-22 21:15:08.512 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 21:15:08.514 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:15:08.514 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:15:08.515 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:15:08.515 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-22 21:15:08.515 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-22 21:15:08.515 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:15:08.517 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:15:16.260 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755868475857_rf5fqjjcl，文件: 用户相关数据.xlsx
2025-08-22 21:15:16.263 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:562 - 执行BIA数据分析
2025-08-22 21:15:16.636 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-22 21:15:16.638 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-22 21:15:16.638 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:586 - BIA数据分析完成
2025-08-22 21:15:16.641 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:15:16.641 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:15:16.642 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:15:16.642 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-22 21:15:16.643 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: 📊 已上传BIA数据：xlsx
2025-08-22 21:15:16.643 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:15:16.645 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
2025-08-22 21:16:13.851 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868573279_7f3rwm7wf, 消息长度: 0
2025-08-22 21:16:13.852 | INFO     | __main__:conversation_step:142 - 用户消息内容: ''
2025-08-22 21:16:13.859 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:16:13.860 | INFO     | src.agents.conversation_agent:brain_decision_node:157 - 首次启动，生成问候消息
2025-08-22 21:16:13.862 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:16:18.449 | INFO     | __main__:submit_profile:442 - 收到用户档案提交，会话: session_1755868573279_7f3rwm7wf
2025-08-22 21:16:18.449 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: greeting
2025-08-22 21:16:18.449 | INFO     | src.agents.conversation_agent:brain_decision_node:248 - 用户已填写档案，提供数据收集选项
2025-08-22 21:16:18.449 | INFO     | __main__:log_api_call:84 - API调用记录: submit-profile - 成功
2025-08-22 21:16:20.236 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868573279_7f3rwm7wf, 消息长度: 8
2025-08-22 21:16:20.237 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'GLIM评估问卷'
2025-08-22 21:16:20.240 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:16:20.240 | INFO     | src.agents.conversation_agent:brain_decision_node:213 - 用户选择GLIM评估问卷，用户输入: GLIM评估问卷
2025-08-22 21:16:20.241 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:16:50.042 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868573279_7f3rwm7wf, 消息长度: 11
2025-08-22 21:16:50.042 | INFO     | __main__:conversation_step:142 - 用户消息内容: '已完成GLIM评估问卷'
2025-08-22 21:16:50.042 | INFO     | __main__:conversation_step:144 - 额外数据: glim_completion
2025-08-22 21:16:50.043 | INFO     | __main__:conversation_step:161 - GLIM评估数据已保存到会话状态
2025-08-22 21:16:50.045 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:16:50.045 | INFO     | src.agents.conversation_agent:brain_decision_node:183 - 用户完成GLIM评估，用户输入: 已完成GLIM评估问卷
2025-08-22 21:16:50.047 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:17:46.925 | INFO     | __main__:conversation_step:141 - 收到对话请求，会话: session_1755868573279_7f3rwm7wf, 消息长度: 5
2025-08-22 21:17:46.925 | INFO     | __main__:conversation_step:142 - 用户消息内容: 'BIA数据'
2025-08-22 21:17:46.927 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:17:46.927 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:17:46.927 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:17:46.928 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: 'BIA数据'
2025-08-22 21:17:46.928 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: BIA数据
2025-08-22 21:17:46.928 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:17:46.929 | INFO     | __main__:log_api_call:84 - API调用记录: conversation - 成功
2025-08-22 21:17:59.149 | INFO     | __main__:upload_bia:319 - 收到BIA数据上传请求，会话: session_1755868573279_7f3rwm7wf，文件: 用户相关数据.xlsx
2025-08-22 21:17:59.151 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:562 - 执行BIA数据分析
2025-08-22 21:17:59.426 | INFO     | src.tools.bia_calculator:load_bia_data:64 - 成功加载BIA数据: 10行，61列
2025-08-22 21:17:59.427 | INFO     | src.tools.bia_calculator:calculate_patient_bia_analysis:89 - 完成患者 unknown 的BIA分析
2025-08-22 21:17:59.427 | INFO     | src.agents.conversation_agent:call_bia_analysis_node:586 - BIA数据分析完成
2025-08-22 21:17:59.431 | INFO     | src.agents.conversation_agent:brain_decision_node:147 - AI主脑分析当前状态，阶段: data_collection
2025-08-22 21:17:59.431 | WARNING  | src.agents.conversation_agent:brain_decision_node:152 - AI主脑调用次数过多，使用降级逻辑
2025-08-22 21:17:59.431 | WARNING  | src.agents.conversation_agent:_fallback_decision:404 - 使用降级决策流程
2025-08-22 21:17:59.432 | INFO     | src.agents.conversation_agent:_fallback_decision:410 - 🔍 降级模式：检查用户输入: '📊 已上传BIA数据：xlsx'
2025-08-22 21:17:59.432 | INFO     | src.agents.conversation_agent:_fallback_decision:433 - 🔄 降级模式：用户选择BIA数据分析，用户输入: 📊 已上传BIA数据：xlsx
2025-08-22 21:17:59.432 | INFO     | src.agents.conversation_agent:_fallback_decision:443 - 🚀 降级模式：设置BIA上传前端动作
2025-08-22 21:17:59.435 | INFO     | __main__:upload_bia:389 - 开始转换BIA分析数据为JSON可序列化格式
2025-08-22 21:17:59.435 | INFO     | __main__:upload_bia:391 - BIA数据转换完成
2025-08-22 21:17:59.435 | INFO     | __main__:log_api_call:84 - API调用记录: upload-bia - 成功
