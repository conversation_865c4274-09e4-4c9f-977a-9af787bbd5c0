#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LM Studio API客户端
用于与本地部署的大语言模型进行交互
支持华佗GPT主脑模型和视觉分析模型
"""
import requests
import json
import base64
from typing import Dict, List, Any, Optional, Union
from loguru import logger
import time
from pathlib import Path
from io import BytesIO
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
try:
    from config.settings import settings
except ImportError:
    # 如果无法导入配置，使用默认值
    class DefaultSettings:
        CONVERSATION_TIMEOUT = 600
        VISION_ANALYSIS_TIMEOUT = 2400  # 40分钟，适应本地大模型推理
        COMPREHENSIVE_ANALYSIS_TIMEOUT = 900  # 15分钟
    settings = DefaultSettings()

try:
    from PIL import Image
except ImportError:
    Image = None


class LMStudioClient:
    """LM Studio API客户端"""
    
    def __init__(self, base_url: str = None):
        """
        初始化LM Studio客户端

        Args:
            base_url: LM Studio API地址，如果为None则使用配置文件
        """
        # 如果没有提供base_url，使用配置文件中的设置
        if base_url is None:
            from config.settings import settings
            base_url = settings.LM_STUDIO_BASE_URL
        
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # 设置默认超时适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        # 创建自定义适配器，设置默认超时
        adapter = HTTPAdapter()
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 定义模型配置
        self.models = {
            'huatuogpt': {
                'name': 'freedomintelligence.huatuogpt-o1-7b',
                'description': '华佗GPT主脑模型',
                'type': 'text',
                'max_tokens': 10000,  # 增加到8192以支持更长的完整分析结果
                'context_length': 15000,  # 设置上下文长度为12000 tokens，支持完整的面部分析数据
                'temperature': 0.7
            },
            'vision': {
                'name': 'mimo-vl-7b-rl',
                'description': '视觉分析模型',
                'type': 'vision',
                'max_tokens': 4096,  # 视觉分析保持4096
                'temperature': 0.3
            }
        }
        
        logger.info(f"初始化LM Studio客户端: {self.base_url}")
        logger.info(f"配置模型: {list(self.models.keys())}")
        
        # 初始化统一提示词管理系统
        try:
            from config.unified_prompt_manager import get_system_prompt
            self.get_system_prompt = get_system_prompt
            logger.info("✅ 已连接统一提示词管理系统")
        except ImportError:
            logger.warning("⚠️ 统一提示词管理系统不可用，使用降级方案")
            self.get_system_prompt = self._get_fallback_system_prompt
    
    def _get_fallback_system_prompt(self, module: str, prompt_type: str = "system", **kwargs) -> str:
        """降级系统提示词"""
        base_prompt = """你是温州医科大学附属第一医院营养科的资深临床营养学专家，具备以下专业资质和特长：

## 专业背景
- 主治医师，营养医学专业硕士
- 15年以上临床营养工作经验
- GLIM国际营养不良诊断标准权威专家
- 擅长营养风险评估和个体化营养干预

## 核心职责
1. **专业营养评估**：基于GLIM标准进行准确的营养状况判定
2. **多模态数据分析**：整合面部视觉、BIA体成分、生化指标等多维度信息
3. **循证医学决策**：结合最新营养医学证据和临床指南
4. **个体化建议**：针对患者具体情况制定精准营养干预方案

## 分析原则
- 严格遵循GLIM国际标准的表型标准和病因学标准
- 重视患者年龄、疾病状态、药物使用等影响因素
- 综合考虑营养状况的动态变化趋势
- 注重营养干预的可行性和依从性

请基于这些专业要求，为每位患者提供准确、专业、个性化的营养评估和建议。"""
        
        if module == "facial":
            return base_prompt + "\n\n特别强调：请重点关注面部营养相关特征的评估分析。"
        elif module == "glim":
            return base_prompt + "\n\n特别强调：请严格按照GLIM标准进行营养不良诊断评估。"
        elif module == "comprehensive":
            return base_prompt + "\n\n特别强调：请进行综合的多模态营养状况分析。"
        else:
            return base_prompt
    
    def check_health(self) -> Dict[str, Any]:
        """检查API健康状态和模型信息"""
        try:
            # 检查基础连接
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code != 200:
                # 尝试直接检查模型接口
                try:
                    models_response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
                    if models_response.status_code == 200:
                        models_data = models_response.json()
                        current_models = [model['id'] for model in models_data.get('data', [])]
                        
                        return {
                            'healthy': True,
                            'current_models': current_models,
                            'available_models': self.models,
                            'base_url': self.base_url,
                            'note': '健康检查端点不可用，但模型接口正常'
                        }
                except:
                    pass
                
                return {
                    'healthy': False,
                    'error': f'健康检查失败: HTTP {response.status_code}',
                    'suggestion': '请确认LM Studio已启动并加载了模型'
                }
            
            # 获取当前加载的模型
            models_response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
            if models_response.status_code == 200:
                models_data = models_response.json()
                current_models = [model['id'] for model in models_data.get('data', [])]
                
                return {
                    'healthy': True,
                    'current_models': current_models,
                    'available_models': self.models,
                    'base_url': self.base_url
                }
            else:
                return {
                    'healthy': True,
                    'current_models': [],
                    'error': '无法获取模型列表',
                    'status_code': models_response.status_code
                }
                
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'suggestion': '请确认LM Studio已启动在127.0.0.1:1234端口'
            }
    
    def get_current_model(self) -> Optional[str]:
        """获取当前加载的模型"""
        try:
            response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = data.get('data', [])
                if models:
                    return models[0]['id']  # 返回第一个模型ID
            return None
        except Exception as e:
            logger.error(f"获取当前模型失败: {e}")
            return None

    def load_model_with_lms(self, model_name: str, ttl_seconds: int = None, context_length: int = None) -> Dict[str, Any]:
        """
        使用lms CLI工具加载模型

        Args:
            model_name: 模型名称
            ttl_seconds: TTL时间（秒），可选
            context_length: 上下文长度，可选

        Returns:
            Dict: 加载结果
        """
        try:
            logger.info(f"🔄 使用lms CLI加载模型: {model_name}")

            # 构建lms load命令
            cmd = ["lms", "load", model_name]
            if ttl_seconds:
                cmd.extend(["--ttl", str(ttl_seconds)])
                logger.info(f"   设置TTL: {ttl_seconds}秒")
            
            # 添加上下文长度参数
            if context_length:
                cmd.extend(["--context-length", str(context_length)])
                logger.info(f"   强制设置上下文长度: {context_length} tokens (模型: {model_name})")

            # 执行命令
            import subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                logger.info(f"✅ 模型加载成功: {model_name}")
                
                # 通知模型状态管理器更新状态
                try:
                    from api_server import model_status_manager
                    model_status_manager.update_status(force_check=True)
                except ImportError:
                    # 如果在其他上下文中运行，忽略导入错误
                    pass
                
                return {
                    'success': True,
                    'model': model_name,
                    'ttl': ttl_seconds,
                    'message': f'模型已加载: {model_name}',
                    'output': result.stdout
                }
            else:
                error_msg = f"lms load失败: {result.stderr or result.stdout}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'returncode': result.returncode
                }

        except subprocess.TimeoutExpired:
            error_msg = f"模型加载超时: {model_name}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"模型加载异常: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def unload_model_with_lms(self, model_name: str = None) -> Dict[str, Any]:
        """
        使用lms CLI工具卸载模型

        Args:
            model_name: 模型名称，如果为None则卸载所有模型

        Returns:
            Dict: 卸载结果
        """
        try:
            if model_name:
                logger.info(f"🔄 使用lms CLI卸载模型: {model_name}")
                cmd = ["lms", "unload", model_name]
            else:
                logger.info(f"🔄 使用lms CLI卸载所有模型")
                cmd = ["lms", "unload", "--all"]

            # 执行命令
            import subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                target = model_name or "所有模型"
                logger.info(f"✅ 模型卸载成功: {target}")
                
                # 通知模型状态管理器更新状态
                try:
                    from api_server import model_status_manager
                    model_status_manager.update_status(force_check=True)
                except ImportError:
                    # 如果在其他上下文中运行，忽略导入错误
                    pass
                
                return {
                    'success': True,
                    'model': model_name,
                    'message': f'模型已卸载: {target}',
                    'output': result.stdout
                }
            else:
                error_msg = f"lms unload失败: {result.stderr or result.stdout}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'returncode': result.returncode
                }

        except subprocess.TimeoutExpired:
            error_msg = f"模型卸载超时"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"模型卸载异常: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def cleanup_session_models(self) -> Dict[str, Any]:
        """
        会话开始时清理所有已加载的模型，确保每个新会话都从干净的状态开始

        Returns:
            Dict: 清理结果
        """
        try:
            logger.info("🧹 新会话开始 - 强制清理所有已加载的模型")

            # 强制卸载所有模型（不依赖检查结果）
            unload_result = self.unload_model_with_lms()  # 卸载所有模型
            
            if unload_result['success']:
                logger.info("✅ 强制模型清理完成，确保新会话从干净状态开始")
                
                # 二次验证：检查是否还有模型加载
                import subprocess
                check_result = subprocess.run(
                    ["lms", "ps"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                if check_result.returncode == 0:
                    output = check_result.stdout or ""
                    logger.info(f"📋 清理后模型状态检查: {output.strip()}")
                    
                    # 检查清理是否彻底
                    if "No models are currently loaded" in output:
                        logger.info("✅ 验证成功：所有模型已清理干净")
                        return {
                            'success': True,
                            'message': '所有模型已清理，新会话状态干净',
                            'models_unloaded': ['所有模型'],
                            'verified_clean': True
                        }
                    else:
                        # 解析残留模型
                        remaining_models = []
                        lines = output.strip().split('\n') if output else []
                        for line in lines:
                            line = line.strip()
                            if line.startswith('Identifier:'):
                                model_name = line.replace('Identifier:', '').strip()
                                if model_name:
                                    remaining_models.append(model_name)
                        
                        if remaining_models:
                            logger.warning(f"⚠️ 发现残留模型，尝试二次清理: {remaining_models}")
                            # 针对性清理残留模型
                            for model in remaining_models:
                                self.unload_model_with_lms(model)
                        
                        return {
                            'success': True,
                            'message': f'初次清理完成，发现{len(remaining_models)}个残留模型已二次清理',
                            'models_unloaded': ['所有模型'],
                            'remaining_models_cleaned': remaining_models,
                            'verified_clean': len(remaining_models) == 0
                        }
                else:
                    logger.warning(f"⚠️ 清理后状态检查失败: {check_result.stderr}")
                    return {
                        'success': True,  # 主清理成功，检查失败不影响
                        'message': '模型清理完成，但状态检查失败',
                        'models_unloaded': ['所有模型'],
                        'check_failed': True
                    }
                    
            else:
                logger.warning(f"⚠️ 强制模型清理失败: {unload_result.get('error', '未知错误')}")
                
                # 即使失败，也尝试通过API检查和清理华佗GPT
                try:
                    huatuo_config = self.models.get('huatuogpt', {})
                    if huatuo_config:
                        logger.info("🔧 尝试直接清理华佗GPT模型...")
                        self.unload_model_with_lms(huatuo_config.get('name', 'huatuogpt'))
                except Exception as e:
                    logger.warning(f"华佗GPT模型清理尝试失败: {e}")
                
                return {
                    'success': False,
                    'error': f"强制模型清理失败: {unload_result.get('error', '未知错误')}",
                    'attempted_huatuo_cleanup': True
                }

        except Exception as e:
            error_msg = f"会话模型清理异常: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }


    
    def call_huatuogpt(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        调用华佗GPT主脑模型进行综合分析

        Args:
            prompt: 综合分析提示词
            **kwargs: 其他参数

        Returns:
            分析结果字典
        """
        logger.info("调用华佗GPT主脑模型进行综合分析")

        # 检查华佗GPT模型是否已加载，避免重复加载
        huatuogpt_model = settings.HUATUO_MODEL_ID  # 从配置文件获取华佗GPT模型名称
        
        # 更严格的模型检查逻辑
        try:
            # 首先检查模型是否已加载
            models_response = self.session.get(f"{self.base_url}/v1/models", timeout=5)
            current_models = []
            if models_response.status_code == 200:
                models_data = models_response.json()
                current_models = [model['id'] for model in models_data.get('data', [])]
                logger.info(f"🔍 当前已加载的模型: {current_models}")
            
            # 检查华佗GPT是否在已加载的模型中
            model_loaded = any(huatuogpt_model in model_id for model_id in current_models)
            
            if model_loaded:
                logger.info(f"🔄 华佗GPT模型已加载，强制重新加载以应用15000 tokens上下文长度: {huatuogpt_model}")
                # 先卸载现有模型
                unload_result = self.unload_model_with_lms(huatuogpt_model)
                if unload_result['success']:
                    logger.info(f"✅ 华佗GPT模型已卸载: {huatuogpt_model}")
                else:
                    logger.warning(f"⚠️ 华佗GPT模型卸载失败，继续尝试加载: {unload_result['error']}")
            else:
                logger.info(f"🔄 华佗GPT模型未加载，开始加载: {huatuogpt_model}")
                logger.info(f"🔍 已加载模型列表: {current_models}")
            
            # 重新加载华佗GPT模型，强制设置15000 tokens上下文长度
            logger.info(f"🔄 加载华佗GPT模型，强制设置上下文长度: 15000 tokens")
            load_result = self.load_model_with_lms(huatuogpt_model, context_length=15000)

            if not load_result['success']:
                return {
                    'success': False,
                    'error': f"华佗GPT模型加载失败: {load_result['error']}",
                    'timestamp': time.time()
                }

            logger.info(f"✅ 华佗GPT模型重新加载成功: {huatuogpt_model} (上下文长度: 15000 tokens)")
                
        except Exception as e:
            logger.warning(f"⚠️ 检查模型状态时出错: {e}")
            logger.info("🔄 继续尝试加载华佗GPT模型")
            
            load_result = self.load_model_with_lms(huatuogpt_model, context_length=15000)
            if not load_result['success']:
                return {
                    'success': False,
                    'error': f"华佗GPT模型加载失败: {load_result['error']}",
                    'timestamp': time.time()
                }

        # 记录完整的提示词
        logger.info("=" * 80)
        logger.info("🤖 华佗GPT调用 - 完整提示词")
        logger.info("=" * 80)

        # 记录到增强日志
        try:
            # 使用全局变量方式记录增强日志
            import builtins
            if hasattr(builtins, 'add_enhanced_log'):
                builtins.add_enhanced_log(
                    'ai-call',
                    '华佗GPT调用',
                    model='华佗GPT',
                    prompt_length=len(prompt),
                    system_prompt=system_prompt,
                    user_prompt=prompt
                )
                logger.info("✅ 增强日志记录成功: 华佗GPT调用")
            else:
                logger.debug("增强日志函数不可用")
        except Exception as e:
            logger.debug(f"增强日志记录失败: {e}")

        # 使用统一的综合分析系统提示词
        try:
            base_system_prompt = self.get_system_prompt("comprehensive", "system")
        except Exception as e:
            logger.warning(f"无法从统一提示词系统获取提示词，使用降级方案: {e}")
            base_system_prompt = "你是温州医科大学附属第一医院营养科的资深临床营养学专家。"
        
        system_prompt = base_system_prompt + """

🚨 强制要求 - 必须严格遵守：
1. 必须首先输出营养状况诊断结论！不得省略！
2. 诊断结论必须在报告的最开始！
3. 不得询问用户任何问题，直接输出完整报告！
4. 不得显示思考过程，直接给出最终结果！

强制输出格式（必须按此顺序）：

🎯 营养状况诊断（必须首先输出）
**诊断结论**：[明确的营养状况诊断 - 如：营养状况正常/存在营养风险/轻度营养不良/中度营养不良/重度营养不良]
**严重程度**：[正常/轻度异常/中度营养不良/重度营养不良]
**系统置信度**：[0-100%的具体数值]
**诊断依据**：[基于GLIM标准的具体依据]

📋 分析概览
- 使用的评估模块：[实际使用的评估工具]
- 数据质量评估：[优秀/良好/一般/需改进]
- 多模态一致性：[高度一致/基本一致/存在分歧/数据不足]

💡 支持证据
[详细的证据支撑分析]

🏥 专业建议
[具体的治疗和生活建议]

⚠️ 重要：诊断结论是最重要的，必须在最前面！不得省略！"""

        # 记录系统提示词
        logger.info("📋 系统提示词:")
        logger.info(system_prompt)
        logger.info("-" * 40)
        logger.info("📝 用户提示词:")
        logger.info(prompt)
        logger.info("=" * 80)

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        try:
            model_config = self.models['huatuogpt']
            
            # 强制使用华佗GPT模型，不使用其他模型
            target_model = model_config['name']
            logger.info(f"强制使用华佗GPT模型: {target_model}")
            
            params = {
                "model": target_model,
                "messages": messages,
                "temperature": kwargs.get("temperature", model_config['temperature']),
                "max_tokens": kwargs.get("max_tokens", model_config['max_tokens']),
                "stream": False
            }
            
            logger.info(f"发送华佗GPT请求，使用模型: {target_model}，提示词长度: {len(prompt)}")
            
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=params,
                timeout=kwargs.get("timeout", settings.COMPREHENSIVE_ANALYSIS_TIMEOUT)
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    raw_result = data['choices'][0]['message']['content']
                    usage = data.get('usage', {})
                    
                    logger.info(f"华佗GPT原始响应长度: {len(raw_result)}")
                    if usage:
                        logger.info(f"Token使用情况: {usage}")

                    # 记录完整的原始响应内容
                    logger.info("=" * 80)
                    logger.info("🤖 华佗GPT原始响应 - 完整内容")
                    logger.info("=" * 80)
                    logger.info("📄 原始结果:")
                    logger.info(raw_result)
                    logger.info("=" * 80)
                    
                    # 处理华佗GPT-o1的思考部分，提取纯结果
                    result = self._extract_analysis_from_huatuo_response(raw_result)
                    
                    logger.info("=" * 80)
                    logger.info("🎯 华佗GPT提取的分析结果")
                    logger.info("=" * 80)
                    logger.info("📊 提取结果:")
                    logger.info(result)
                    logger.info(f"📏 提取后长度: {len(result)}")
                    logger.info("=" * 80)

                    # 记录到增强日志
                    try:
                        import builtins
                        if hasattr(builtins, 'add_enhanced_log'):
                            builtins.add_enhanced_log(
                                'ai-response',
                                '华佗GPT响应',
                                model=target_model,
                                response_content=result,
                                token_usage=usage,
                                system_prompt=system_prompt,
                                user_prompt=prompt,
                                performance={'response_length': len(result)}
                            )
                            logger.info("✅ 增强日志记录成功: 华佗GPT响应")
                        else:
                            logger.debug("增强日志函数不可用")
                    except Exception as e:
                        logger.debug(f"增强日志记录失败: {e}")

                    return {
                        'success': True,
                        'analysis': result,
                        'model': target_model,
                        'usage': usage,
                        'timestamp': time.time()
                    }
                else:
                    error_msg = f"华佗GPT响应格式错误: {data}"
                    logger.error(error_msg)
                    return {
                        'success': False,
                        'error': error_msg,
                        'model': target_model
                    }
            else:
                error_msg = f"华佗GPT请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code,
                    'response_text': response.text
                }
                
        except Exception as e:
            error_msg = f"华佗GPT调用异常: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'exception': str(e)
            }
    
    def _extract_analysis_from_huatuo_response(self, raw_response: str) -> str:
        """
        从华佗GPT-o1的响应中提取纯分析结果，去除思考部分
        华佗GPT-o1的输出格式：## Thinking (思考过程) -> ## Final Response (最终回复)
        
        Args:
            raw_response: 华佗GPT的原始响应
            
        Returns:
            提取的分析结果
        """
        try:
            logger.info("🔍 开始提取华佗GPT-o1响应中的最终回复部分")
            
            # 华佗GPT-o1的标准格式：## Thinking 和 ## Final Response
            # 优先方法：查找 ## Final Response 标签
            if '## Final Response' in raw_response:
                start_idx = raw_response.find('## Final Response') + len('## Final Response')
                # 提取 Final Response 之后的所有内容
                extracted = raw_response[start_idx:].strip()
                logger.info("✅ 使用 ## Final Response 标签提取分析结果")
                return extracted
            
            # 备用方法1：查找类似的结束标识
            final_response_markers = [
                '## Final Response', '## 最终回复', '## 最终响应', '## Response',
                '## 回复', '## 分析结果', '## 结果', '## RESPONSE'
            ]
            
            for marker in final_response_markers:
                if marker in raw_response:
                    start_idx = raw_response.find(marker) + len(marker)
                    extracted = raw_response[start_idx:].strip()
                    logger.info(f"✅ 使用标识符'{marker}'提取分析结果")
                    return extracted
            
            # 备用方法2: 如果没有 ## Final Response，但有 ## Thinking，提取 Thinking 之后的非思考内容
            if '## Thinking' in raw_response:
                # 找到 Thinking 部分的结束位置
                thinking_start = raw_response.find('## Thinking')
                
                # 寻找思考部分结束的标识
                thinking_end_markers = [
                    '\n\n##', '\n## ', '思考完毕', '思考结束', '分析开始', 
                    '基于以上思考', '现在给出', '以下是'
                ]
                
                thinking_end = -1
                for marker in thinking_end_markers:
                    marker_pos = raw_response.find(marker, thinking_start + len('## Thinking'))
                    if marker_pos != -1:
                        thinking_end = marker_pos
                        break
                
                if thinking_end != -1:
                    extracted = raw_response[thinking_end:].strip()
                    # 清理可能的标识符
                    for marker in thinking_end_markers:
                        if extracted.startswith(marker.strip()):
                            extracted = extracted[len(marker.strip()):].strip()
                            break
                    logger.info("✅ 通过 ## Thinking 定位提取分析结果")
                    return extracted
                else:
                    # 如果找不到思考结束标识，取 Thinking 之后的一定距离的内容
                    thinking_section = raw_response[thinking_start:]
                    lines = thinking_section.split('\n')
                    
                    # 跳过前几行（通常是思考内容），寻找结构化内容
                    for i, line in enumerate(lines):
                        if line.strip().startswith('##') and i > 0 and 'thinking' not in line.lower():
                            extracted = '\n'.join(lines[i:]).strip()
                            logger.info("✅ 在 ## Thinking 后找到结构化内容")
                            return extracted
            
            # 备用方法3: 查找结构化分析内容（## 📋 等）
            structured_start_patterns = [
                '## 📋 分析概览', '## 🎯 营养状况评估', '## 💡 支持证据', 
                '## 🏥 专业建议', '## 📅 后续建议', '## ⚠️ 重要提醒'
            ]
            
            for pattern in structured_start_patterns:
                if pattern in raw_response:
                    start_idx = raw_response.find(pattern)
                    extracted = raw_response[start_idx:].strip()
                    logger.info(f"✅ 使用结构化格式'{pattern}'提取分析结果")
                    return extracted
            
            # 备用方法4: 如果响应很长，可能整个就包含思考过程，尝试智能分割
            if len(raw_response) > 500:
                lines = raw_response.split('\n')
                
                # 寻找明显的分析开始标识
                analysis_start_idx = -1
                for i, line in enumerate(lines):
                    line_clean = line.strip().lower()
                    if (line_clean.startswith('##') and 
                        any(keyword in line_clean for keyword in ['分析', 'analysis', '评估', '诊断', '建议'])):
                        analysis_start_idx = i
                        break
                
                if analysis_start_idx != -1:
                    extracted = '\n'.join(lines[analysis_start_idx:]).strip()
                    logger.info(f"✅ 智能定位分析内容 (从第{analysis_start_idx}行开始)")
                    return extracted
            
            # 默认情况：如果无法识别格式，返回原始响应并警告
            logger.warning("⚠️ 无法识别华佗GPT-o1的标准格式，返回原始响应")
            logger.warning("⚠️ 期望格式：## Thinking ... ## Final Response ...")
            logger.warning(f"⚠️ 实际收到长度：{len(raw_response)} 字符")
            logger.warning(f"⚠️ 前100字符：{raw_response[:100]}")
            return raw_response.strip()
            
        except Exception as e:
            logger.error(f"❌ 提取华佗GPT分析结果失败: {e}")
            logger.error("❌ 返回原始响应作为降级方案")
            return raw_response.strip()
    
    def call_vision_model(self, image_data: Union[str, bytes], prompt: str, **kwargs) -> Dict[str, Any]:
        """
        调用视觉分析模型分析面部图像

        Args:
            image_data: 图像数据（base64字符串或字节数据）
            prompt: 分析提示词
            **kwargs: 其他参数

        Returns:
            分析结果字典
        """
        logger.info("调用视觉分析模型进行面部图像分析")

        # 加载视觉模型
        vision_model = settings.VLM_MODEL_ID  # 从配置文件获取视觉模型名称
        logger.info(f"🔄 开始加载视觉模型: {vision_model}")

        load_result = self.load_model_with_lms(vision_model, ttl_seconds=600)  # 10分钟TTL

        if not load_result['success']:
            return {
                'success': False,
                'error': f"视觉模型加载失败: {load_result['error']}",
                'timestamp': time.time()
            }

        logger.info(f"✅ 视觉模型加载成功: {vision_model}")
        
        # 处理图像数据
        if isinstance(image_data, bytes):
            base64_image = base64.b64encode(image_data).decode('utf-8')
        elif isinstance(image_data, str):
            # 假设已经是base64格式
            base64_image = image_data
        else:
            return {
                'success': False,
                'error': '不支持的图像数据格式'
            }
        
        # 构建视觉分析的专业提示词
        try:
            system_prompt = self.get_system_prompt("facial", "system")
        except Exception as e:
            logger.warning(f"无法从统一提示词系统获取面部分析提示词，使用降级方案: {e}")
            system_prompt = """你是一位专业的营养医学影像分析专家，专门通过面部特征评估营养状况。
你需要仔细观察面部图像中与营养不良相关的特征，包括但不限于：

1. 面部轮廓和体型特征
2. 颞部是否有凹陷
3. 面颊是否消瘦或饱满
4. 颧骨是否突出
5. 眼窝是否深陷
6. 皮肤状况和色泽
7. 整体面容的营养状态

请严格按照以下格式输出分析结果：

## 🔍 面部特征观察
[详细描述观察到的面部特征]

## 📊 营养相关特征评估
[分析各项营养相关指标]

## ⚖️ 综合评估
**营养状况评估：[正常/轻度营养不良/中度营养不良/重度营养不良]**
**置信度：[0-100%]**

## 🔍 关键发现
[列出支持评估结论的关键观察点]

## ⚠️ 限制说明
基于单张面部图像的分析具有一定局限性，建议结合其他临床数据综合评估。

请基于图像进行客观、专业的分析。"""

        messages = [
            {
                "role": "system", 
                "content": system_prompt
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
        
        try:
            model_config = self.models['vision']

            # 强制使用配置的视觉模型，不使用当前加载的模型
            target_model = model_config['name']
            logger.info(f"强制使用视觉模型: {target_model}")
            
            params = {
                "model": target_model,
                "messages": messages,
                "temperature": kwargs.get("temperature", model_config['temperature']),
                "max_tokens": kwargs.get("max_tokens", model_config['max_tokens']),
                "stream": False
            }
            
            timeout_value = kwargs.get("timeout", settings.VISION_ANALYSIS_TIMEOUT)
            logger.info(f"发送视觉分析请求，使用模型: {target_model}，图像大小: {len(base64_image)} 字符")
            logger.info(f"设置超时时间: {timeout_value} 秒")

            # 记录完整的视觉分析提示词
            logger.info("=" * 80)
            logger.info("👁️ 视觉分析调用 - 完整提示词")
            logger.info("=" * 80)
            logger.info("📝 分析提示词:")
            logger.info(prompt)
            logger.info(f"🖼️ 图像数据: Base64编码，长度 {len(base64_image)} 字符")
            logger.info("=" * 80)

            # 记录到增强日志
            try:
                import builtins
                if hasattr(builtins, 'add_enhanced_log'):
                    builtins.add_enhanced_log(
                        'ai-call',
                        '视觉分析调用',
                        model=target_model,
                        user_prompt=prompt,
                        image_size=len(base64_image),
                        timeout=timeout_value
                    )
                    logger.info("✅ 增强日志记录成功: 视觉分析调用")
                else:
                    logger.debug("增强日志函数不可用")
            except Exception as e:
                logger.debug(f"增强日志记录失败: {e}")

            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=params,
                timeout=timeout_value
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    result = data['choices'][0]['message']['content']
                    usage = data.get('usage', {})
                    
                    logger.info(f"视觉分析响应成功，输出长度: {len(result)}")
                    if usage:
                        logger.info(f"Token使用情况: {usage}")

                    # 记录完整的视觉分析响应
                    logger.info("=" * 80)
                    logger.info("👁️ 视觉分析响应 - 完整内容")
                    logger.info("=" * 80)
                    logger.info("📄 分析结果:")
                    logger.info(result)
                    logger.info("=" * 80)

                    # 记录到增强日志
                    try:
                        import builtins
                        if hasattr(builtins, 'add_enhanced_log'):
                            builtins.add_enhanced_log(
                                'ai-response',
                                '视觉分析响应',
                                model=target_model,
                                response_content=result,
                                token_usage=usage,
                                user_prompt=prompt,
                                performance={'response_length': len(result), 'image_size': len(base64_image)}
                            )
                            logger.info("✅ 增强日志记录成功: 视觉分析响应")
                        else:
                            logger.debug("增强日志函数不可用")
                    except Exception as e:
                        logger.debug(f"增强日志记录失败: {e}")

                    # 自动卸载视觉模型以释放显存
                    logger.info("💡 视觉分析完成！开始卸载视觉模型以释放显存...")
                    unload_result = self.unload_model_with_lms(vision_model)

                    if unload_result['success']:
                        logger.info(f"✅ 视觉模型已卸载: {vision_model}")
                    else:
                        logger.warning(f"⚠️ 视觉模型卸载失败: {unload_result['error']}")

                    return {
                        'success': True,
                        'analysis': result,
                        'model': target_model,
                        'usage': usage,
                        'timestamp': time.time(),
                        'vision_model_unloaded': unload_result['success']
                    }
                else:
                    error_msg = f"视觉模型响应格式错误: {data}"
                    logger.error(error_msg)
                    return {
                        'success': False,
                        'error': error_msg,
                        'model': target_model
                    }
            else:
                error_msg = f"视觉分析请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code,
                    'response_text': response.text
                }

        except Exception as e:
            error_msg = f"视觉分析调用异常: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'exception': str(e)
            }
    
    def complete(self, prompt: str, model: str = None, **kwargs) -> Optional[str]:
        """
        通用文本生成完成（向后兼容）
        
        Args:
            prompt: 输入提示词
            model: 模型名称（可选）
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        try:
            # 如果没有指定模型，使用华佗GPT
            if not model:
                result = self.call_huatuogpt(prompt, **kwargs)
                return result.get('analysis') if result['success'] else None
            
            # 直接调用指定模型
            current_model = self.get_current_model()
            target_model = current_model or model
            
            params = {
                "model": target_model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 1000),
                "stream": False
            }
            
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=params,
                timeout=kwargs.get("timeout", settings.CONVERSATION_TIMEOUT)
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    return data['choices'][0]['message']['content']
            
            logger.error(f"通用完成请求失败: {response.status_code}")
            return None
            
        except Exception as e:
            logger.error(f"通用完成调用异常: {e}")
            return None


# 向后兼容的类别名
ModelManager = LMStudioClient