#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置设置
"""
import os
from typing import Dict, Any

class Settings:
    """系统配置类"""
    
    # LM Studio配置
    LM_STUDIO_BASE_URL: str = os.getenv("LM_STUDIO_BASE_URL", "http://127.0.0.1:1234")
    LM_STUDIO_API_KEY: str = os.getenv("LM_STUDIO_API_KEY", "not-needed")
    
    # 模型配置
    HUATUO_MODEL_ID: str = os.getenv("HUATUO_MODEL_ID", "freedomintelligence.huatuogpt-o1-7b")
    VLM_MODEL_ID: str = os.getenv("VLM_MODEL_ID", "mimo-vl-7b-rl")
    
    # 推理参数
    TEMPERATURE: float = float(os.getenv("TEMPERATURE", "0.2"))
    MAX_TOKENS: int = int(os.getenv("MAX_TOKENS", "2048"))
    
    # VLM投票配置
    VLM_INSTANCES: int = int(os.getenv("VLM_INSTANCES", "3"))
    CONSENSUS_THRESHOLD: float = float(os.getenv("CONSENSUS_THRESHOLD", "0.6"))

    # 超时配置 - 适应本地大模型推理需求（单位：秒）
    CONVERSATION_TIMEOUT: int = int(os.getenv("CONVERSATION_TIMEOUT", "600"))  # 10分钟
    VISION_ANALYSIS_TIMEOUT: int = int(os.getenv("VISION_ANALYSIS_TIMEOUT", "2400"))  # 40分钟，适应MIMO-VL等思考型模型
    COMPREHENSIVE_ANALYSIS_TIMEOUT: int = int(os.getenv("COMPREHENSIVE_ANALYSIS_TIMEOUT", "900"))  # 15分钟
    HTTP_REQUEST_TIMEOUT: int = int(os.getenv("HTTP_REQUEST_TIMEOUT", "2400"))  # 40分钟，与视觉分析保持一致
    WEBSOCKET_TIMEOUT: int = int(os.getenv("WEBSOCKET_TIMEOUT", "2400"))  # 40分钟
    FILE_UPLOAD_TIMEOUT: int = int(os.getenv("FILE_UPLOAD_TIMEOUT", "300"))  # 5分钟
    
    # 路径配置
    PROJECT_ROOT: str = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DATA_DIR: str = os.path.join(PROJECT_ROOT, "data")
    KNOWLEDGE_DIR: str = os.path.join(DATA_DIR, "knowledge") 
    SAMPLES_DIR: str = os.path.join(DATA_DIR, "samples")
    TEMP_DIR: str = os.path.join(DATA_DIR, "temp")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # === 自动模式配置（新增） ===
    # 文件上传限制
    ALLOWED_EXTENSIONS: Dict[str, list] = {
        'image': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
        'bia': ['.xlsx', '.csv', '.xls'],
        'glim': ['.json', '.txt'],
        'text': ['.pdf', '.doc', '.docx', '.txt', '.md']
    }
    MAX_UPLOAD_SIZE_MB: int = int(os.getenv("MAX_UPLOAD_SIZE_MB", "50"))
    
    # 批量上传配置
    BATCH_UPLOAD_CONFIG: Dict[str, Any] = {
        'max_files': int(os.getenv("BATCH_MAX_FILES", "10")),
        'allowed_total_size_mb': int(os.getenv("BATCH_MAX_TOTAL_SIZE_MB", "100")),
        'temp_storage_days': int(os.getenv("TEMP_STORAGE_DAYS", "7"))
    }
    
    # 自动分析最小要求
    AUTO_ANALYSIS_MIN_REQUIREMENTS: Dict[str, Any] = {
        'min_modules': int(os.getenv("AUTO_MIN_MODULES", "1")),
        'preferred_modules': ['glim_assessment', 'facial_analysis']
    }
    
    # 并发控制配置
    PARALLEL_ANALYSIS_CONFIG: Dict[str, Any] = {
        'max_concurrent_vlm': int(os.getenv("MAX_CONCURRENT_VLM", "1")),
        'max_total_concurrent': int(os.getenv("MAX_TOTAL_CONCURRENT", "3")),
        'enable_resource_monitoring': True
    }
    
    # 重试机制配置
    RETRY_CONFIG: Dict[str, Any] = {
        'max_retries': int(os.getenv("MAX_RETRIES", "2")),
        'retry_delay_seconds': int(os.getenv("RETRY_DELAY", "30")),
        'backoff_factor': float(os.getenv("RETRY_BACKOFF_FACTOR", "1.5")),
        'retry_conditions': {
            'facial_analysis': {
                'retryable_errors': ['TimeoutError', 'ConnectionError', 'HTTPError']
            },
            'bia_analysis': {
                'retryable_errors': ['FileNotFoundError', 'PermissionError']
            },
            'glim_assessment': {
                'retryable_errors': ['JSONDecodeError', 'KeyError']
            }
        }
    }
    
    # 总体分析超时
    TOTAL_ANALYSIS_TIMEOUT: int = int(os.getenv("TOTAL_ANALYSIS_TIMEOUT", "3600"))  # 1小时
    
    @classmethod
    def get_model_config(cls) -> Dict[str, Any]:
        """获取模型配置"""
        return {
            "base_url": cls.LM_STUDIO_BASE_URL,
            "api_key": cls.LM_STUDIO_API_KEY,
            "huatuo_model": cls.HUATUO_MODEL_ID,
            "vlm_model": cls.VLM_MODEL_ID,
            "temperature": cls.TEMPERATURE,
            "max_tokens": cls.MAX_TOKENS
        }
    
    @classmethod
    def get_vlm_config(cls) -> Dict[str, Any]:
        """获取VLM配置"""
        return {
            "instances": cls.VLM_INSTANCES,
            "consensus_threshold": cls.CONSENSUS_THRESHOLD,
            "temperature": cls.TEMPERATURE
        }

    @classmethod
    def get_timeout_config(cls) -> Dict[str, int]:
        """获取超时配置"""
        return {
            "conversation": cls.CONVERSATION_TIMEOUT,
            "vision_analysis": cls.VISION_ANALYSIS_TIMEOUT,
            "comprehensive_analysis": cls.COMPREHENSIVE_ANALYSIS_TIMEOUT,
            "http_request": cls.HTTP_REQUEST_TIMEOUT,
            "websocket": cls.WEBSOCKET_TIMEOUT,
            "file_upload": cls.FILE_UPLOAD_TIMEOUT,
            "total_analysis": cls.TOTAL_ANALYSIS_TIMEOUT
        }
    
    @classmethod
    def get_batch_upload_config(cls) -> Dict[str, Any]:
        """获取批量上传配置"""
        return cls.BATCH_UPLOAD_CONFIG
    
    @classmethod
    def get_auto_analysis_config(cls) -> Dict[str, Any]:
        """获取自动分析配置"""
        return cls.AUTO_ANALYSIS_MIN_REQUIREMENTS
    
    @classmethod
    def get_parallel_config(cls) -> Dict[str, Any]:
        """获取并发控制配置"""
        return cls.PARALLEL_ANALYSIS_CONFIG
    
    @classmethod
    def get_retry_config(cls) -> Dict[str, Any]:
        """获取重试配置"""
        return cls.RETRY_CONFIG
    
    @classmethod
    def is_supported_file(cls, filename: str, mimetype: str = None) -> bool:
        """检查文件是否被支持"""
        from pathlib import Path
        file_ext = Path(filename).suffix.lower()
        
        for extensions in cls.ALLOWED_EXTENSIONS.values():
            if file_ext in extensions:
                return True
        return False
    
    @classmethod
    def get_file_size_limit_mb(cls) -> int:
        """获取单文件大小限制（MB）"""
        return cls.MAX_UPLOAD_SIZE_MB

# 全局设置实例
settings = Settings()

