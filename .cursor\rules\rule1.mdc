---
alwaysApply: true
---
[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool  = "context7"

# MCP Interactive Feedback 规则
注意在任务完成前根据mcp-feedback-enhanced工具的要求进行工具调用

项目中涉及到的所有大模型均采用本地部署的方式，使用的工具是LMstudio；
创建测试文件后，经过测试，测试成功后，删除测试文件及其产生的垃圾；
涉及到大模型api相关的，请先查看这个文件docs\LM_Studio_API_Reference.md；
后端API服务器：运行在 localhost:5000 ，前端静态服务器：运行在 localhost:8080;
测试只需要清除原有的所有进程，然后启动start_brain_system.py就可以了，无需反复检查进程；
lms的tools docs文档地址，可自行查看https://lmstudio.ai/docs/app/api/tools