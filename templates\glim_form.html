<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球领导人发起营养不良诊断标准（GLIM）标准及分级标准</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SimSun', serif;
            line-height: 1.4;
            background-color: #ffffff;
            padding: 20px;
            font-size: 14px;
            overflow-x: hidden; /* 隐藏水平滚动条 */
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin-bottom: 20px;
            letter-spacing: 1px;
        }

        /* 主要表格样式 */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
            border: 2px solid #000;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
            font-weight: normal;
        }

        .main-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .step-header {
            width: 120px;
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .content-header {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        /* 详细表格样式 */
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
            border: 2px solid #000;
        }

        .detail-table th,
        .detail-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
        }

        .detail-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .category-cell {
            width: 120px;
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .content-cell {
            text-align: left;
            padding: 8px;
        }

        .checkbox-cell {
            width: 40px;
            text-align: center;
        }

        .note-text {
            font-size: 12px;
            margin: 10px 0;
            text-align: left;
        }

        /* 输入控件样式 */
        input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        /* 患者信息表格 */
        .patient-info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
            border: 1px solid #000;
        }

        .patient-info-table td {
            border: 1px solid #000;
            padding: 8px;
        }

        .patient-info-table input {
            border: none;
            background: transparent;
            width: 100%;
            font-size: 12px;
        }

        .action-buttons {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 25px;
            margin: 0 10px;
            border: 1px solid #000;
            background: #f0f0f0;
            font-size: 14px;
            cursor: pointer;
            font-family: inherit;
        }

        .btn:hover {
            background: #e0e0e0;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .result-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
            display: none;
        }

        .result-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .diagnosis-result {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #000;
            font-weight: bold;
            text-align: center;
        }

        .diagnosis-negative {
            background: #e8f5e8;
        }

        .diagnosis-moderate {
            background: #fff3cd;
        }

        .diagnosis-severe {
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>全球领导人发起营养不良诊断标准（GLIM）标准及分级标准</h1>
        </div>

        <form id="glimForm">

            <!-- 主要GLIM标准表 -->
            <table class="main-table">
                <tr>
                    <th class="step-header">步骤</th>
                    <th class="content-header">内容</th>
                </tr>
                <tr>
                    <td class="step-header">营养筛查</td>
                    <td>使用MNA-SF或NRS 2002工具</td>
                </tr>
                <tr>
                    <td rowspan="2" class="step-header">评定（诊断）方法</td>
                    <td>表型诊断标准<sup>a</sup></td>
                </tr>
                <tr>
                    <td>病因诊断标准<sup>b</sup></td>
                </tr>
                <tr>
                    <td class="step-header">做出评定（诊断）</td>
                    <td>至少满足一项表型诊断标准和一项病因诊断标准可诊断营养不良</td>
                </tr>
                <tr>
                    <td class="step-header">严重程度评级</td>
                    <td>基于表现型指标评定营养不良严重程度<sup>c</sup></td>
                </tr>
            </table>

            <div class="note-text">注：a、b、c判断标准见下表</div>

            <!-- 表型诊断标准详细表 -->
            <table class="detail-table">
                <tr>
                    <th style="width: 120px;">表型诊断标准</th>
                    <th>内涵</th>
                    <th style="width: 40px;">是</th>
                    <th style="width: 40px;">否</th>
                </tr>
                <tr>
                    <td class="category-cell">非自主性体重减轻</td>
                    <td class="content-cell">过去6个月内体重减轻>5%；或超过6个月内体重减轻>10%</td>
                    <td class="checkbox-cell"><input type="checkbox" name="weight_loss" id="weight_loss_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="weight_loss_no" id="weight_loss_no"></td>
                </tr>
                <tr>
                    <td class="category-cell">低BMI</td>
                    <td class="content-cell">&lt;70岁，BMI&lt;18.5kg/m²；≥70岁，BMI&lt;20kg/m²</td>
                    <td class="checkbox-cell"><input type="checkbox" name="low_bmi" id="low_bmi_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="low_bmi_no" id="low_bmi_no"></td>
                </tr>
                <tr>
                    <td rowspan="5" class="category-cell">肌肉质量减少</td>
                    <td class="content-cell">ASMI（kg/m²）DXA 男性&lt;7，女性&lt;5.4</td>
                    <td rowspan="5" class="checkbox-cell"><input type="checkbox" name="muscle_loss" id="muscle_loss_yes"></td>
                    <td rowspan="5" class="checkbox-cell"><input type="checkbox" name="muscle_loss_no" id="muscle_loss_no"></td>
                </tr>
                <tr>
                    <td class="content-cell">BIA 男性&lt;7，女性&lt;5.7</td>
                </tr>
                <tr>
                    <td class="content-cell">FFMI（kg/m²）男性&lt;17，女性&lt;15</td>
                </tr>
                <tr>
                    <td class="content-cell">小腿围（cm）男性&lt;34，女性&lt;33</td>
                </tr>
                <tr>
                    <td class="content-cell">握力（kg）男性&lt;28，女性&lt;18</td>
                </tr>
            </table>
            <!-- 病因诊断标准详细表 -->
            <table class="detail-table">
                <tr>
                    <th style="width: 120px;">病因诊断标准</th>
                    <th>内涵</th>
                    <th style="width: 40px;">是</th>
                    <th style="width: 40px;">否</th>
                </tr>
                <tr>
                    <td class="category-cell">食物摄入减少或吸收障碍</td>
                    <td class="content-cell">食物摄入量≤50%的能量需要量持续>1周，或食物摄入量减少超过2周或者合并任何影响消化和吸收的慢性胃肠状况</td>
                    <td class="checkbox-cell"><input type="checkbox" name="food_intake_reduction" id="food_intake_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="food_intake_reduction_no" id="food_intake_no"></td>
                </tr>
                <tr>
                    <td class="category-cell">疾病负担或炎症</td>
                    <td class="content-cell">急性疾病（或创伤）或者慢性疾病相关的炎症</td>
                    <td class="checkbox-cell"><input type="checkbox" name="disease_inflammation" id="inflammation_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="disease_inflammation_no" id="inflammation_no"></td>
                </tr>
            </table>
            <!-- 营养不良严重程度评级表 -->
            <table class="detail-table">
                <tr>
                    <th style="width: 120px;">营养不良严重程度评级</th>
                    <th>内涵</th>
                    <th style="width: 40px;">是</th>
                    <th style="width: 40px;">否</th>
                </tr>
                <tr>
                    <td class="category-cell">体重显著下降</td>
                    <td class="content-cell">过去6个月内体重减轻>10%；或超过6个月内体重减轻>20%</td>
                    <td class="checkbox-cell"><input type="checkbox" name="severe_weight_loss" id="severe_weight_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="severe_weight_loss_no" id="severe_weight_no"></td>
                </tr>
                <tr>
                    <td class="category-cell">低BMI</td>
                    <td class="content-cell">&lt;70岁，BMI&lt;18.5kg/m²；≥70岁，BMI&lt;20kg/m²</td>
                    <td class="checkbox-cell"><input type="checkbox" name="severe_bmi" id="severe_bmi_yes"></td>
                    <td class="checkbox-cell"><input type="checkbox" name="severe_bmi_no" id="severe_bmi_no"></td>
                </tr>
            </table>

            <!-- 备注信息 -->
            <div style="margin: 20px 0;">
                <label for="notes" style="font-weight: bold; display: block; margin-bottom: 8px;">备注：</label>
                <textarea id="notes" name="notes" rows="3" style="width: 100%; padding: 10px; border: 1px solid #000; font-family: inherit; font-size: 12px;" placeholder="请输入相关临床备注信息..."></textarea>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button type="button" class="btn" onclick="resetForm()">重置表单</button>
                <button type="submit" class="btn btn-primary">提交</button>
            </div>
        </form>

        <!-- 结果显示区域 -->
        <div class="result-section" id="resultSection">
            <div class="result-title">GLIM评估结果</div>
            <div id="diagnosisResult"></div>
            <div id="detailsResult"></div>
        </div>
    </div>

    <script>
        // 确保复选框互斥
        function setupCheckboxes() {
            // 表型标准复选框
            const weightLossYes = document.getElementById('weight_loss_yes');
            const weightLossNo = document.getElementById('weight_loss_no');
            const lowBmiYes = document.getElementById('low_bmi_yes');
            const lowBmiNo = document.getElementById('low_bmi_no');
            const muscleLossYes = document.getElementById('muscle_loss_yes');
            const muscleLossNo = document.getElementById('muscle_loss_no');

            // 病因标准复选框
            const foodIntakeYes = document.getElementById('food_intake_yes');
            const foodIntakeNo = document.getElementById('food_intake_no');
            const inflammationYes = document.getElementById('inflammation_yes');
            const inflammationNo = document.getElementById('inflammation_no');

            // 严重程度标准复选框
            const severeWeightYes = document.getElementById('severe_weight_yes');
            const severeWeightNo = document.getElementById('severe_weight_no');
            const severeBmiYes = document.getElementById('severe_bmi_yes');
            const severeBmiNo = document.getElementById('severe_bmi_no');

            // 设置互斥关系
            function setupMutualExclusive(checkbox1, checkbox2) {
                checkbox1.addEventListener('change', function() {
                    if (this.checked) checkbox2.checked = false;
                });
                checkbox2.addEventListener('change', function() {
                    if (this.checked) checkbox1.checked = false;
                });
            }

            setupMutualExclusive(weightLossYes, weightLossNo);
            setupMutualExclusive(lowBmiYes, lowBmiNo);
            setupMutualExclusive(muscleLossYes, muscleLossNo);
            setupMutualExclusive(foodIntakeYes, foodIntakeNo);
            setupMutualExclusive(inflammationYes, inflammationNo);
            setupMutualExclusive(severeWeightYes, severeWeightNo);
            setupMutualExclusive(severeBmiYes, severeBmiNo);
        }

        // 计算GLIM评估结果
        function calculateGLIMResults() {
            // 收集表型标准
            const phenotypicCriteria = [];
            if (document.getElementById('weight_loss_yes').checked) {
                phenotypicCriteria.push('非自主性体重减轻');
            }
            if (document.getElementById('low_bmi_yes').checked) {
                phenotypicCriteria.push('低BMI');
            }
            if (document.getElementById('muscle_loss_yes').checked) {
                phenotypicCriteria.push('肌肉质量减少');
            }

            // 收集病因标准
            const etiologicCriteria = [];
            if (document.getElementById('food_intake_yes').checked) {
                etiologicCriteria.push('食物摄入减少或吸收障碍');
            }
            if (document.getElementById('inflammation_yes').checked) {
                etiologicCriteria.push('疾病负担或炎症');
            }

            // 收集严重程度标准
            const severeCriteria = [];
            if (document.getElementById('severe_weight_yes').checked) {
                severeCriteria.push('体重显著下降');
            }
            if (document.getElementById('severe_bmi_yes').checked) {
                severeCriteria.push('低BMI');
            }

            // 第一步：营养不良诊断
            const hasPhenotypic = phenotypicCriteria.length > 0;
            const hasEtiologic = etiologicCriteria.length > 0;
            const isMalnutrition = hasPhenotypic && hasEtiologic;

            // 第二步：严重程度评级（仅在确诊营养不良后进行）
            let severity = null;
            if (isMalnutrition) {
                severity = severeCriteria.length > 0 ? '重度营养不良' : '中度营养不良';
            }

            // 构建完整结果
            return {
                // 原始填表数据
                form_data: {
                    phenotypic_criteria: {
                        weight_loss: document.getElementById('weight_loss_yes').checked,
                        low_bmi: document.getElementById('low_bmi_yes').checked,
                        muscle_loss: document.getElementById('muscle_loss_yes').checked
                    },
                    etiologic_criteria: {
                        food_intake_reduction: document.getElementById('food_intake_yes').checked,
                        disease_inflammation: document.getElementById('inflammation_yes').checked
                    },
                    severity_criteria: {
                        severe_weight_loss: document.getElementById('severe_weight_yes').checked,
                        severe_bmi: document.getElementById('severe_bmi_yes').checked
                    },
                    notes: document.getElementById('notes').value
                },
                // 计算结果
                calculation_results: {
                    diagnosis: {
                        is_malnutrition: isMalnutrition,
                        result: isMalnutrition ? severity : '未诊断为营养不良'
                    },
                    criteria_analysis: {
                        phenotypic_criteria: {
                            count: phenotypicCriteria.length,
                            met_criteria: phenotypicCriteria,
                            sufficient: hasPhenotypic
                        },
                        etiologic_criteria: {
                            count: etiologicCriteria.length,
                            met_criteria: etiologicCriteria,
                            sufficient: hasEtiologic
                        },
                        severity_criteria: {
                            count: severeCriteria.length,
                            met_criteria: severeCriteria,
                            indicates_severe: severeCriteria.length > 0
                        }
                    },
                    diagnostic_logic: {
                        step1_phenotypic_sufficient: hasPhenotypic,
                        step1_etiologic_sufficient: hasEtiologic,
                        step1_both_criteria_met: isMalnutrition,
                        step2_severity_assessment: severity
                    }
                },
                form_metadata: {
                    filled_date: new Date().toISOString().split('T')[0],
                    filled_time: new Date().toLocaleTimeString(),
                    calculation_timestamp: new Date().toISOString()
                }
            };
        }

        // 显示GLIM计算结果 - 已禁用，结果仅用于后台分析
        function showGLIMResults(results) {
            // 不再显示结果给用户，结果将在后台保存并用于综合分析
            console.log('GLIM评估结果已计算完成，将用于后台分析:', results);

            // 注释掉原来的显示逻辑
            /*
            const resultSection = document.getElementById('resultSection');
            const diagnosisResult = document.getElementById('diagnosisResult');
            const detailsResult = document.getElementById('detailsResult');

            const diagnosis = results.calculation_results.diagnosis;
            const criteria = results.calculation_results.criteria_analysis;

            // 显示诊断结果
            let resultClass = '';
            if (!diagnosis.is_malnutrition) {
                resultClass = 'diagnosis-negative';
            } else if (diagnosis.result === '中度营养不良') {
                resultClass = 'diagnosis-moderate';
            } else {
                resultClass = 'diagnosis-severe';
            }

            diagnosisResult.innerHTML = `<div class="diagnosis-result ${resultClass}">诊断结果：${diagnosis.result}</div>`;

            // 显示详细分析
            let details = '<h4>GLIM评估详情：</h4>';
            details += `<p><strong>第一步 - 表型标准：</strong>满足 ${criteria.phenotypic_criteria.count} 项`;
            if (criteria.phenotypic_criteria.met_criteria.length > 0) {
                details += ` (${criteria.phenotypic_criteria.met_criteria.join('、')})`;
            }
            details += `</p>`;

            details += `<p><strong>第一步 - 病因标准：</strong>满足 ${criteria.etiologic_criteria.count} 项`;
            if (criteria.etiologic_criteria.met_criteria.length > 0) {
                details += ` (${criteria.etiologic_criteria.met_criteria.join('、')})`;
            }
            details += `</p>`;

            if (diagnosis.is_malnutrition) {
                details += `<p><strong>第二步 - 严重程度标准：</strong>满足 ${criteria.severity_criteria.count} 项`;
                if (criteria.severity_criteria.met_criteria.length > 0) {
                    details += ` (${criteria.severity_criteria.met_criteria.join('、')})`;
                }
                details += `</p>`;
            }

            if (results.form_data.notes) {
                details += `<p><strong>备注：</strong>${results.form_data.notes}</p>`;
            }

            detailsResult.innerHTML = details;
            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: 'smooth' });
            */
        }

        // 表单提交处理函数
        function handleFormSubmit(e) {
            e.preventDefault();
            console.log('🚀 新版本 - GLIM表单提交事件被触发');

            // 计算GLIM评估结果
            const glimResults = calculateGLIMResults();
            console.log('📊 新版本 - GLIM评估结果计算完成:', glimResults);

            // 保存到localStorage供后续使用
            localStorage.setItem('glimData', JSON.stringify(glimResults));

            // 显示提交成功提示
            alert('✅ GLIM评估问卷提交成功！\n\n您的营养状况评估数据已保存，将用于综合分析。');

            // 通知父窗口GLIM数据已提交
            console.log('尝试通知父窗口GLIM数据已提交', glimResults);
            if (window.parent && window.parent.handleGlimSubmission) {
                console.log('找到父窗口的handleGlimSubmission函数，正在调用...');
                window.parent.handleGlimSubmission(glimResults);
            } else {
                console.error('无法找到父窗口的handleGlimSubmission函数');
                // 备用方案：使用postMessage
                console.log('使用postMessage发送数据到父窗口');
                window.parent.postMessage({
                    type: 'glim_submission',
                    data: glimResults
                }, '*');
            }
        }



        // 重置表单
        function resetForm() {
            document.getElementById('glimForm').reset();
            document.getElementById('resultSection').style.display = 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 新版本GLIM表单 - DOMContentLoaded事件触发');
            setupCheckboxes();

            // 注册表单提交事件监听器
            const form = document.getElementById('glimForm');
            console.log('🔍 查找表单元素:', form);
            if (form) {
                form.addEventListener('submit', handleFormSubmit);
                console.log('✅ GLIM表单提交事件监听器已注册 - 新版本');
            } else {
                console.error('❌ 无法找到GLIM表单元素');
            }

            console.log('🎯 GLIM表单已初始化 - 新版本');
        });

        // 备用初始化方案 - 如果DOMContentLoaded没有触发
        window.addEventListener('load', function() {
            console.log('window load事件触发');
            const form = document.getElementById('glimForm');
            if (form && !form.hasAttribute('data-listener-added')) {
                form.addEventListener('submit', handleFormSubmit);
                form.setAttribute('data-listener-added', 'true');
                console.log('通过window load事件注册了GLIM表单提交监听器');
            }
        });
    </script>
</body>
</html>