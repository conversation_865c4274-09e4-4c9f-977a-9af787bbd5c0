<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能营养顾问</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f7f7f8;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* === 主容器布局 - 修改为居中显示 === */
        .main-container {
            flex: 1;
            display: flex;
            padding-top: 64px;
            height: 100vh;
            justify-content: center; /* 居中对齐 */
            max-width: 1400px; /* 设置最大宽度 */
            margin: 0 auto; /* 整体居中 */
            width: 100%;
        }
        
        /* 顶部工具栏 */
        .toolbar {
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .toolbar-title {
            font-size: 16px;
            font-weight: 600;
            color: #202123;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: #f0f0f0;
            border-radius: 12px;
            font-size: 12px;
            color: #6b7280;
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .toolbar-right {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .toolbar-btn {
            padding: 8px 12px;
            background: #fff;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .toolbar-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .toolbar-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 模型状态显示 */
        .model-status-display {
            display: flex;
            gap: 12px;
            align-items: center;
            font-size: 12px;
        }

        .model-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
        }

        .model-label {
            color: #6b7280;
            font-weight: 500;
        }

        .model-status {
            font-weight: 600;
            font-size: 11px;
        }

        .model-status.loaded {
            color: #059669;
        }

        .model-status.not-loaded {
            color: #dc2626;
        }

        .model-status.checking {
            color: #d97706;
        }
        
        /* === 左侧自动模式面板 === */
        .auto-mode-sidebar {
            width: 380px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }
        
        .auto-mode-sidebar.hidden {
            transform: translateX(-100%);
            position: absolute;
            z-index: -1;
        }
        
        /* === 右侧对话区域 === */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: transparent; /* 恢复透明背景，与页面背景一致 */
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .chat-area.full-width {
            margin: 0 auto; /* 手动模式居中 */
            width: 100%;
            max-width: 768px; /* 手动模式限制最大宽度 */
            padding: 0 20px; /* 添加左右padding保证在容器内有间距 */
        }
        
        /* 调试：临时边界显示（可以移除） */
        .chat-area.sidebar-mode {
            margin-left: 0;
            width: calc(100% - 380px);
            height: calc(100vh - 64px); /* 减去toolbar高度 */
            display: flex;
            flex-direction: column;
            padding: 0 20px 0 0; /* 右侧20px留白，左侧不需要因为整体已居中 */
            /* border: 2px solid red; /* 调试用边界，可以移除 */
        }
        
        /* 聊天容器 */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            padding: 0 20px;
            /* 确保容器能够处理长内容 */
            overflow: hidden;
            min-height: 0;
        }
        
        /* 确保手动模式下对话框始终居中，并修复输入框宽度 */
        .chat-area.full-width .chat-container {
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            padding: 0; /* 移除容器内部padding，让外层控制 */
        }
        
        /* 自动模式下的对话框样式 - 修复为居中布局 */
        .chat-area.sidebar-mode .chat-container {
            max-width: 768px;
            margin: 0 auto; /* 居中对齐 */
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 0; /* 移除内部padding，让外层控制间距 */
        }
        
        /* 自动模式下消息区域的特殊样式 */
        .chat-area.sidebar-mode .messages-area {
            padding: 20px 0; /* 上下padding，左右由容器控制 */
        }
        
        /* 确保自动模式下消息区域可见 */
        .chat-area.sidebar-mode .messages-area {
            flex: 1;
            display: block !important;
            overflow-y: auto;
            min-height: 400px;
            visibility: visible !important;
        }
        
        /* 自动模式下保持输入区域可见，允许用户交互 */
        .chat-area.sidebar-mode .input-area {
            display: flex !important;
            position: relative !important;
            bottom: 0 !important;
            width: 100% !important;
            background: transparent !important; /* 确保透明背景 */
            border: none !important; /* 去除边框 */
            padding: 20px 0; /* 上下padding，左右由容器控制 */
        }
        
        /* 自动模式下输入容器的样式 */
        .chat-area.sidebar-mode .input-container {
            max-width: 768px; /* 与聊天容器保持一致 */
            margin: 0 auto;
            width: 100%;
        }
        
        /* 删除重复的CSS规则 */
        
        /* 确保自动模式下消息区域不会占满全部高度，为输入区域留空间 */
        .chat-area.sidebar-mode .chat-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .chat-area.sidebar-mode .messages-area {
            flex: 1;
            overflow-y: auto;
            min-height: 0; /* 允许flex压缩 */
        }
        
        /* 消息区域 */
        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            scroll-behavior: smooth;
            background: transparent; /* 确保背景透明 */
            /* 确保能够显示长内容 */
            max-height: none;
            height: auto;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        /* 手动模式下消息区域的样式 */
        .chat-area.full-width .messages-area {
            padding: 20px; /* 确保手动模式下有足够的内边距 */
            background: transparent;
        }
        
        /* 隐藏 WebKit 浏览器的滚动条 */
        .messages-area::-webkit-scrollbar {
            display: none;
        }
        
        /* 消息样式 */
        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
            align-items: flex-start;
            /* 确保消息容器能够适应内容高度 */
            min-height: auto;
            width: 100%;
        }

        /* 用户消息右对齐 */
        .message.user {
            flex-direction: row-reverse;
            justify-content: flex-start;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .message.assistant .message-avatar {
            background: #19c37d;
            color: white;
        }
        
        .message.user .message-avatar {
            background: #343541;
            color: white;
        }
        
        .message-content {
            flex: 1;
            min-width: 0;
            /* 确保内容容器能够适应任何长度的内容 */
            width: 100%;
            max-width: 100%;
            overflow: visible;
            display: flex;
            flex-direction: column;
        }

        /* 用户消息内容右对齐 */
        .message.user .message-content {
            align-items: flex-end;
        }

        /* AI消息内容左对齐 */
        .message.assistant .message-content {
            align-items: flex-start;
        }
        
        .message-text {
            line-height: 1.6;
            color: #374151;
            white-space: pre-wrap;
            word-wrap: break-word;
            /* 确保长文本能完整显示 */
            max-width: 100%;
            overflow-wrap: break-word;
            /* 移除任何高度限制 */
            max-height: none;
            /* 确保文本不会被截断 */
            text-overflow: clip;
        }
        
        .message.assistant .message-text {
            font-size: 15px;
            background: #f8f9fa; /* AI消息背景色 */
            border: 1px solid #e9ecef; /* 边框 */
            border-radius: 18px; /* 圆角 */
            padding: 12px 16px; /* 内边距 */
            max-width: fit-content; /* 自适应宽度 */
        }
        
        .message.user .message-text {
            font-size: 15px;
            color: #374151;
            background: #f0f9ff; /* 添加背景色 */
            border: 1px solid #e0e7ff; /* 添加边框 */
            border-radius: 18px; /* 圆角 */
            padding: 12px 16px; /* 内边距 */
            text-align: left; /* 用户消息文本左对齐 */
            max-width: fit-content; /* 自适应宽度 */
            margin-left: auto; /* 整个消息框右对齐 */
        }
        
        /* 选项按钮 */
        .message-options {
            margin-top: 16px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* 用户消息中的选项按钮右对齐 */
        .message.user .message-options {
            justify-content: flex-end;
        }
        
        .option-btn {
            padding: 8px 16px;
            background: #fff;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .option-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .option-btn.selected {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }
        
        /* 打字指示器 */
        .typing-indicator {
            margin-bottom: 32px;
            display: none;
        }
        
        .typing-content {
            display: flex;
            gap: 16px;
        }
        
        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 12px 16px;
            background: #f3f4f6;
            border-radius: 20px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #9ca3af;
            animation: typingAnimation 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }
        
        @keyframes typingAnimation {
            0%, 80%, 100% {
                transform: scale(1);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        /* 输入区域 - 悬浮效果设计 */
        .input-area {
            background: transparent; /* 去除背景 */
            border: none; /* 去除边框 */
            padding: 20px;
            flex-shrink: 0; /* 防止被压缩 */
            position: relative;
            z-index: 10; /* 确保在最上层 */
        }
        
        .input-container {
            max-width: 768px;
            margin: 0 auto;
            position: relative;
        }
        
        /* 手动模式下输入区域的样式 */
        .chat-area.full-width .input-area {
            background: transparent; /* 确保透明背景 */
            padding: 20px; /* 确保手动模式下有足够的内边距 */
        }
        
        .chat-area.full-width .input-container {
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
        }
        
        .input-wrapper {
            display: flex;
            align-items: flex-end;
            background: #fff;
            border: 1px solid #d1d5db;
            border-radius: 24px; /* 增加圆角，更加现代 */
            padding: 12px 20px; /* 增加左右padding */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增强阴影效果 */
            transition: all 0.2s ease; /* 添加过渡效果 */
            margin-bottom: 8px; /* 添加底部间距，增强悬浮感 */
            position: relative;
            backdrop-filter: blur(10px); /* 毛玻璃效果 */
        }
        
        /* 悬浮时的附加效果 */
        .input-wrapper:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }
        
        .input-wrapper:focus-within {
            border-color: #10b981;
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.15); /* 聚焦时增强阴影和颜色 */
            transform: translateY(-2px); /* 轻微上浮效果 */
        }
        
        .message-input {
            flex: 1;
            border: none;
            outline: none;
            resize: none;
            font-size: 16px;
            line-height: 1.5;
            color: #374151;
            background: transparent;
            min-height: 20px;
            max-height: 120px;
            padding: 4px 0; /* 增加上下padding使文本更好对齐 */
        }
        
        .message-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }
        
        .send-button {
            width: 36px;
            height: 36px;
            border: none;
            background: #10b981;
            color: white;
            border-radius: 50%; /* 圆形按钮 */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); /* 添加阴影 */
        }
        
        .send-button:hover:not(:disabled) {
            background: #059669;
            transform: scale(1.05); /* 悬浮时轻微放大 */
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }
        
        .send-button:disabled {
            background: #d1d5db;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            animation: modalSlideIn 0.2s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -48%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }
        
        .modal-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #e5e5e5;
            position: relative;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        
        .modal-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .close-modal {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border: none;
            background: #f3f4f6;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s;
        }
        
        .close-modal:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        /* GLIM表单模态框特殊样式 - 去除外层滚动条 */
        #glimModal .modal-body {
            padding: 0;
            max-height: none;
            overflow: hidden;
        }
        
        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .form-input, .form-select {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #374151;
            background: #fff;
            transition: border-color 0.2s;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        /* 文件上传样式 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .upload-area.dragover {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 12px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #9ca3af;
        }
        
        .file-info {
            background: #f0fdf4;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            display: none;
        }
        
        .file-name {
            font-weight: 500;
            color: #065f46;
            margin-bottom: 4px;
        }
        
        .file-size {
            font-size: 14px;
            color: #059669;
        }
        
        /* 按钮样式 */
        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 24px;
            border-top: 1px solid #e5e5e5;
            background: #f9fafb;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #10b981;
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            background: #059669;
        }
        
        .btn-primary:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #fff;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin: 12px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #10b981;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        /* 会话历史弹窗 */
        .session-history-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .session-history-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 800px;
            max-height: 80vh;
            width: 90%;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .session-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e5e5;
        }

        .session-history-title {
            font-size: 18px;
            font-weight: 600;
            color: #202123;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
            border-radius: 4px;
        }

        .close-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .session-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .session-item {
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .session-item:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .session-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .session-item-title {
            font-weight: 600;
            color: #202123;
        }

        .session-item-date {
            font-size: 12px;
            color: #6b7280;
        }

        .session-item-preview {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }

        .session-item-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .session-action-btn {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .session-action-btn:hover {
            background: #f3f4f6;
        }

        .session-action-btn.delete {
            color: #dc2626;
            border-color: #fca5a5;
        }

        .session-action-btn.delete:hover {
            background: #fef2f2;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-container {
                max-width: 1200px; /* 在中等屏幕上适当减少最大宽度 */
            }
            
            .auto-mode-sidebar {
                width: 320px;
            }
            
            .chat-area.sidebar-mode {
                width: calc(100% - 320px);
                padding: 0 15px 0 0; /* 在小屏幕上减少右侧padding */
            }
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                max-width: 100%; /* 手机端占满全宽 */
                padding: 0 10px; /* 手机端添加一些边距 */
            }
            
            .auto-mode-sidebar {
                width: 100%;
                height: 50vh;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
                position: relative;
                transform: none;
            }
            
            .auto-mode-sidebar.hidden {
                transform: translateY(-100%);
                height: 0;
                position: absolute;
                z-index: -1;
            }
            
            .chat-area,
            .chat-area.full-width,
            .chat-area.sidebar-mode {
                width: 100% !important;
                margin-left: 0 !important;
                padding: 0 10px; /* 移动端减少padding */
            }
            
            .chat-area.sidebar-mode .chat-container {
                max-width: 768px;
                margin: 0 auto;
            }
            
            .toolbar {
                padding: 8px 16px;
                flex-wrap: wrap;
                gap: 8px;
            }

            .toolbar-title {
                font-size: 14px;
            }

            .toolbar-right {
                gap: 6px;
            }

            .toolbar-btn {
                padding: 6px 10px;
                font-size: 13px;
            }

            .session-history-content {
                padding: 16px;
                margin: 20px;
            }
        }

        @media (max-width: 480px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .toolbar-left, .toolbar-right {
                justify-content: center;
            }

            .toolbar-btn {
                flex: 1;
                text-align: center;
            }
            
            .messages-area {
                padding: 16px;
            }
            
            .input-area {
                padding: 16px;
                background: transparent; /* 移动端也保持透明背景 */
            }
            
            /* 移动端输入框优化 */
            .input-wrapper {
                border-radius: 20px; /* 移动端稍小的圆角 */
                padding: 10px 16px; /* 移动端减少padding */
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95vw;
                max-width: none;
            }
        }
        
        /* 超宽屏幕适配 */
        @media (min-width: 1440px) {
            .main-container {
                max-width: 1400px; /* 超宽屏幕限制最大宽度 */
            }
            
            .chat-area.sidebar-mode {
                padding: 0 40px 0 0; /* 超宽屏幕增加右侧留白 */
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 重新填写按钮样式通过JavaScript动态设置，与.option-btn保持一致 */
        
        /* === 左侧面板内容样式 === */
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .sidebar-subtitle {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .auto-mode-panel {
            background: #f8fafc;
            border-radius: 16px;
            padding: 24px;
            margin: 16px;
            border: 2px solid #e2e8f0;
        }
        
        .auto-mode-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .auto-mode-header h3 {
            color: #1f2937;
            margin: 0 0 8px 0;
            font-size: 24px;
        }
        
        .auto-mode-header p {
            color: #6b7280;
            margin: 0;
            font-size: 16px;
        }
        
        .batch-upload-container {
            margin-bottom: 24px;
        }
        
        .upload-zone {
            border: 3px dashed #cbd5e1;
            border-radius: 12px;
            padding: 40px 24px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .upload-zone:hover, .upload-zone.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .upload-text h4 {
            color: #1f2937;
            margin: 0 0 8px 0;
            font-size: 18px;
        }
        
        .upload-text p {
            color: #6b7280;
            margin: 0 0 16px 0;
            font-size: 14px;
        }
        
        .file-types-info {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .file-type-tag {
            background: #e5e7eb;
            color: #374151;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .browse-files-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .browse-files-btn:hover {
            background: #2563eb;
        }
        
        .selected-files-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 16px;
            border: 1px solid #e5e7eb;
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .panel-header h4 {
            margin: 0;
            color: #1f2937;
        }
        
        .clear-files-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .file-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-icon {
            font-size: 20px;
        }
        
        .file-info {
            flex: 1;
            min-width: 0;
        }
        
        .file-name {
            font-weight: 600;
            font-size: 14px;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .file-size {
            font-size: 12px;
            color: #6b7280;
        }
        
        .remove-file-btn {
            background: #ef4444;
            color: white;
            border: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .upload-summary {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #1e40af;
        }
        
        .start-analysis-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .start-analysis-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        
        .analysis-progress-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e5e7eb;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .progress-header h4 {
            margin: 0;
            color: #1f2937;
        }
        
        .overall-progress {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .progress-bar-container {
            width: 200px;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        
        .progress-text {
            font-weight: 600;
            color: #059669;
            font-size: 16px;
        }
        
        .stages-progress {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stage-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .stage-item.active {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }
        
        .stage-item.completed {
            border-left-color: #10b981;
            background: #ecfdf5;
        }
        
        .stage-item.error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        
        .stage-icon {
            font-size: 24px;
            width: 40px;
            text-align: center;
        }
        
        .stage-info {
            flex: 1;
        }
        
        .stage-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        
        .stage-detail {
            font-size: 14px;
            color: #6b7280;
        }
        
        .stage-status {
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .stage-status.running {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .stage-status.completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .stage-status.error {
            background: #fee2e2;
            color: #b91c1c;
        }
        
        .modules-detail {
            border-top: 1px solid #e5e7eb;
            padding-top: 16px;
        }
        
        .modules-detail h5 {
            margin: 0 0 12px 0;
            color: #1f2937;
            font-size: 16px;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }
        
        .module-status-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
        }
        
        .module-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .module-status {
            font-size: 12px;
            color: #6b7280;
        }
        
        @media (max-width: 768px) {
            .auto-mode-panel {
                margin: 8px;
                padding: 16px;
            }
            
            .files-grid {
                grid-template-columns: 1fr;
            }
            
            .stages-progress {
                margin-bottom: 16px;
            }
            
            .stage-item {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }
        }
        
        /* 滚动条样式 */
        .messages-area::-webkit-scrollbar {
            width: 6px;
        }
        
        .messages-area::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .messages-area::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }
        
        .messages-area::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
    </style>
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="toolbar">
        <div class="toolbar-left">
            <div class="toolbar-title">🌟 智能营养顾问</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="statusText">准备中</span>
            </div>
        </div>
        <div class="toolbar-right">
            <button class="toolbar-btn" id="modeToggleBtn" onclick="toggleMode()">
                <span id="modeToggleText">🤖 切换到自动模式</span>
            </button>
            <button class="toolbar-btn" onclick="createNewSession()">
                ➕ 新建会话
            </button>
            <button class="toolbar-btn" onclick="openSessionHistory()">
                📚 历史会话
            </button>
            <button class="toolbar-btn" onclick="openEnhancedLogViewer()">
                📋 查看日志
            </button>
            <button class="toolbar-btn" onclick="exportChatHistory()">
                📥 导出聊天记录
            </button>
            <div class="model-status-display">
                <div class="model-item" id="visionModelStatus">
                    <span class="model-label">视觉模型:</span>
                    <span class="model-status" id="visionStatus">检查中...</span>
                </div>
                <div class="model-item" id="huatuoModelStatus">
                    <span class="model-label">分析模型:</span>
                    <span class="model-status" id="huatuoStatus">检查中...</span>
                </div>
            </div>
            <button class="toolbar-btn" onclick="clearConversation()">
                🗑️ 清空对话
            </button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧自动模式面板 -->
        <div class="auto-mode-sidebar hidden" id="autoModeSidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">🤖 AI智能分析</div>
                <div class="sidebar-subtitle">上传多种营养相关数据，AI将自动识别类型并进行专业综合分析</div>
            </div>
            
            <div class="sidebar-content">
                <!-- 批量上传区域 -->
                <div class="batch-upload-container">
                    <div class="upload-zone" id="batchDropZone">
                        <div class="upload-icon">📂</div>
                        <div class="upload-text">
                            <h4>拖拽多个文件到此处 或 点击选择文件</h4>
                            <p>支持：面部图片、BIA数据表格、GLIM表单、病历文档</p>
                            <div class="file-types-info">
                                <span class="file-type-tag">📷 JPG/PNG</span>
                                <span class="file-type-tag">📊 Excel/CSV</span>
                                <span class="file-type-tag">📋 JSON</span>
                                <span class="file-type-tag">📄 PDF/Word</span>
                            </div>
                        </div>
                        <button class="browse-files-btn" onclick="document.getElementById('batchFileInput').click()">
                            选择文件
                        </button>
                    </div>
                    
                    <input type="file" id="batchFileInput" multiple accept=".jpg,.jpeg,.png,.xlsx,.xls,.csv,.json,.pdf,.doc,.docx,.txt" style="display: none;">
                    
                    <!-- 文件列表显示 -->
                    <div class="selected-files-panel" id="selectedFilesPanel" style="display: none;">
                        <div class="panel-header">
                            <h4>📋 已选择的文件</h4>
                            <button class="clear-files-btn" onclick="clearSelectedFiles()">清空</button>
                        </div>
                        <div class="files-grid" id="selectedFilesGrid"></div>
                        <div class="upload-summary" id="uploadSummary"></div>
                        <button class="start-analysis-btn" id="startAnalysisBtn" onclick="startBatchAnalysis()">
                            🚀 开始智能分析
                        </button>
                    </div>
                </div>
                
                <!-- 分析进度面板 -->
                <div class="analysis-progress-panel" id="analysisProgressPanel" style="display: none;">
                    <div class="progress-header">
                        <h4>🔬 智能分析进度</h4>
                        <div class="overall-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill" id="overallProgressBar"></div>
                            </div>
                            <span class="progress-text" id="overallProgressText">0%</span>
                        </div>
                    </div>
                    
                    <div class="stages-progress">
                        <div class="stage-item" id="ingestionStage">
                            <div class="stage-icon">🔍</div>
                            <div class="stage-info">
                                <div class="stage-title">数据识别</div>
                                <div class="stage-detail">智能识别文件类型和内容...</div>
                            </div>
                            <div class="stage-status" id="ingestionStatus">等待中</div>
                        </div>
                        
                        <div class="stage-item" id="analysisStage">
                            <div class="stage-icon">⚡</div>
                            <div class="stage-info">
                                <div class="stage-title">智能分析</div>
                                <div class="stage-detail">执行视觉分析、体成分分析等...</div>
                            </div>
                            <div class="stage-status" id="analysisStatus">等待中</div>
                        </div>
                        
                        <div class="stage-item" id="comprehensiveStage">
                            <div class="stage-icon">🧠</div>
                            <div class="stage-info">
                                <div class="stage-title">综合分析</div>
                                <div class="stage-detail">HuatuoGPT整合生成报告...</div>
                            </div>
                            <div class="stage-status" id="comprehensiveStatus">等待中</div>
                        </div>
                    </div>
                    
                    <!-- 模块详细状态 -->
                    <div class="modules-detail" id="modulesDetail" style="display: none;">
                        <h5>📊 分析模块状态</h5>
                        <div class="modules-grid" id="modulesGrid"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧对话区域 -->
        <div class="chat-area full-width" id="chatArea">
            <div class="chat-container">
                <!-- 消息区域 -->
                <div class="messages-area" id="messagesArea">
                    <div class="message assistant">
                        <div class="message-avatar">AI</div>
                        <div class="message-content">
                            <div class="message-text">正在初始化智能营养顾问...</div>
                        </div>
                    </div>
                </div>

                <!-- 打字指示器 -->
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-content">
                        <div class="message-avatar" style="background: #19c37d; color: white;">AI</div>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="input-area" id="inputArea">
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea class="message-input" 
                                     id="messageInput" 
                                     placeholder="输入您的回复..."
                                     rows="1"></textarea>
                            <button class="send-button" id="sendButton" onclick="sendMessage()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户档案模态框 -->
    <div class="modal" id="profileModal">
        <div class="modal-content" style="width: 500px;">
            <div class="modal-header">
                <h2 class="modal-title">基本信息</h2>
                <p class="modal-subtitle">请填写您的基本信息，帮助我们提供更准确的评估</p>
                <button class="close-modal" onclick="closeModal('profileModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-input" id="userName" placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">年龄</label>
                        <input type="number" class="form-input" id="userAge" placeholder="请输入年龄" min="1" max="120">
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别</label>
                        <select class="form-select" id="userGender">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">身高 (cm) <span style="color: #6b7280; font-size: 12px;">（选填）</span></label>
                        <input type="number" class="form-input" id="userHeight" placeholder="如：170" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">当前体重 (kg) <span style="color: #6b7280; font-size: 12px;">（选填）</span></label>
                        <input type="number" class="form-input" id="userWeight" placeholder="如：65" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">平时体重 (kg) <span style="color: #6b7280; font-size: 12px;">（选填）</span></label>
                        <input type="number" class="form-input" id="userUsualWeight" placeholder="如：70" step="0.1">
                    </div>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeModal('profileModal')">取消</button>
                <button class="btn btn-primary" onclick="submitProfile()">确定</button>
            </div>
        </div>
    </div>

    <!-- 文件上传模态框 -->
    <div class="modal" id="uploadModal">
        <div class="modal-content" style="width: 500px;">
            <div class="modal-header">
                <h2 class="modal-title" id="uploadTitle">文件上传</h2>
                <p class="modal-subtitle" id="uploadSubtitle">请选择要上传的文件</p>
                <button class="close-modal" onclick="closeModal('uploadModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon" id="uploadIcon">📁</div>
                    <div class="upload-text" id="uploadText">点击选择文件或拖拽文件到此处</div>
                    <div class="upload-hint" id="uploadHint">支持多种文件格式</div>
                </div>
                <input type="file" id="fileInput" style="display: none;">
                <div class="file-info" id="fileInfo">
                    <div class="file-name" id="fileName"></div>
                    <div class="file-size" id="fileSize"></div>
                </div>
                <div class="progress-bar hidden" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeModal('uploadModal')">取消</button>
                <button class="btn btn-primary" id="uploadButton" onclick="submitUpload()" disabled>上传</button>
            </div>
        </div>
    </div>

    <!-- GLIM表单模态框 -->
    <div class="modal" id="glimModal">
        <div class="modal-content" style="width: 90%; max-width: 1000px;">
            <div class="modal-header">
                <h2 class="modal-title">GLIM营养不良诊断标准评估</h2>
                <p class="modal-subtitle">请根据您的实际情况填写评估表</p>
                <button class="close-modal" onclick="closeModal('glimModal')">&times;</button>
            </div>
            <div class="modal-body" style="padding: 0;">
                <iframe src="/templates/glim_form.html" style="width: 100%; height: 600px; border: none;"></iframe>
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeModal('glimModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 会话历史弹窗 -->
    <div id="sessionHistoryModal" class="session-history-modal">
        <div class="session-history-content">
            <div class="session-history-header">
                <div class="session-history-title">📚 历史会话</div>
                <button class="close-btn" onclick="closeSessionHistory()">&times;</button>
            </div>
            <div id="sessionList" class="session-list">
                <!-- 会话列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let conversationState = {
            messages: [],
            userProfile: {},
            currentTask: 'initializing',
            selectedFile: null,
            currentUploadType: null,
            sessionId: generateSessionId(),
            sessionMode: 'manual' // 默认为手动模式
        };

        // 生成会话ID
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 带超时的fetch包装函数 - 40分钟超时适应本地大模型推理
        async function fetchWithTimeout(url, options = {}, timeoutMs = 2400000) { // 40分钟超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

            try {
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return response;
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    throw new Error('请求超时（40分钟），本地大模型推理时间异常，请检查模型状态或稍后重试');
                }
                throw error;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 确保初始为手动模式居中布局
            const chatArea = document.getElementById('chatArea');
            const sidebar = document.getElementById('autoModeSidebar');
            chatArea.classList.add('full-width');
            sidebar.classList.add('hidden');
            
            initializeChat();
            setupFileUpload();
            setupAutoResize();
            logSystemEvent('系统初始化', '对话式营养筛查智能体启动');

            // 启动模型状态管理
            startModelStatusStream();
            
            // 添加调试函数
            window.checkInputAreaVisibility = function() {
                const inputArea = document.getElementById('inputArea');
                const chatArea = document.getElementById('chatArea');
                console.log('输入区域元素:', inputArea);
                console.log('输入区域display样式:', getComputedStyle(inputArea).display);
                console.log('输入区域visibility样式:', getComputedStyle(inputArea).visibility);
                console.log('聊天区域类名:', chatArea.className);
                console.log('自动模式状态:', isAutoMode);
            };
        });

        // 初始化聊天 - 调用AI主脑
        async function initializeChat() {
            updateStatus('初始化中');
            logSystemEvent('初始化', '开始初始化智能营养顾问');
            
            // 清空初始消息
            document.getElementById('messagesArea').innerHTML = '';
            
            try {
                // 调用对话API开始会话
                const response = await fetchWithTimeout('http://localhost:5000/api/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: '', // 空消息表示开始会话
                        session_id: conversationState.sessionId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success && result.messages && result.messages.length > 0) {
                        const aiMessage = result.messages[0];
                        
                        // 处理前端动作
                        if (result.frontend_actions && result.frontend_actions.length > 0) {
                            handleFrontendAction(result.frontend_actions[0]);
                        }
                        
                        addMessage('assistant', aiMessage.content, { showProfileForm: true });
                        updateStatus('等待用户信息');
                        logSystemEvent('问候', 'AI主脑发送问候消息');
                        return;
                    }
                }
            } catch (error) {
                logSystemEvent('初始化失败', error.message);
            }
            
            // 降级处理 - 使用默认消息
            setTimeout(() => {
                addMessage('assistant', `🌟 您好！我是您的专业营养顾问AI助手。

很高兴为您提供营养状况的初步筛查服务。我将通过友好的对话，引导您提供一些基本信息和可选的检测数据，然后为您生成个性化的营养评估报告。

⚠️ 重要提醒：我提供的是基于AI的初步筛查建议，不能替代专业医生的诊断。建议您将评估结果作为参考，并咨询专业的营养科医生。

首先，让我了解一下您的基本信息。`, {
                    showProfileForm: true
                });
                updateStatus('等待用户信息');
                logSystemEvent('问候', '使用降级问候消息');
            }, 1000);
        }

        // 添加消息
        function addMessage(role, text, actions = {}) {
            const messagesArea = document.getElementById('messagesArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'assistant' ? 'AI' : 'You';
            if (role === 'assistant') {
                avatar.style.background = '#19c37d';
                avatar.style.color = 'white';
            } else {
                avatar.style.background = '#343541';
                avatar.style.color = 'white';
            }

            const content = document.createElement('div');
            content.className = 'message-content';

            const textDiv = document.createElement('div');
            textDiv.className = 'message-text';
            // 使用innerHTML来支持换行符和格式化文本
            textDiv.innerHTML = text.replace(/\n/g, '<br>');

            content.appendChild(textDiv);

            // 添加选项按钮
            if (actions.options && actions.options.length > 0) {
                const optionsDiv = document.createElement('div');
                optionsDiv.className = 'message-options';

                actions.options.forEach((option, index) => {
                    // 在自动模式下隐藏特定按钮，但保留"重新填写基本信息"
                    if (isAutoMode) {
                        const buttonsToHideInAutoMode = ['GLIM评估问卷', '面部照片', 'BIA数据', '完成收集'];
                        if (buttonsToHideInAutoMode.includes(option)) {
                            return; // 跳过这些按钮的创建
                        }
                    }
                    
                    const button = document.createElement('button');
                    button.className = 'option-btn';
                    button.textContent = `${index + 1}. ${option}`;
                    button.onclick = () => selectOption(option, button);
                    optionsDiv.appendChild(button);
                });

                // 只有当optionsDiv中有按钮时才添加到页面
                if (optionsDiv.children.length > 0) {
                    content.appendChild(optionsDiv);
                }
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);
            messagesArea.appendChild(messageDiv);

            // 滚动到底部
            messagesArea.scrollTop = messagesArea.scrollHeight;

            // 记录消息
            conversationState.messages.push({
                role: role,
                content: text,
                timestamp: new Date().toISOString(),
                actions: actions
            });

            // 处理特殊动作
            if (actions.showProfileForm) {
                setTimeout(() => showModal('profileModal'), 500);
            }
            if (actions.showGlimForm) {
                setTimeout(() => showModal('glimModal'), 500);
            }
            if (actions.showUpload) {
                setupUploadModal(actions.uploadType);
                setTimeout(() => showModal('uploadModal'), 500);
            }
            if (actions.showRetryProfileButton) {
                // 添加重新填写基本信息按钮
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'retry-button-container';
                buttonContainer.style.marginTop = '15px';

                const retryButton = document.createElement('button');
                retryButton.className = 'retry-profile-btn';
                retryButton.innerHTML = '📝 重新填写基本信息';
                retryButton.onclick = retryProfileForm;

                // 设置按钮样式（与其他按钮保持一致）
                Object.assign(retryButton.style, {
                    padding: '8px 16px',
                    background: '#fff',
                    border: '1px solid #d1d5db',
                    borderRadius: '20px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    color: '#374151',
                    transition: 'all 0.2s',
                    whiteSpace: 'nowrap'
                });

                // 添加悬停效果
                retryButton.addEventListener('mouseenter', function() {
                    this.style.background = '#f3f4f6';
                    this.style.borderColor = '#9ca3af';
                });

                retryButton.addEventListener('mouseleave', function() {
                    this.style.background = '#fff';
                    this.style.borderColor = '#d1d5db';
                });

                buttonContainer.appendChild(retryButton);
                content.appendChild(buttonContainer);
            }

            // 记录日志
            logMessage(role, text, actions);
        }

        // 选择选项
        function selectOption(option, buttonElement) {
            // 高亮选中的按钮
            const allButtons = buttonElement.parentElement.querySelectorAll('.option-btn');
            allButtons.forEach(btn => btn.classList.remove('selected'));
            buttonElement.classList.add('selected');

            // 发送用户消息
            setTimeout(() => {
                addMessage('user', option);
                processUserChoice(option);
            }, 300);
        }

        // 处理用户选择 - 统一调用对话API
        async function processUserChoice(choice, additionalData = null) {
            showTyping();
            updateStatus('AI主脑思考中');

            logSystemEvent('用户选择', choice);

            try {
                // 构建请求体
                const requestBody = {
                    message: choice,
                    session_id: conversationState.sessionId
                };

                // 如果有额外数据，添加到请求中
                if (additionalData) {
                    requestBody.additional_data = additionalData;
                }

                // 调用统一对话API
                const response = await fetchWithTimeout('http://localhost:5000/api/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                
                hideTyping();
                
                if (result.success) {
                    // 处理AI回复
                    if (result.messages && result.messages.length > 0) {
                        const aiMessage = result.messages[0];
                        
                        // 根据前端动作决定是否显示选项或特殊UI
                        const actions = {};
                        if (result.frontend_actions && result.frontend_actions.length > 0) {
                            const frontendAction = result.frontend_actions[0];
                            handleFrontendAction(frontendAction);
                        }
                        
                        // 如果是数据收集阶段，根据已收集数据动态显示选项
                        // 检查是否没有需要执行的前端动作（none类型或空数组都算作无动作）
                        const hasNoAction = !result.frontend_actions.length ||
                                          (result.frontend_actions.length === 1 && result.frontend_actions[0].type === 'none');

                        if (result.current_phase === 'data_collection' && hasNoAction) {
                            const availableOptions = [];
                            const collectedData = conversationState.collectedData || {};

                            console.log('🔍 检查已收集数据状态:', collectedData);

                            // 只显示未完成的评估选项
                            if (!collectedData.glim_results) {
                                availableOptions.push('GLIM评估问卷');
                                console.log('➕ 添加GLIM选项（未完成）');
                            } else {
                                console.log('✅ GLIM已完成，跳过');
                            }
                            if (!collectedData.facial_analysis) {
                                availableOptions.push('面部照片');
                            }
                            if (!collectedData.bia_analysis) {
                                availableOptions.push('BIA数据');
                            }

                            // 总是显示完成收集选项
                            availableOptions.push('完成收集');

                            console.log('📋 最终选项列表:', availableOptions);
                            actions.options = availableOptions;
                        }
                        
                        addMessage('assistant', aiMessage.content, actions);
                    }
                    
                    // 更新状态显示
                    updateStatus(getPhaseDisplayName(result.current_phase));
                    
                    // 检查是否完成
                    if (result.is_completed) {
                        updateStatus('评估完成');
                    }
                    
                    logAIResponse('AI主脑', choice, result.messages[0]?.content || '无响应');
                } else {
                    addMessage('assistant', `❌ 处理出现问题：${result.error}`);
                    updateStatus('处理失败');
                }
                
            } catch (error) {
                hideTyping();
                addMessage('assistant', `❌ 系统错误：${error.message}\n请稍后重试。`);
                updateStatus('系统错误');
                logSystemEvent('系统错误', error.message);
            }
        }

        // 处理GLIM选择
        function handleGlimChoice() {
            addMessage('assistant', '好的！让我们来填写GLIM营养不良诊断标准问卷。请根据您的实际情况，诚实填写每个问题。这份问卷将帮助我们评估您是否存在营养不良的风险。', {
                showGlimForm: true
            });
            updateStatus('等待GLIM表单');
            logSystemEvent('GLIM选择', '用户选择填写GLIM问卷');
        }

        // 处理GLIM表单提交
        function handleGlimSubmission(glimResults) {
            console.log('主窗口收到GLIM提交数据:', glimResults);

            // 先保存GLIM数据到状态
            if (!conversationState.collectedData) {
                conversationState.collectedData = {};
            }
            conversationState.collectedData.glim_results = glimResults;

            // 关闭GLIM模态框（此时数据已保存，不会触发取消逻辑）
            closeModal('glimModal', true); // 标记为成功提交

            // 显示提交成功消息
            addMessage('user', '✅ GLIM评估问卷已提交');

            // 发送完成消息到后端，携带GLIM数据
            processUserChoice('已完成GLIM评估问卷', {
                type: 'glim_completion',
                glim_results: glimResults
            });

            logSystemEvent('GLIM提交', 'GLIM问卷数据已提交', glimResults);
        }

        // 监听来自iframe的postMessage
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'glim_submission') {
                console.log('通过postMessage收到GLIM提交数据:', event.data.data);
                handleGlimSubmission(event.data.data);
            }
        });



        // 处理照片选择
        function handlePhotoChoice() {
            addMessage('assistant', '好的！请上传一张您近期拍摄的清晰正面照片。请确保：\n• 光线充足，面部清晰可见\n• 正面角度，无遮挡\n• 表情自然\n\n我会通过专业的视觉分析技术来评估面部营养相关特征。', {
                showUpload: true,
                uploadType: 'photo'
            });
            updateStatus('等待照片上传');
            logSystemEvent('照片选择', '用户选择上传面部照片');
        }

        // 处理BIA选择
        function handleBiaChoice() {
            addMessage('assistant', '好的！请上传您的生物电阻抗分析(BIA)数据文件。这可以是：\n• 体脂秤或BIA设备的数据导出文件\n• Excel格式的测量数据\n• 体检报告中的BIA相关数据\n\n这些数据将帮助我们分析您的体成分状况。', {
                showUpload: true,
                uploadType: 'bia'
            });
            updateStatus('等待BIA数据');
            logSystemEvent('BIA选择', '用户选择上传BIA数据');
        }

        // 提供数据选项
        function offerDataOptions() {
            const availableOptions = ['GLIM评估问卷', '面部照片', 'BIA数据', '完成收集'];
            
            addMessage('assistant', '为了更准确地评估您的营养状况，您可以选择提供以下信息。您可以选择其中任意几项，也可以跳过某些项目：', {
                options: availableOptions
            });
            
            updateStatus('等待数据选择');
            logSystemEvent('提供选项', '向用户提供数据收集选项');
        }

        // 提交用户档案 - 调用新API
        async function submitProfile() {
            const profile = {
                name: document.getElementById('userName').value.trim(),
                age: parseInt(document.getElementById('userAge').value) || 0,
                gender: document.getElementById('userGender').value,
                height: parseFloat(document.getElementById('userHeight').value) || 0,
                current_weight: parseFloat(document.getElementById('userWeight').value) || 0,
                usual_weight: parseFloat(document.getElementById('userUsualWeight').value) || 0
            };

            // 验证必填项（姓名、年龄、性别为必填，身高体重为选填）
            if (!profile.name || !profile.age || !profile.gender) {
                alert('请填写姓名、年龄和性别等基本信息');
                return;
            }

            conversationState.userProfile = profile;
            closeModal('profileModal', true); // 标记为成功提交

            // 构建用户档案消息，只显示已填写的信息
            let profileMessage = `✅ 基本信息已提交：\n姓名：${profile.name}\n年龄：${profile.age}岁\n性别：${profile.gender}`;
            
            // 如果填写了身高体重，则显示
            if (profile.height > 0) {
                profileMessage += `\n身高：${profile.height}cm`;
            }
            if (profile.current_weight > 0) {
                profileMessage += `\n当前体重：${profile.current_weight}kg`;
            }
            if (profile.usual_weight > 0) {
                profileMessage += `\n平时体重：${profile.usual_weight}kg`;
            }
            
            addMessage('user', profileMessage);
            
            logSystemEvent('用户档案', '用户提交基本信息', profile);

            try {
                showTyping();
                updateStatus('处理档案信息');

                const response = await fetchWithTimeout('http://localhost:5000/api/submit-profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        profile: profile,
                        session_id: conversationState.sessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                
                hideTyping();
                
                if (result.success && result.messages && result.messages.length > 0) {
                    const aiMessage = result.messages[0];
                    
                    // 根据前端动作处理UI
                    if (result.frontend_actions && result.frontend_actions.length > 0) {
                        handleFrontendAction(result.frontend_actions[0]);
                    }
                    
                    // 如果没有特殊动作或动作类型为none，显示数据收集选项
                    const hasNoAction = result.frontend_actions.length === 0 ||
                                      (result.frontend_actions.length === 1 && result.frontend_actions[0].type === 'none');

                    let actions = {};
                    if (hasNoAction && result.current_phase === 'data_collection') {
                        // 根据已收集数据动态生成选项
                        const availableOptions = [];
                        const collectedData = conversationState.collectedData || {};

                        if (!collectedData.glim_results) {
                            availableOptions.push('GLIM评估问卷');
                        }
                        if (!collectedData.facial_analysis) {
                            availableOptions.push('面部照片');
                        }
                        if (!collectedData.bia_analysis) {
                            availableOptions.push('BIA数据');
                        }

                        availableOptions.push('完成收集');
                        actions = { options: availableOptions };
                    }
                    
                    addMessage('assistant', aiMessage.content, actions);
                    updateStatus('等待数据选择');
                } else {
                    addMessage('assistant', `❌ 档案处理失败：${result.error || '未知错误'}`);
                    updateStatus('处理失败');
                }
                
            } catch (error) {
                hideTyping();
                addMessage('assistant', `❌ 档案提交出错：${error.message}`);
                updateStatus('提交失败');
                logSystemEvent('档案提交失败', error.message);
            }
        }

        // 文件上传设置
        function setupUploadModal(uploadType) {
            conversationState.currentUploadType = uploadType;
            
            const titles = {
                photo: '上传面部照片',
                bia: '上传BIA数据'
            };
            
            const icons = {
                photo: '📷',
                bia: '📊'
            };
            
            const hints = {
                photo: '支持 JPG, PNG, WEBP 格式',
                bia: '支持 Excel, CSV 格式'
            };
            
            const accepts = {
                photo: 'image/*',
                bia: '.xlsx,.xls,.csv'
            };

            document.getElementById('uploadTitle').textContent = titles[uploadType] || '文件上传';
            document.getElementById('uploadIcon').textContent = icons[uploadType] || '📁';
            document.getElementById('uploadHint').textContent = hints[uploadType] || '请选择文件';
            document.getElementById('fileInput').accept = accepts[uploadType] || '*';
        }

        // 设置文件上传
        function setupFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const fileInfo = document.getElementById('fileInfo');
            const uploadButton = document.getElementById('uploadButton');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    conversationState.selectedFile = file;
                    document.getElementById('fileName').textContent = file.name;
                    document.getElementById('fileSize').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
                    fileInfo.style.display = 'block';
                    uploadButton.disabled = false;
                    logSystemEvent('文件选择', `用户选择文件: ${file.name}`);
                }
            });

            // 拖拽功能
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => uploadArea.classList.add('dragover'), false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('dragover'), false);
            });

            uploadArea.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    fileInput.dispatchEvent(new Event('change'));
                }
            });
        }

        // 提交上传 - 使用新API
        async function submitUpload() {
            if (!conversationState.selectedFile) {
                alert('请先选择文件');
                return;
            }

            const file = conversationState.selectedFile;
            const uploadType = conversationState.currentUploadType;

            // 设置上传标志，防止关闭模态框时触发取消逻辑
            conversationState.isUploading = true;

            closeModal('uploadModal', true); // 标记为成功提交

            addMessage('user', `📎 已上传${uploadType === 'photo' ? '照片' : 'BIA数据文件'}：${file.name}`);

            // 显示处理中状态
            showTyping();
            updateStatus('AI分析中');

            logSystemEvent('文件上传', `开始处理${uploadType}文件: ${file.name}`);

            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('session_id', conversationState.sessionId);

                // 根据类型选择不同的API端点
                const endpoint = uploadType === 'photo' ? 'upload-image' : 'upload-bia';
                
                const response = await fetchWithTimeout(`http://localhost:5000/api/${endpoint}`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                
                hideTyping();

                if (result.success) {
                    // 清除上传标志
                    conversationState.isUploading = false;

                    // 更新已收集数据状态
                    if (!conversationState.collectedData) {
                        conversationState.collectedData = {};
                    }

                    if (uploadType === 'bia') {
                        conversationState.collectedData.bia_analysis = result.detailed_data || true;
                        logSystemEvent('BIA完成', 'BIA数据分析完成', result.detailed_data);
                    } else if (uploadType === 'photo') {
                        conversationState.collectedData.facial_analysis = result.detailed_data || true;
                        logSystemEvent('面部分析完成', '面部照片分析完成', result.detailed_data);
                    }

                    const analysisText = `✅ ${uploadType === 'photo' ? '面部照片' : 'BIA数据'}分析完成！\n\n${result.analysis}`;

                    // 根据已收集数据动态生成选项
                    const availableOptions = [];
                    const collectedData = conversationState.collectedData || {};

                    console.log('🔍 上传完成后检查已收集数据状态:', collectedData);

                    if (!collectedData.glim_results) {
                        availableOptions.push('GLIM评估问卷');
                    }
                    if (!collectedData.facial_analysis) {
                        availableOptions.push('面部照片');
                    }
                    if (!collectedData.bia_analysis) {
                        availableOptions.push('BIA数据');
                    }

                    availableOptions.push('完成收集');

                    console.log('📋 上传完成后最终选项列表:', availableOptions);

                    addMessage('assistant', analysisText, {
                        options: availableOptions
                    });

                    // 发送完成消息到后端，让后端也知道数据已完成
                    if (uploadType === 'bia') {
                        processUserChoice('📊 已上传BIA数据：xlsx', {
                            type: 'bia_completion',
                            bia_results: result.detailed_data
                        });
                    } else if (uploadType === 'photo') {
                        processUserChoice('📷 已上传面部照片：jpg', {
                            type: 'photo_completion',
                            photo_results: result.detailed_data
                        });
                    }

                    logAIResponse(`${uploadType}分析`, `分析${file.name}`, result.analysis);
                } else {
                    // 清除上传标志
                    conversationState.isUploading = false;

                    addMessage('assistant', `❌ ${uploadType === 'photo' ? '图片' : '数据'}分析失败：${result.error}\n\n请重新上传或选择其他选项。`, {
                        options: ['重新上传', '选择其他选项']
                    });
                }

                updateStatus('等待用户选择');

            } catch (error) {
                // 清除上传标志
                conversationState.isUploading = false;

                hideTyping();
                addMessage('assistant', `❌ 处理过程中出现错误：${error.message}\n\n请重试或联系技术支持。`);
                updateStatus('分析出错');
                logSystemEvent('分析错误', error.message, { uploadType, fileName: file.name });
            }

            // 清理
            conversationState.selectedFile = null;
            document.getElementById('uploadButton').disabled = true;
            document.getElementById('fileInfo').style.display = 'none';
        }

        // 真实调用视觉分析模型
        async function analyzePhotoWithAI(imageFile) {
            const prompt = `请分析这张面部照片，评估营养相关的面部特征。重点关注：
1. 颞部是否有凹陷
2. 面颊是否消瘦
3. 颧骨是否突出
4. 眼窝是否深陷
5. 整体面容是否有营养不良的体征

请提供详细的分析结果和营养状况评估。`;

            try {
                // 将图片转换为base64
                const base64Image = await fileToBase64(imageFile);
                
                const response = await fetchWithTimeout('http://localhost:5000/api/analyze-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: base64Image,
                        prompt: prompt,
                        session_id: conversationState.sessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                logAIResponse('视觉分析模型', prompt, result.analysis || result.error);
                
                return {
                    success: result.success,
                    analysis: result.analysis || `分析错误: ${result.error}`,
                    error: result.error
                };
                
            } catch (error) {
                logSystemEvent('API调用失败', `视觉分析API调用失败: ${error.message}`);
                throw new Error(`API调用失败: ${error.message}`);
            }
        }

        // 分析BIA数据
        async function analyzeBIAWithAI(dataFile) {
            try {
                const formData = new FormData();
                formData.append('file', dataFile);
                formData.append('session_id', conversationState.sessionId);

                const response = await fetchWithTimeout('http://localhost:5000/api/upload-bia', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                logAIResponse('BIA分析', `分析BIA数据文件: ${dataFile.name}`, result.analysis || result.error);
                
                return {
                    success: result.success,
                    analysis: result.analysis || `分析错误: ${result.error}`,
                    error: result.error
                };
                
            } catch (error) {
                logSystemEvent('API调用失败', `BIA分析API调用失败: ${error.message}`);
                throw new Error(`API调用失败: ${error.message}`);
            }
        }

        // 最终分析
        async function performFinalAnalysis() {
            addMessage('assistant', '🤔 让我综合分析您提供的所有信息，为您生成个性化的营养评估报告...\n\n这可能需要一些时间，请稍候。');
            
            showTyping();
            updateStatus('综合分析中');
            logSystemEvent('最终分析', '开始综合分析所有收集的数据');

            try {
                // 调用华佗GPT进行综合分析
                const analysisResult = await callHuatuoGPT();
                
                hideTyping();
                
                if (analysisResult.success) {
                    const userName = conversationState.userProfile.name || '';
                    const greeting = userName ? `💫 ${userName}，` : '💫 ';
                    
                    addMessage('assistant', `${greeting}${analysisResult.report}`);
                    
                    // 添加后续对话提示
                    setTimeout(() => {
                        addMessage('assistant', `📝 **您的营养评估已完成！** 

如果您对评估结果有任何疑问，或想了解更多营养知识，请继续与我对话：

💡 您可以询问：
• "如何改善我的营养状况？"
• "推荐一些适合我的食物"  
• "这个分析结果意味着什么？"
• "如何制定个性化饮食计划？"

我会根据您的具体情况提供专业的营养建议和知识科普！`);
                    }, 2000);
                    
                    updateStatus('分析完成');
                    logSystemEvent('分析完成', '综合分析报告生成完成');
                } else {
                    addMessage('assistant', `❌ 分析过程中出现问题：${analysisResult.error}\n\n请稍后重试或联系技术支持。`);
                    updateStatus('分析失败');
                }
                
            } catch (error) {
                hideTyping();
                addMessage('assistant', `❌ 系统错误：${error.message}\n\n请稍后重试。`);
                updateStatus('系统错误');
                logSystemEvent('系统错误', error.message);
            }
        }

        // 调用华佗GPT主脑
        async function callHuatuoGPT() {
            // 构建综合分析提示词
            const prompt = buildComprehensivePrompt();
            
            try {
                const response = await fetchWithTimeout('http://localhost:5000/api/analyze-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        user_profile: conversationState.userProfile,
                        messages: conversationState.messages,
                        session_id: conversationState.sessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const result = await response.json();
                logAIResponse('华佗GPT主脑', prompt, result.report || result.error);
                
                return {
                    success: result.success,
                    report: result.report || `分析失败: ${result.error}`,
                    error: result.error
                };
                
            } catch (error) {
                logSystemEvent('API调用失败', `华佗GPT API调用失败: ${error.message}`);
                throw new Error(`华佗GPT调用失败: ${error.message}`);
            }
        }

        // 构建综合分析提示词
        function buildComprehensivePrompt() {
            const userProfile = conversationState.userProfile;
            
            let prompt = `你是一位资深的临床营养学专家。请基于以下收集到的多模态数据，进行综合的营养状况评估和诊断。

患者基本信息：
- 姓名：${userProfile.name || '未提供'}
- 年龄：${userProfile.age || '未提供'}岁
- 性别：${userProfile.gender || '未提供'}
- 身高：${userProfile.height || '未提供'} cm
- 当前体重：${userProfile.current_weight || '未提供'} kg
- 平时体重：${userProfile.usual_weight || '未提供'} kg

对话过程中收集到的数据：
`;

            // 分析对话历史，提取关键信息
            conversationState.messages.forEach(msg => {
                if (msg.role === 'user' && (msg.content.includes('✅') || msg.content.includes('📎'))) {
                    prompt += `- ${msg.content}\n`;
                }
            });

            prompt += `
请基于以上所有可用数据，进行综合分析并给出最终的营养状况诊断。请从以下几个方面进行分析：

1. **数据一致性分析**: 各个模块的分析结果是否一致？如有分歧，如何解释？
2. **综合诊断**: 基于所有证据的最终营养状况诊断
3. **置信度评估**: 对综合诊断的置信度（0-100%）
4. **支持证据**: 支持诊断结论的关键证据
5. **风险分层**: 当前营养风险等级和干预紧急程度
6. **临床建议**: 具体的营养干预和治疗建议
7. **随访计划**: 后续监测和复查建议

请以专业、客观但友好的语言进行分析，确保结论有充分的循证医学依据。
同时，请在最后明确提醒这是AI辅助的初步筛查结果，建议咨询专业医生。`;

            return prompt;
        }

        // 辅助函数
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result.split(',')[1]);
                reader.onerror = error => reject(error);
            });
        }

        function showTyping() {
            document.getElementById('typingIndicator').style.display = 'block';
            scrollToBottom();
        }

        function hideTyping() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function updateStatus(status) {
            document.getElementById('statusText').textContent = status;
        }

        function scrollToBottom() {
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId, isSuccessfulSubmission = false) {
            document.getElementById(modalId).style.display = 'none';

            // 处理模态框关闭后的逻辑
            if (modalId === 'uploadModal') {
                handleUploadModalClose(isSuccessfulSubmission);
            } else if (modalId === 'glimModal') {
                handleGlimModalClose(isSuccessfulSubmission);
            } else if (modalId === 'profileModal') {
                handleProfileModalClose(isSuccessfulSubmission);
            }
        }

        // 处理上传模态框关闭
        function handleUploadModalClose(isSuccessfulSubmission = false) {
            const uploadType = conversationState.currentUploadType;

            // 检查是否正在进行上传处理（通过检查状态或全局标志）
            const isUploading = conversationState.isUploading || false;

            // 只有在用户真正取消（而不是成功提交或正在上传）时才显示取消消息
            if (uploadType && !isSuccessfulSubmission && !isUploading) {
                const typeName = uploadType === 'photo' ? '面部照片' : 'BIA数据';
                addMessage('user', `❌ 已取消${typeName}上传`);

                // 提供重新选择的选项
                const availableOptions = [];
                const collectedData = conversationState.collectedData || {};

                if (!collectedData.glim_results) {
                    availableOptions.push('GLIM评估问卷');
                }
                if (!collectedData.facial_analysis) {
                    availableOptions.push('面部照片');
                }
                if (!collectedData.bia_analysis) {
                    availableOptions.push('BIA数据');
                }

                availableOptions.push('完成收集');

                addMessage('assistant', '您可以重新选择其他评估方式：', {
                    options: availableOptions
                });

                logSystemEvent('取消上传', `用户取消${typeName}上传`);
            }

            // 清理上传状态（无论是取消还是成功提交都需要清理）
            if (uploadType && !isUploading) {
                conversationState.currentUploadType = null;
                conversationState.selectedFile = null;

                // 重置文件输入和上传按钮
                document.getElementById('fileInput').value = '';
                document.getElementById('uploadButton').disabled = true;
                document.getElementById('fileInfo').style.display = 'none';
            }
        }

        // 处理GLIM模态框关闭
        function handleGlimModalClose(isSuccessfulSubmission = false) {
            // 只有在用户真正取消（而不是成功提交）时才显示取消消息
            if (!isSuccessfulSubmission && !conversationState.collectedData?.glim_results) {
                addMessage('user', '❌ 已取消GLIM问卷填写');

                // 提供重新选择的选项
                const availableOptions = [];
                const collectedData = conversationState.collectedData || {};

                if (!collectedData.glim_results) {
                    availableOptions.push('GLIM评估问卷');
                }
                if (!collectedData.facial_analysis) {
                    availableOptions.push('面部照片');
                }
                if (!collectedData.bia_analysis) {
                    availableOptions.push('BIA数据');
                }

                availableOptions.push('完成收集');

                addMessage('assistant', '您可以重新选择其他评估方式：', {
                    options: availableOptions
                });

                logSystemEvent('取消GLIM', '用户取消GLIM问卷填写');
            }
        }

        // 处理档案模态框关闭
        function handleProfileModalClose(isSuccessfulSubmission = false) {
            // 只有在用户真正取消（而不是成功提交）时才显示取消消息
            if (!isSuccessfulSubmission && !conversationState.userProfile?.name) {
                addMessage('user', '❌ 已取消基本信息填写');

                // 显示重新填写按钮，而不是自动弹出表单
                const retryMessage = `没有基本信息我无法为您提供个性化的营养评估服务。

如果您希望继续，请点击下方按钮重新填写基本信息：`;

                addMessage('assistant', retryMessage, {
                    showRetryProfileButton: true
                });

                logSystemEvent('取消档案', '用户取消基本信息填写');
            }
        }

        // 重新填写基本信息
        function retryProfileForm() {
            // 显示用户点击了重新填写按钮
            addMessage('user', '📝 重新填写基本信息');

            // 弹出个人信息表单
            setTimeout(() => showModal('profileModal'), 300);

            logSystemEvent('重试档案', '用户点击重新填写基本信息按钮');
        }

        // 自动调整输入框高度
        function setupAutoResize() {
            const input = document.getElementById('messageInput');
            input.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            addMessage('user', message);
            input.value = '';
            input.style.height = 'auto';
            
            // 处理用户输入
            processUserChoice(message);
        }

        // 清空对话
        async function clearConversation() {
            if (confirm('确定要清空当前对话吗？这将清理所有已加载的模型并开始新的会话。')) {
                try {
                    updateStatus('清理中...');
                    
                    // 先清理模型
                    try {
                        const clearResponse = await fetchWithTimeout('http://localhost:5000/api/clear_models', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({})
                        }, 30000);

                        if (clearResponse.ok) {
                            const result = await clearResponse.json();
                            if (result.success) {
                                logSystemEvent('模型清理', '成功清理所有已加载的模型');
                                console.log('✅ 模型清理成功:', result.details);
                            } else {
                                logSystemEvent('模型清理', `清理失败: ${result.error}`);
                                console.warn('⚠️ 模型清理失败:', result.error);
                            }
                        } else {
                            logSystemEvent('模型清理', '清理请求失败');
                            console.warn('⚠️ 模型清理请求失败');
                        }
                    } catch (clearError) {
                        logSystemEvent('模型清理', `清理异常: ${clearError.message}`);
                        console.warn('⚠️ 模型清理异常:', clearError);
                        // 即使清理失败，也继续进行对话清空
                    }

                    // 清空对话状态
                    conversationState.messages = [];
                    conversationState.sessionId = generateSessionId();
                    document.getElementById('messagesArea').innerHTML = '';
                    logSystemEvent('清空对话', '用户清空对话历史，开始新会话');
                    
                    // 初始化新对话
                    await initializeChat();
                    
                } catch (error) {
                    console.error('清空对话失败:', error);
                    updateStatus('清空失败');
                    alert('清空对话时发生错误，请刷新页面重试。');
                }
            }
        }

        // 导出聊天记录（纯前端实现）
        function exportChatHistory() {
            try {
                if (!conversationState.messages || conversationState.messages.length === 0) {
                    alert('当前没有聊天记录可以导出');
                    return;
                }

                // 生成TXT格式的聊天记录内容
                const txtContent = generateChatTxt();
                
                // 创建Blob对象
                const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                
                // 生成文件名：智能营养顾问_聊天记录_YYYY-MM-DD_HH-mm-ss.txt
                const now = new Date();
                const timestamp = now.toISOString().slice(0, 19).replace(/[:]/g, '-').replace('T', '_');
                a.download = `智能营养顾问_聊天记录_${timestamp}.txt`;
                
                // 触发下载（用户可选择保存位置）
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                logSystemEvent('导出聊天记录', '成功导出聊天记录为TXT文件');
                
            } catch (error) {
                console.error('导出聊天记录失败:', error);
                alert(`导出失败：${error.message}`);
                logSystemEvent('导出聊天记录', `导出失败: ${error.message}`);
            }
        }

        // 生成聊天记录TXT内容
        function generateChatTxt() {
            const lines = [];
            const now = new Date();
            
            // 文件头部信息
            lines.push('================================================================================');
            lines.push('                        智能营养顾问 - 聊天记录');
            lines.push('================================================================================');
            lines.push('');
            lines.push(`导出时间: ${now.toLocaleString('zh-CN')}`);
            lines.push(`会话ID: ${conversationState.sessionId || '未知'}`);
            lines.push('');
            
            // 用户档案信息
            if (conversationState.userProfile && Object.keys(conversationState.userProfile).length > 0) {
                lines.push('👤 用户档案信息:');
                lines.push('────────────────────────────────────────────────────────────────────────────────');
                
                if (conversationState.userProfile.name) {
                    lines.push(`姓名: ${conversationState.userProfile.name}`);
                }
                if (conversationState.userProfile.age) {
                    lines.push(`年龄: ${conversationState.userProfile.age}岁`);
                }
                if (conversationState.userProfile.gender) {
                    lines.push(`性别: ${conversationState.userProfile.gender}`);
                }
                if (conversationState.userProfile.height) {
                    lines.push(`身高: ${conversationState.userProfile.height}cm`);
                }
                if (conversationState.userProfile.weight) {
                    lines.push(`体重: ${conversationState.userProfile.weight}kg`);
                }
                lines.push('');
            }
            
            // 聊天记录
            lines.push('💬 对话记录:');
            lines.push('────────────────────────────────────────────────────────────────────────────────');
            
            conversationState.messages.forEach((message, index) => {
                const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString('zh-CN') : '时间未知';
                const role = message.role === 'user' ? '👨‍💼 用户' : '🤖 AI助手';
                
                lines.push(`[${timestamp}] ${role}:`);
                lines.push('');
                
                // 处理消息内容，保持格式
                const content = message.content || '';
                const contentLines = content.split('\n');
                contentLines.forEach(line => {
                    lines.push(`  ${line}`);
                });
                
                lines.push('');
                if (index < conversationState.messages.length - 1) {
                    lines.push('- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -');
                    lines.push('');
                }
            });
            
            // 文件尾部
            lines.push('================================================================================');
            lines.push('');
            lines.push('⚠️ 重要提醒：');
            lines.push('• 本报告基于AI技术生成，仅供参考，不能替代专业医生诊断');
            lines.push('• 建议携带此报告咨询专业营养科医生或主治医生');
            lines.push('• 本系统用于辅助筛查，最终诊断需以医生判断为准');
            lines.push('');
            lines.push('================================================================================');
            
            return lines.join('\n');
        }

        // 处理前端动作
        function handleFrontendAction(action) {
            const actionType = action.type;
            
            switch (actionType) {
                case 'show_profile_form':
                    setTimeout(() => showModal('profileModal'), 500);
                    break;
                case 'show_glim_form':
                    setTimeout(() => showModal('glimModal'), 500);
                    break;
                case 'show_photo_upload':
                    console.log('🚀 处理面部照片上传动作');
                    setupUploadModal('photo');
                    console.log('📷 面部照片上传模态框已设置');
                    setTimeout(() => showModal('uploadModal'), 500);
                    break;
                case 'show_bia_upload':
                    setupUploadModal('bia');
                    setTimeout(() => showModal('uploadModal'), 500);
                    break;
                case 'show_analysis_loading':
                    updateStatus('AI综合分析中');
                    showTyping();
                    break;
                case 'show_final_report':
                    updateStatus('分析完成');
                    break;
                case 'show_completion':
                    updateStatus('评估完成');
                    break;
                default:
                    // 无特殊动作
                    break;
            }
        }

        // 获取阶段显示名称
        function getPhaseDisplayName(phase) {
            const phaseNames = {
                'greeting': '初始问候',
                'data_collection': '数据收集',
                'analysis': 'AI分析中',
                'reporting': '生成报告',
                'completed': '评估完成'
            };
            return phaseNames[phase] || phase;
        }

        // 日志系统
        let systemLogs = [];

        function logSystemEvent(event, description, data = null) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                type: 'system',
                event: event,
                description: description,
                data: data,
                session_id: conversationState.sessionId
            };
            systemLogs.push(logEntry);
            console.log(`[SYSTEM] ${event}: ${description}`, data);
        }

        function logMessage(role, content, actions = {}) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                type: 'message',
                role: role,
                content: content,
                actions: actions,
                session_id: conversationState.sessionId
            };
            systemLogs.push(logEntry);
        }

        function logAIResponse(model, prompt, response) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                type: 'ai_call',
                model: model,
                prompt: prompt,
                response: response,
                session_id: conversationState.sessionId
            };
            systemLogs.push(logEntry);
            console.log(`[AI] ${model}:`, { prompt, response });
        }

        // 打开增强版AI日志查看器
        function openEnhancedLogViewer() {
            // 日志查看器需要访问API服务器，所以使用API服务器的地址
            const apiBaseUrl = 'http://localhost:5000';
            window.open(`${apiBaseUrl}/templates/enhanced_log_viewer.html`, '_blank', 'width=1400,height=900');
        }

        // 会话管理功能
        function createNewSession() {
            if (confirm('确定要创建新会话吗？当前会话的内容将被保存。')) {
                // 保存当前会话
                saveCurrentSession();

                // 重置会话状态
                conversationState = {
                    sessionId: generateSessionId(),
                    userProfile: {},
                    messages: [],
                    currentPhase: 'greeting',
                    waitingFor: null,
                    sessionMode: isAutoMode ? 'auto' : 'manual' // 保持当前模式
                };

                // 重置自动模式状态
                resetAutoModeCompletely();

                // 清空界面
                document.getElementById('messagesArea').innerHTML = '';
                document.getElementById('messageInput').value = '';

                // 重新初始化会话
                initializeChat();

                console.log('🆕 新会话已创建:', conversationState.sessionId);
            }
        }

        function openSessionHistory() {
            loadSessionHistory();
            document.getElementById('sessionHistoryModal').style.display = 'flex';
        }

        function closeSessionHistory() {
            document.getElementById('sessionHistoryModal').style.display = 'none';
        }

        function saveCurrentSession() {
            if (!conversationState.sessionId || conversationState.messages.length === 0) {
                return; // 没有内容需要保存
            }

            const sessionData = {
                sessionId: conversationState.sessionId,
                userProfile: conversationState.userProfile,
                messages: conversationState.messages,
                currentPhase: conversationState.currentPhase,
                waitingFor: conversationState.waitingFor,
                timestamp: Date.now(),
                title: generateSessionTitle(),
                sessionMode: conversationState.sessionMode || (isAutoMode ? 'auto' : 'manual') // 标记会话模式
            };

            // 发送到后端保存
            fetch('http://localhost:5000/api/sessions/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(sessionData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ 会话已保存:', sessionData.sessionId);
                } else {
                    console.error('❌ 会话保存失败:', data.error);
                }
            })
            .catch(error => {
                console.error('❌ 会话保存异常:', error);
            });
        }

        function loadSessionHistory() {
            fetch('http://localhost:5000/api/sessions/list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySessionHistory(data.sessions);
                } else {
                    console.error('❌ 获取会话历史失败:', data.error);
                }
            })
            .catch(error => {
                console.error('❌ 获取会话历史异常:', error);
            });
        }

        function displaySessionHistory(sessions) {
            const sessionList = document.getElementById('sessionList');

            if (sessions.length === 0) {
                sessionList.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 40px;">暂无历史会话</div>';
                return;
            }

            sessionList.innerHTML = sessions.map(session => {
                const modeIcon = session.sessionMode === 'auto' ? '🤖' : '💬';
                const modeText = session.sessionMode === 'auto' ? '自动模式' : '手动模式';
                return `
                <div class="session-item" onclick="loadSession('${session.sessionId}')">
                    <div class="session-item-header">
                        <div class="session-item-title">${modeIcon} ${session.title}</div>
                        <div class="session-item-date">${formatDate(session.timestamp)}</div>
                    </div>
                    <div class="session-item-mode" style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">${modeText}</div>
                    <div class="session-item-preview">${session.preview}</div>
                    <div class="session-item-actions">
                        <button class="session-action-btn" onclick="event.stopPropagation(); loadSession('${session.sessionId}')">
                            📂 加载
                        </button>
                        <button class="session-action-btn delete" onclick="event.stopPropagation(); deleteSession('${session.sessionId}')">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
                `;
            }).join('');
        }

        function loadSession(sessionId) {
            fetch(`http://localhost:5000/api/sessions/load/${sessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存当前会话
                    saveCurrentSession();

                    // 加载历史会话
                    conversationState = data.session;

                    // 根据会话模式切换界面
                    const sessionMode = data.session.sessionMode;
                    if (sessionMode === 'auto' && !isAutoMode) {
                        // 切换到自动模式但不清空对话
                        isAutoMode = true;
                        updateModeUI();
                    } else if (sessionMode === 'manual' && isAutoMode) {
                        // 切换到手动模式但不清空对话
                        isAutoMode = false;
                        updateModeUI();
                    }

                    // 重新渲染界面
                    renderMessages();

                    // 关闭弹窗
                    closeSessionHistory();

                    console.log('✅ 会话已加载:', sessionId, '模式:', sessionMode);
                } else {
                    alert('加载会话失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('❌ 加载会话异常:', error);
                alert('加载会话失败');
            });
        }

        function deleteSession(sessionId) {
            if (confirm('确定要删除这个会话吗？此操作不可恢复。')) {
                fetch(`http://localhost:5000/api/sessions/delete/${sessionId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('✅ 会话已删除:', sessionId);
                        loadSessionHistory(); // 重新加载列表
                    } else {
                        alert('删除会话失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('❌ 删除会话异常:', error);
                    alert('删除会话失败');
                });
            }
        }

        function generateSessionTitle() {
            const userName = conversationState.userProfile.name || '用户';
            const messageCount = conversationState.messages.length;
            const date = new Date().toLocaleDateString();
            return `${userName}的营养咨询 (${messageCount}条消息) - ${date}`;
        }

        function formatDate(timestamp) {
            // 处理不同格式的时间戳
            let date;
            if (typeof timestamp === 'string') {
                date = new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                date = new Date(timestamp);
            } else {
                date = new Date();
            }

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return '未知时间';
            }

            return date.toLocaleString('zh-CN');
        }

        function renderMessages() {
            const messagesContainer = document.getElementById('messagesArea');
            messagesContainer.innerHTML = '';

            conversationState.messages.forEach(message => {
                addMessage(message.role, message.content);
            });
        }

        // 模型状态管理
        let modelStatusEventSource = null;
        let statusPollingInterval = null;

        // 更新模型状态显示
        function updateModelStatus(data = null) {
            try {
                if (!data) {
                    // 如果没有提供数据，进行API调用
                    fetch('http://localhost:5000/api/model-status')
                        .then(response => response.json())
                        .then(data => processModelStatusData(data))
                        .catch(error => {
                            console.error('❌ 获取模型状态失败:', error);
                            setModelStatusError();
                        });
                } else {
                    processModelStatusData(data);
                }
            } catch (error) {
                console.error('❌ 处理模型状态时出错:', error);
                setModelStatusError();
            }
        }

        function processModelStatusData(data) {
            if (data.success) {
                const visionLoaded = data.vision_loaded || false;
                const huatuoLoaded = data.huatuo_loaded || false;

                console.log('🔍 模型状态更新:', {
                    vision: visionLoaded,
                    huatuo: huatuoLoaded,
                    loaded_models: data.loaded_models
                });

                // 更新视觉模型状态
                const visionStatus = document.getElementById('visionStatus');
                if (visionLoaded) {
                    visionStatus.textContent = '✅ 已加载';
                    visionStatus.className = 'model-status loaded';
                } else {
                    visionStatus.textContent = '❌ 未加载';
                    visionStatus.className = 'model-status not-loaded';
                }

                // 更新华佗GPT状态
                const huatuoStatus = document.getElementById('huatuoStatus');
                if (huatuoLoaded) {
                    huatuoStatus.textContent = '✅ 已加载';
                    huatuoStatus.className = 'model-status loaded';
                } else {
                    huatuoStatus.textContent = '❌ 未加载';
                    huatuoStatus.className = 'model-status not-loaded';
                }

                console.log('📊 模型状态显示更新完成');
            } else {
                console.error('❌ 模型状态API返回错误:', data.error);
                setModelStatusError();
            }
        }

        function setModelStatusError() {
            document.getElementById('visionStatus').textContent = '❓ 检测中...';
            document.getElementById('huatuoStatus').textContent = '❓ 检测中...';
            document.getElementById('visionStatus').className = 'model-status checking';
            document.getElementById('huatuoStatus').className = 'model-status checking';
        }

        // 启动服务端推送事件连接
        function startModelStatusStream() {
            if (modelStatusEventSource) {
                modelStatusEventSource.close();
            }

            try {
                modelStatusEventSource = new EventSource('http://localhost:5000/api/model-status-stream');
                
                modelStatusEventSource.onmessage = function(event) {
                    try {
                        const eventData = JSON.parse(event.data);
                        if (eventData.type === 'model_status_update') {
                            console.log('📢 收到模型状态实时更新');
                            processModelStatusData({
                                success: true,
                                vision_loaded: eventData.data.vision_loaded,
                                huatuo_loaded: eventData.data.huatuo_loaded,
                                loaded_models: eventData.data.loaded_models
                            });
                        } else if (eventData.type === 'heartbeat') {
                            console.log('💓 收到服务器心跳');
                        }
                    } catch (error) {
                        console.error('❌ 解析SSE数据失败:', error);
                    }
                };

                modelStatusEventSource.onerror = function(event) {
                    console.warn('⚠️ SSE连接错误，切换到轮询模式');
                    modelStatusEventSource.close();
                    modelStatusEventSource = null;
                    startPollingFallback();
                };

                modelStatusEventSource.onopen = function(event) {
                    console.log('✅ 模型状态实时推送连接成功');
                    // 清除轮询定时器
                    if (statusPollingInterval) {
                        clearInterval(statusPollingInterval);
                        statusPollingInterval = null;
                    }
                };

            } catch (error) {
                console.error('❌ 启动SSE连接失败，使用轮询模式:', error);
                startPollingFallback();
            }
        }

        // 轮询模式降级方案
        function startPollingFallback() {
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
            }
            
            console.log('🔄 启动轮询模式，间隔60秒');
            updateModelStatus(); // 立即检查一次
            statusPollingInterval = setInterval(updateModelStatus, 60000); // 60秒轮询一次
        }

        // 点击模态框外部关闭（但不关闭基本信息模态框）
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                // 阻止基本信息模态框通过背景点击关闭
                if (event.target.id === 'profileModal') {
                    return; // 不关闭基本信息模态框
                }
                event.target.style.display = 'none';
            }
        }

        // 页面卸载时自动保存会话
        window.addEventListener('beforeunload', function(e) {
            saveCurrentSession();
        });

        // === 自动模式JavaScript功能（新增） ===
        
        let isAutoMode = false;
        let selectedFiles = [];
        let autoModeInitialized = false;
        let lastAnalysisCompleted = false;
        
        function toggleMode() {
            isAutoMode = !isAutoMode;
            const sidebar = document.getElementById('autoModeSidebar');
            const chatArea = document.getElementById('chatArea');
            const modeToggleBtn = document.getElementById('modeToggleBtn');
            const modeToggleText = document.getElementById('modeToggleText');
            const inputArea = document.getElementById('inputArea');
            
            if (isAutoMode) {
                // 显示左侧面板，调整右侧布局
                sidebar.classList.remove('hidden');
                chatArea.classList.remove('full-width');
                chatArea.classList.add('sidebar-mode');
                modeToggleBtn.classList.add('active');
                modeToggleText.textContent = '📋 切换到手动模式';
                // inputArea.style.display = 'none'; // 注释掉，保持输入框可见
                inputArea.style.display = 'flex'; // 强制显示输入框
                
                // 确保消息区域在自动模式下可见
                const messagesArea = document.getElementById('messagesArea');
                messagesArea.style.display = 'block';
                messagesArea.style.visibility = 'visible';
                
                // 切换到自动模式时清空对话（相当于新开一个对话）
                clearConversationForAutoMode();
                
                // 初始化自动模式
                initializeAutoMode();
                
                // 调试：检查输入区域状态
                console.log('自动模式 - 输入区域显示状态:', inputArea.style.display);
                console.log('自动模式 - 输入区域可见性:', getComputedStyle(inputArea).display);
                console.log('自动模式 - 输入区域高度:', getComputedStyle(inputArea).height);
                
                // 自动模式下也要求填写基本信息
                addMessage('assistant', '🌟 欢迎来到自动分析模式！\n\n在这个模式下，您可以通过左侧面板上传多种营养相关数据（面部图片、BIA数据表格、GLIM表单、病历文档等），系统将自动识别文件类型并进行智能综合分析。\n\n为了提供更准确的个性化分析，请先填写您的基本信息。', {
                    showProfileForm: true
                });
            } else {
                // 隐藏左侧面板，恢复手动模式布局
                sidebar.classList.add('hidden');
                chatArea.classList.remove('sidebar-mode');
                chatArea.classList.add('full-width');
                modeToggleBtn.classList.remove('active');
                modeToggleText.textContent = '🤖 切换到自动模式';
                inputArea.style.display = 'flex'; // 确保手动模式下输入框可见
                
                // 确保消息区域在手动模式下也正常显示
                const messagesArea = document.getElementById('messagesArea');
                messagesArea.style.display = 'block';
                messagesArea.style.visibility = 'visible';
                
                // 切换到手动模式时也清空对话（相当于新开一个对话）
                clearConversationForManualMode();
                
                // 清理自动模式状态
                cleanupAutoMode();
                
                // 添加欢迎消息
                addMessage('assistant', '🌟 欢迎来到手动对话模式！\n\n我会通过友好的对话逐步收集您的信息，并提供个性化的营养建议。您可以直接与我对话，或选择我提供的选项进行营养评估。\n\n首先，让我了解一下您的基本信息。', {
                    showProfileForm: true
                });
            }
        }
        
        // 仅更新UI模式，不清空对话（用于加载历史会话时）
        function updateModeUI() {
            const sidebar = document.getElementById('autoModeSidebar');
            const chatArea = document.getElementById('chatArea');
            const modeToggleBtn = document.getElementById('modeToggleBtn');
            const modeToggleText = document.getElementById('modeToggleText');
            const inputArea = document.getElementById('inputArea');
            
            if (isAutoMode) {
                // 显示左侧面板，调整右侧布局
                sidebar.classList.remove('hidden');
                chatArea.classList.remove('full-width');
                chatArea.classList.add('sidebar-mode');
                modeToggleBtn.classList.add('active');
                modeToggleText.textContent = '📋 切换到手动模式';
                inputArea.style.display = 'flex';
                
                // 确保消息区域可见
                const messagesArea = document.getElementById('messagesArea');
                messagesArea.style.display = 'block';
                messagesArea.style.visibility = 'visible';
                
                // 初始化自动模式（但不清空对话）
                initializeAutoMode();
            } else {
                // 隐藏左侧面板，恢复手动模式布局
                sidebar.classList.add('hidden');
                chatArea.classList.remove('sidebar-mode');
                chatArea.classList.add('full-width');
                modeToggleBtn.classList.remove('active');
                modeToggleText.textContent = '🤖 切换到自动模式';
                inputArea.style.display = 'flex';
                
                // 确保消息区域可见
                const messagesArea = document.getElementById('messagesArea');
                messagesArea.style.display = 'block';
                messagesArea.style.visibility = 'visible';
                
                // 清理自动模式状态（但不清空对话）
                cleanupAutoMode();
            }
        }
        
        function initializeAutoMode() {
            // 只在首次初始化或明确需要重置时才清理状态
            if (!autoModeInitialized) {
                // 首次进入自动模式，设置基础事件
                setupDragAndDrop();
                
                // 设置文件选择事件（避免重复绑定）
                const fileInput = document.getElementById('batchFileInput');
                if (fileInput && !fileInput.hasEventListener) {
                    fileInput.addEventListener('change', handleFileSelect);
                    fileInput.hasEventListener = true;
                }
                
                autoModeInitialized = true;
            }
            
            // 恢复界面状态（如果有之前的文件或进度）
            restoreAutoModeState();
        }
        
        function cleanupAutoMode() {
            // 只停止轮询，保留文件和状态
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
                statusPollingInterval = null;
            }
            
            // 不清理文件和进度状态，让用户切换回来时能看到之前的状态
            // selectedFiles 和界面状态保持不变
        }
        
        // 为自动模式清空对话（相当于新开一个对话）
        function clearConversationForAutoMode() {
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.innerHTML = '';
            
            // 重置对话状态，包括清空用户基本信息
            conversationState = {
                messages: [],
                userProfile: {}, // 清空用户档案，要求重新填写
                currentTask: 'auto_mode',
                selectedFile: null,
                currentUploadType: null,
                sessionId: generateSessionId(),
                sessionMode: 'auto' // 标记为自动模式会话
            };
            
            logSystemEvent('自动模式', '清空对话，开始新的自动模式会话');
        }
        
        // 为手动模式清空对话（相当于新开一个对话）
        function clearConversationForManualMode() {
            const messagesArea = document.getElementById('messagesArea');
            messagesArea.innerHTML = '';
            
            // 重置对话状态，包括清空用户基本信息
            conversationState = {
                messages: [],
                userProfile: {}, // 清空用户档案，要求重新填写
                currentTask: 'manual_mode',
                selectedFile: null,
                currentUploadType: null,
                sessionId: generateSessionId(),
                sessionMode: 'manual' // 标记为手动模式会话
            };
            
            logSystemEvent('手动模式', '清空对话，开始新的手动模式会话');
        }
        
        function restoreAutoModeState() {
            // 恢复文件列表显示
            if (selectedFiles.length > 0) {
                updateFilesList();
            }
            
            // 如果分析已完成且有结果，显示完成状态
            if (lastAnalysisCompleted) {
                // 显示分析完成的状态，但不显示进度面板
                // 用户可以重新选择文件进行新的分析
                document.getElementById('analysisProgressPanel').style.display = 'none';
                // 可以添加一个"分析已完成"的提示
            }
        }
        
        function resetAutoModeCompletely() {
            // 完全重置自动模式（用于新会话或明确重置）
            selectedFiles = [];
            clearSelectedFiles();
            resetProgressPanels();
            lastAnalysisCompleted = false;
            
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
                statusPollingInterval = null;
            }
        }
        
        function setupDragAndDrop() {
            const dropZone = document.getElementById('batchDropZone');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.add('drag-over');
                }, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.remove('drag-over');
                }, false);
            });
            
            dropZone.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                handleFiles(Array.from(files));
            });
        }
        
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        }
        
        function handleFiles(files) {
            // 过滤和验证文件
            const validFiles = [];
            const errors = [];
            
            files.forEach(file => {
                // 检查文件类型
                const validExtensions = ['.jpg', '.jpeg', '.png', '.xlsx', '.xls', '.csv', '.json', '.pdf', '.doc', '.docx', '.txt'];
                const fileExt = '.' + file.name.split('.').pop().toLowerCase();
                
                if (!validExtensions.includes(fileExt)) {
                    errors.push(`${file.name}: 不支持的文件类型`);
                    return;
                }
                
                // 检查文件大小 (50MB限制)
                if (file.size > 50 * 1024 * 1024) {
                    errors.push(`${file.name}: 文件大小超过50MB限制`);
                    return;
                }
                
                // 检查是否已存在
                if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
                    errors.push(`${file.name}: 文件已存在`);
                    return;
                }
                
                validFiles.push(file);
            });
            
            // 检查总文件数量
            if (selectedFiles.length + validFiles.length > 10) {
                errors.push('最多只能选择10个文件');
                validFiles.splice(10 - selectedFiles.length);
            }
            
            // 添加有效文件
            selectedFiles.push(...validFiles);
            
            // 显示错误
            if (errors.length > 0) {
                alert('文件选择警告:\n' + errors.join('\n'));
            }
            
            // 更新界面
            updateFilesList();
        }
        
        function updateFilesList() {
            const panel = document.getElementById('selectedFilesPanel');
            const grid = document.getElementById('selectedFilesGrid');
            const summary = document.getElementById('uploadSummary');
            
            if (selectedFiles.length === 0) {
                panel.style.display = 'none';
                return;
            }
            
            panel.style.display = 'block';
            
            // 更新文件列表
            grid.innerHTML = selectedFiles.map((file, index) => {
                const fileIcon = getFileIcon(file.name);
                const fileSize = formatFileSize(file.size);
                
                return `
                    <div class="file-item">
                        <span class="file-icon">${fileIcon}</span>
                        <div class="file-info">
                            <div class="file-name" title="${file.name}">${file.name}</div>
                            <div class="file-size">${fileSize}</div>
                        </div>
                        <button class="remove-file-btn" onclick="removeFile(${index})" title="移除文件">×</button>
                    </div>
                `;
            }).join('');
            
            // 更新摘要
            const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
            summary.innerHTML = `
                已选择 <strong>${selectedFiles.length}</strong> 个文件，
                总大小 <strong>${formatFileSize(totalSize)}</strong>
            `;
        }
        
        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const iconMap = {
                'jpg': '📷', 'jpeg': '📷', 'png': '📷',
                'xlsx': '📊', 'xls': '📊', 'csv': '📊',
                'json': '📋',
                'pdf': '📄', 'doc': '📄', 'docx': '📄', 'txt': '📄'
            };
            return iconMap[ext] || '📎';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFilesList();
        }
        
        function clearSelectedFiles() {
            selectedFiles = [];
            updateFilesList();
            lastAnalysisCompleted = false; // 重置分析完成状态
            
            // 重置文件输入
            document.getElementById('batchFileInput').value = '';
            
            // 隐藏进度面板
            document.getElementById('analysisProgressPanel').style.display = 'none';
            
            // 停止可能存在的轮询
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
                statusPollingInterval = null;
            }
        }
        
        async function startBatchAnalysis() {
            if (selectedFiles.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            // 检查是否填写了基本信息
            if (!conversationState.userProfile || !conversationState.userProfile.name) {
                alert('请先填写基本信息才能开始智能分析');
                addMessage('assistant', '🔔 请先填写您的基本信息，这将帮助我们提供更准确的个性化分析。', {
                    showProfileForm: true
                });
                setTimeout(() => showModal('profileModal'), 500);
                return;
            }
            
            // 隐藏文件选择界面，显示进度界面
            document.getElementById('selectedFilesPanel').style.display = 'none';
            document.getElementById('analysisProgressPanel').style.display = 'block';
            
            // 重置进度状态
            resetProgressStatus();
            updateStageStatus('ingestion', 'running', '正在识别文件类型...');
            
            try {
                // 构建FormData
                const formData = new FormData();
                selectedFiles.forEach(file => {
                    formData.append('files[]', file);
                });
                formData.append('session_id', conversationState.sessionId);
                
                // 发送批量上传请求
                const response = await fetch('http://localhost:5000/api/upload-batch', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    // 开始轮询状态
                    startStatusPolling();
                } else {
                    throw new Error(result.error || '上传失败');
                }
                
            } catch (error) {
                console.error('批量分析失败:', error);
                updateStageStatus('ingestion', 'error', `分析失败: ${error.message}`);
                alert(`批量分析失败: ${error.message}`);
            }
        }
        
        function startStatusPolling() {
            // 清除之前的轮询
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
            }
            
            // 开始新的轮询
            statusPollingInterval = setInterval(async () => {
                try {
                    const response = await fetch(`http://localhost:5000/api/session/${conversationState.sessionId}/status`);
                    
                    if (response.ok) {
                        const status = await response.json();
                        console.log('轮询得到完整状态:', status); // 调试日志
                        console.log('当前阶段:', status.current_stage); // 调试日志
                        console.log('会话ID:', conversationState.sessionId); // 调试日志
                        updateAnalysisProgress(status);
                        
                        // 如果分析完成，停止轮询
                        if (status.current_stage === 'completed' || status.current_stage === 'follow_up_conversation') {
                            clearInterval(statusPollingInterval);
                            statusPollingInterval = null;
                            lastAnalysisCompleted = true; // 标记分析已完成
                            
                            // 延迟一下再隐藏进度面板，并获取分析结果
                            setTimeout(async () => {
                                document.getElementById('analysisProgressPanel').style.display = 'none';
                                
                                // 获取会话中的分析结果
                                try {
                                    const sessionResponse = await fetch(`/api/get-session/${conversationState.sessionId}`);
                                    if (sessionResponse.ok) {
                                        const sessionData = await sessionResponse.json();
                                        if (sessionData.success && sessionData.messages) {
                                            // 查找最终分析报告
                                            const finalReportMessage = sessionData.messages.find(msg => 
                                                msg.message_type === 'comprehensive_analysis_complete' || 
                                                msg.message_type === 'final_report' ||
                                                (msg.role === 'assistant' && 
                                                    (msg.content.includes('🎯 营养状况诊断') || 
                                                     msg.content.includes('营养状况分析报告') ||
                                                     msg.content.includes('## 综合分析结果')))
                                            );
                                            
                                            if (finalReportMessage) {
                                                // 显示实际的分析结果
                                                addMessage('assistant', finalReportMessage.content);
                                                console.log('✅ 找到并显示了综合分析报告');
                                            } else {
                                                console.warn('⚠️ 未找到综合分析报告，将显示默认后续对话提示');
                                                // 如果找不到特定的分析报告，显示最后一条助手消息
                                                const lastAssistantMessage = sessionData.messages.filter(msg => msg.role === 'assistant').pop();
                                                if (lastAssistantMessage && !lastAssistantMessage.content.includes('现在您可以继续与我对话')) {
                                                    addMessage('assistant', lastAssistantMessage.content);
                                                    console.log('✅ 显示了最后一条助手消息作为分析结果');
                                                }
                                            }
                                        }
                                    }
                                } catch (error) {
                                    console.error('获取分析结果失败:', error);
                                }
                                
                                // 显示后续对话提示（但不要与分析报告重复）
                                const shouldShowFollowup = !finalReportMessage || 
                                    (!finalReportMessage.content.includes('现在您可以继续与我对话') &&
                                     !finalReportMessage.content.includes('如果您对报告有任何疑问') &&
                                     !finalReportMessage.content.includes('请随时提问'));
                                
                                if (shouldShowFollowup) {
                                    addMessage('assistant', `💬 **现在您可以继续与我对话，获得更多帮助：**
• 询问分析结果的具体含义
• 获得个性化的营养改善建议  
• 了解相关的营养知识
• 制定针对性的饮食计划

请在下方输入框中输入您的问题，我会为您提供专业的解答！

如需重新分析其他数据，请点击左侧的"清空"按钮后重新选择文件。`);
                                    console.log('✅ 显示了后续对话提示');
                                } else {
                                    console.log('✅ 分析报告已包含后续指导，跳过额外提示');
                                }
                            }, 3000);
                        }
                    } else {
                        console.warn('状态查询响应异常:', response.status);
                    }
                } catch (error) {
                    console.error('状态查询失败:', error);
                }
            }, 2000); // 每2秒轮询一次
        }
        
        function updateAnalysisProgress(status) {
            // 更新总体进度
            const progress = status.progress || 0;
            document.getElementById('overallProgressBar').style.width = `${progress * 100}%`;
            document.getElementById('overallProgressText').textContent = `${Math.round(progress * 100)}%`;
            
            // 更新阶段状态
            const currentStage = status.current_stage;
            
            console.log('收到状态更新:', currentStage); // 调试日志
            
            switch (currentStage) {
                case 'ingestion':
                    updateStageStatus('ingestion', 'running', '正在识别和解析文件...');
                    break;
                    
                case 'analysis_planning':
                    updateStageStatus('ingestion', 'completed', '文件识别完成');
                    updateStageStatus('analysis', 'running', '正在制定分析计划...');
                    break;
                    
                case 'batch_tools':
                case 'auto_serial_analysis':
                    updateStageStatus('ingestion', 'completed', '文件识别完成');
                    updateStageStatus('analysis', 'running', '正在执行智能分析（视觉分析、体成分分析等）...');
                    updateModulesStatus(status.module_status || {});
                    break;
                    
                case 'final_analysis':
                case 'comprehensive_analysis':
                    updateStageStatus('ingestion', 'completed', '文件识别完成');
                    updateStageStatus('analysis', 'completed', '专项分析完成');
                    updateStageStatus('comprehensive', 'running', '正在生成综合报告...');
                    break;
                    
                case 'completed':
                case 'follow_up_conversation':
                    updateStageStatus('ingestion', 'completed', '文件识别完成');
                    updateStageStatus('analysis', 'completed', '专项分析完成');
                    updateStageStatus('comprehensive', 'completed', '综合分析完成');
                    break;
                    
                case 'ingestion_failed':
                    updateStageStatus('ingestion', 'error', '文件识别失败');
                    break;
                    
                default:
                    console.warn('未知状态:', currentStage);
                    break;
            }
            
            // 显示错误信息
            if (status.errors && status.errors.length > 0) {
                const lastError = status.errors[status.errors.length - 1];
                console.warn('分析过程中的错误:', lastError);
            }
        }
        
        function updateStageStatus(stage, status, detail) {
            const stageElement = document.getElementById(`${stage}Stage`);
            const statusElement = document.getElementById(`${stage}Status`);
            
            if (stageElement && statusElement) {
                // 更新阶段样式
                stageElement.className = `stage-item ${status}`;
                
                // 更新状态文本
                const statusTexts = {
                    'running': '分析中',
                    'completed': '已完成',
                    'error': '失败'
                };
                statusElement.textContent = statusTexts[status] || '等待中';
                statusElement.className = `stage-status ${status}`;
                
                // 更新详细信息
                const detailElement = stageElement.querySelector('.stage-detail');
                if (detailElement && detail) {
                    detailElement.textContent = detail;
                }
            }
        }
        
        function updateModulesStatus(moduleStatus) {
            const modulesDetail = document.getElementById('modulesDetail');
            const modulesGrid = document.getElementById('modulesGrid');
            
            if (Object.keys(moduleStatus).length === 0) {
                modulesDetail.style.display = 'none';
                return;
            }
            
            modulesDetail.style.display = 'block';
            
            const moduleNames = {
                'facial_analysis': '👤 面部视觉分析',
                'bia_analysis': '⚡ BIA体成分分析',
                'glim_assessment': '📋 GLIM营养评估'
            };
            
            modulesGrid.innerHTML = Object.entries(moduleStatus).map(([module, status]) => {
                const name = moduleNames[module] || module;
                const statusText = {
                    'pending': '等待中',
                    'running': '分析中',
                    'done': '已完成',
                    'error': '失败'
                }[status] || status;
                
                return `
                    <div class="module-status-card">
                        <div class="module-title">${name}</div>
                        <div class="module-status">${statusText}</div>
                    </div>
                `;
            }).join('');
        }
        
        function resetProgressPanels() {
            document.getElementById('analysisProgressPanel').style.display = 'none';
            resetProgressStatus();
        }
        
        function resetProgressStatus() {
            // 重置进度条
            document.getElementById('overallProgressBar').style.width = '0%';
            document.getElementById('overallProgressText').textContent = '0%';
            
            // 重置所有阶段状态
            ['ingestion', 'analysis', 'comprehensive'].forEach(stage => {
                updateStageStatus(stage, 'pending', '');
            });
            
            // 隐藏模块详情
            document.getElementById('modulesDetail').style.display = 'none';
        }
    </script>
</body>
</html>