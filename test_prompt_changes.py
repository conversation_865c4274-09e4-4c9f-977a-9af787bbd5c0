#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词修改测试工具
用于快速测试修改后的提示词效果
"""

from config.unified_prompt_manager import get_system_prompt

def test_prompt_changes():
    """测试提示词修改效果"""
    
    print("🧪 测试当前提示词状态")
    print("=" * 50)
    
    # 测试面部分析提示词
    print("👁️ 面部分析提示词:")
    facial_prompt = get_system_prompt("facial", "system")
    print(f"长度: {len(facial_prompt)} 字符")
    print(f"前100字符: {facial_prompt[:100]}...")
    print()
    
    # 测试GLIM评估提示词
    print("📋 GLIM评估提示词:")
    glim_prompt = get_system_prompt("glim", "system")
    print(f"长度: {len(glim_prompt)} 字符")
    print(f"前100字符: {glim_prompt[:100]}...")
    print()
    
    # 测试综合分析提示词
    print("🧠 综合分析提示词:")
    comprehensive_prompt = get_system_prompt("comprehensive", "system")
    print(f"长度: {len(comprehensive_prompt)} 字符")
    print(f"前100字符: {comprehensive_prompt[:100]}...")
    print()
    
    print("✅ 测试完成")

if __name__ == "__main__":
    test_prompt_changes()
