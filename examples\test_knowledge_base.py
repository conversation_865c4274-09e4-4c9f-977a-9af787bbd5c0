#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试营养知识库功能
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.knowledge.nutrition_kb import NutritionKnowledgeBase, nutrition_kb


def test_knowledge_loading():
    """测试知识库加载"""
    print("=== 测试知识库加载 ===")
    kb = NutritionKnowledgeBase()
    
    if kb.knowledge_loaded:
        print("✅ 知识库加载成功")
        return True
    else:
        print("❌ 知识库加载失败")
        return False


def test_vision_prompt():
    """测试视觉提示词获取"""
    print("\n=== 测试视觉提示词 ===")
    
    prompt = nutrition_kb.get_vision_prompt_template()
    if prompt and len(prompt) > 100:
        print("✅ 视觉提示词获取成功")
        print(f"提示词长度: {len(prompt)} 字符")
        print(f"提示词预览: {prompt[:200]}...")
        return True
    else:
        print("❌ 视觉提示词获取失败")
        return False


def test_facial_regions():
    """测试面部区域信息"""
    print("\n=== 测试面部区域信息 ===")
    
    regions = nutrition_kb.get_facial_regions()
    expected_regions = ["upper_face", "midface", "lower_face"]
    
    found_regions = list(regions.keys())
    if all(region in found_regions for region in expected_regions):
        print("✅ 面部区域信息完整")
        for region_key, region_data in regions.items():
            print(f"  - {region_data['name']}: {len(region_data['features'])}个特征")
        return True
    else:
        print("❌ 面部区域信息不完整")
        print(f"期望区域: {expected_regions}")
        print(f"实际区域: {found_regions}")
        return False


def test_malnutrition_score():
    """测试营养不良评分计算"""
    print("\n=== 测试营养不良评分计算 ===")
    
    # 模拟视觉分析结果
    sample_findings = [
        {
            "facial_region": "Upper Face",
            "finding": "太阳穴轻微凹陷",
            "confidence": 0.7,
            "severity": "mild"
        },
        {
            "facial_region": "Midface",
            "finding": "面颊明显凹陷",
            "confidence": 0.8,
            "severity": "moderate"
        },
        {
            "facial_region": "Lower Face",
            "finding": "下颌轮廓正常",
            "confidence": 0.9,
            "severity": "normal"
        }
    ]
    
    try:
        score_result = nutrition_kb.calculate_malnutrition_score(sample_findings)
        print("✅ 营养不良评分计算成功")
        print(f"  总分: {score_result['total_score']}")
        print(f"  平均分: {score_result['average_score']}")
        print(f"  营养状况: {score_result['nutrition_status']}")
        print(f"  有效发现数: {score_result['valid_findings']}")
        return True
    except Exception as e:
        print(f"❌ 营养不良评分计算失败: {e}")
        return False


def test_contextual_prompt():
    """测试上下文化提示词"""
    print("\n=== 测试上下文化提示词 ===")
    
    patient_info = {
        "age": 70,
        "gender": "male",
        "diseases": ["糖尿病", "慢性肾病"]
    }
    
    try:
        contextual_prompt = nutrition_kb.get_contextual_prompt(patient_info)
        base_prompt = nutrition_kb.get_vision_prompt_template()
        
        if len(contextual_prompt) > len(base_prompt):
            print("✅ 上下文化提示词生成成功")
            print(f"基础提示词长度: {len(base_prompt)}")
            print(f"上下文提示词长度: {len(contextual_prompt)}")
            print(f"增加内容预览: {contextual_prompt[len(base_prompt):200]}")
            return True
        else:
            print("❌ 上下文化提示词未能正确生成")
            return False
    except Exception as e:
        print(f"❌ 上下文化提示词生成失败: {e}")
        return False


def test_response_validation():
    """测试响应验证功能"""
    print("\n=== 测试响应验证功能 ===")
    
    # 正确格式的响应
    valid_response = {
        "visual_analysis": [
            {
                "facial_region": "Upper Face",
                "finding": "太阳穴正常",
                "confidence": 0.8,
                "severity": "normal"
            }
        ],
        "overall_assessment": {
            "malnutrition_likelihood": "normal",
            "confidence": 0.8,
            "key_findings": ["无异常发现"]
        }
    }
    
    # 错误格式的响应
    invalid_response = {
        "visual_analysis": "not a list",  # 错误：应该是列表
        # 缺少overall_assessment
    }
    
    try:
        valid_result = nutrition_kb.validate_vision_response(valid_response)
        invalid_result = nutrition_kb.validate_vision_response(invalid_response)
        
        if valid_result["is_valid"] and not invalid_result["is_valid"]:
            print("✅ 响应验证功能正常")
            print(f"  有效响应验证结果: {valid_result['is_valid']}")
            print(f"  无效响应验证结果: {invalid_result['is_valid']}")
            print(f"  无效响应错误: {invalid_result['errors']}")
            return True
        else:
            print("❌ 响应验证功能异常")
            return False
    except Exception as e:
        print(f"❌ 响应验证测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试营养知识库系统")
    
    results = []
    
    # 运行所有测试
    results.append(test_knowledge_loading())
    results.append(test_vision_prompt())
    results.append(test_facial_regions())
    results.append(test_malnutrition_score())
    results.append(test_contextual_prompt())
    results.append(test_response_validation())
    
    # 总结结果
    print(f"\n📊 测试结果: {sum(results)}/{len(results)} 通过")
    if all(results):
        print("🎉 所有知识库测试通过！")
    else:
        print("⚠️  部分知识库测试失败，请检查相关功能")


if __name__ == "__main__":
    main()

