#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试身高体重处理修改是否正确
"""

import requests
import json
from datetime import datetime

# API服务器地址
BASE_URL = "http://localhost:5000"

def test_profile_submission():
    """测试用户档案提交，验证身高体重0值是否被正确过滤"""
    
    print("=" * 60)
    print("测试用户档案提交 - 身高体重0值过滤")
    print("=" * 60)
    
    # 测试数据 - 包含0值的身高体重
    test_profile = {
        "session_id": f"test_{datetime.now().timestamp()}",
        "profile": {
            "name": "测试用户",
            "age": 69,
            "gender": "男",
            "height": 0,    # 应该被过滤掉
            "weight": 0     # 应该被过滤掉
        }
    }
    
    print("🔍 发送测试数据:")
    print(json.dumps(test_profile, ensure_ascii=False, indent=2))
    
    try:
        # 发送请求
        response = requests.post(
            f"{BASE_URL}/api/submit-profile",
            json=test_profile,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ API响应成功:")
            print(f"状态: {result.get('success')}")
            
            # 检查返回的消息
            messages = result.get('messages', [])
            if messages:
                print(f"\n🤖 AI回复:")
                for msg in messages:
                    print(f"角色: {msg.get('role')}")
                    print(f"内容: {msg.get('content')}")
                    print("-" * 40)
            
            # 检查是否正确处理了身高体重
            ai_content = messages[0].get('content', '') if messages else ''
            if "身高：0cm" in ai_content:
                print("❌ 错误：AI回复中仍包含身高：0cm")
            else:
                print("✅ 正确：AI回复中没有包含无效的身高信息")
                
            if "体重：0kg" in ai_content:
                print("❌ 错误：AI回复中仍包含体重：0kg")
            else:
                print("✅ 正确：AI回复中没有包含无效的体重信息")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_empty_height_weight():
    """测试空值和字符串0的情况"""
    
    print("\n" + "=" * 60)
    print("测试空值和字符串处理")
    print("=" * 60)
    
    # 测试数据 - 包含空值和字符串0
    test_profile = {
        "session_id": f"test_empty_{datetime.now().timestamp()}",
        "profile": {
            "name": "空值测试用户",
            "age": 45,
            "gender": "女",
            "height": "",      # 空字符串
            "weight": "0"      # 字符串0
        }
    }
    
    print("🔍 发送测试数据:")
    print(json.dumps(test_profile, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/submit-profile",
            json=test_profile,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ API响应成功")
            
            messages = result.get('messages', [])
            if messages:
                ai_content = messages[0].get('content', '')
                print(f"🤖 AI回复内容:\n{ai_content}")
                
                # 验证处理结果
                if "身高：" not in ai_content:
                    print("✅ 正确：空字符串身高被过滤")
                else:
                    print("❌ 错误：空字符串身高未被过滤")
                    
                if "体重：0kg" in ai_content or "体重：kg" in ai_content:
                    print("❌ 错误：字符串0体重未被正确处理")
                else:
                    print("✅ 正确：字符串0体重被过滤")
                    
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_valid_height_weight():
    """测试有效的身高体重值"""
    
    print("\n" + "=" * 60)
    print("测试有效身高体重值")
    print("=" * 60)
    
    test_profile = {
        "session_id": f"test_valid_{datetime.now().timestamp()}",
        "profile": {
            "name": "有效值测试用户",
            "age": 35,
            "gender": "男",
            "height": 170,    # 有效身高
            "weight": 65      # 有效体重
        }
    }
    
    print("🔍 发送测试数据:")
    print(json.dumps(test_profile, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/submit-profile",
            json=test_profile,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ API响应成功")
            
            messages = result.get('messages', [])
            if messages:
                ai_content = messages[0].get('content', '')
                print(f"🤖 AI回复内容:\n{ai_content}")
                
                # 验证有效值是否被保留
                if "身高：170cm" in ai_content:
                    print("✅ 正确：有效身高被保留")
                else:
                    print("❌ 错误：有效身高未被显示")
                    
                if "体重：65kg" in ai_content:
                    print("✅ 正确：有效体重被保留")
                else:
                    print("❌ 错误：有效体重未被显示")
                    
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主测试函数"""
    print("🧪 开始身高体重处理修改验证测试")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 先检查API服务器是否运行
    try:
        health_response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ API服务器未运行，请先启动系统")
            return
    except:
        print("❌ 无法连接到API服务器，请先启动系统")
        return
    
    print("✅ API服务器连接正常")
    
    # 运行测试
    test_profile_submission()
    test_empty_height_weight() 
    test_valid_height_weight()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("=" * 60)
    print("修改说明：")
    print("1. API服务器会过滤掉0值、空值、非数字的身高体重")
    print("2. 对话智能体也会清理用户档案数据")
    print("3. 工具调用智能体只显示有效的身高体重信息")
    print("4. BIA工具仍然需要真实的Excel文件才会被调用")

if __name__ == "__main__":
    main()
