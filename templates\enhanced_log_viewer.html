<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版AI日志查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #0d1117;
            color: #c9d1d9;
            line-height: 1.5;
            font-size: 14px;
        }
        
        .header {
            background: #161b22;
            padding: 20px;
            border-bottom: 1px solid #30363d;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            color: #58a6ff;
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .header-info {
            color: #8b949e;
            font-size: 14px;
            display: flex;
            gap: 20px;
        }
        
        .controls {
            background: #21262d;
            padding: 15px 20px;
            border-bottom: 1px solid #30363d;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-group label {
            color: #f0f6fc;
            font-size: 13px;
            font-weight: 500;
        }
        
        select, input {
            background: #0d1117;
            border: 1px solid #30363d;
            color: #c9d1d9;
            padding: 6px 10px;
            border-radius: 6px;
            font-family: inherit;
            font-size: 13px;
        }
        
        select:focus, input:focus {
            outline: none;
            border-color: #58a6ff;
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        }
        
        .btn {
            background: #238636;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-size: 13px;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #2ea043;
        }
        
        .btn-secondary {
            background: #21262d;
            border: 1px solid #30363d;
        }
        
        .btn-secondary:hover {
            background: #30363d;
        }
        
        .main-content {
            padding: 20px;
            max-width: 100%;
            overflow-x: auto;
        }
        
        .log-entry {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        
        .log-header {
            background: #161b22;
            padding: 12px 16px;
            border-bottom: 1px solid #30363d;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }
        
        .log-header:hover {
            background: #21262d;
        }
        
        .log-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .log-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .log-type.ai-call {
            background: #1f6feb;
            color: white;
        }
        
        .log-type.ai-response {
            background: #238636;
            color: white;
        }
        
        .log-type.system {
            background: #f85149;
            color: white;
        }
        
        .log-type.function {
            background: #a5a5a5;
            color: white;
        }

        .log-type.user-data {
            background: #da3633;
            color: white;
        }
        
        .log-event {
            color: #f0f6fc;
            font-weight: 500;
        }
        
        .log-timestamp {
            color: #8b949e;
            font-size: 12px;
        }
        
        .log-body {
            padding: 16px;
            display: none;
        }
        
        .log-body.expanded {
            display: block;
        }
        
        .log-section {
            margin-bottom: 16px;
        }
        
        .log-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            color: #58a6ff;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-content {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 12px;
            overflow-x: auto;
        }
        
        .prompt-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #e6edf3;
            line-height: 1.6;
        }
        
        .response-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #7ee787;
            line-height: 1.6;
        }
        
        .json-content {
            color: #79c0ff;
            font-size: 13px;
        }
        
        .stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
        }
        
        .stat-label {
            color: #8b949e;
            margin-right: 6px;
        }
        
        .stat-value {
            color: #f0f6fc;
            font-weight: 600;
        }
        
        .expand-icon {
            transition: transform 0.2s;
            color: #8b949e;
        }
        
        .expand-icon.expanded {
            transform: rotate(90deg);
        }
        
        .no-logs {
            text-align: center;
            color: #8b949e;
            padding: 40px;
            font-style: italic;
        }
        
        .loading {
            text-align: center;
            color: #58a6ff;
            padding: 20px;
        }
        
        .error {
            background: #490202;
            border: 1px solid #f85149;
            color: #ffa198;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        
        .token-usage {
            display: flex;
            gap: 12px;
            font-size: 12px;
        }
        
        .token-item {
            background: #21262d;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #30363d;
        }
        
        .model-info {
            color: #a5a5a5;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .function-call {
            background: #2d1b69;
            border: 1px solid #6f42c1;
            border-radius: 6px;
            padding: 8px;
            margin: 8px 0;
        }
        
        .function-name {
            color: #b392f0;
            font-weight: 600;
        }
        
        .function-args {
            color: #79c0ff;
            margin-top: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI交互日志查看器</h1>
        <div class="header-info">
            <span>实时监控AI模型调用和响应</span>
            <span id="connectionStatus">🔴 未连接</span>
        </div>
    </div>
    
    <div class="controls">
        <div class="control-group">
            <label>类型筛选:</label>
            <select id="typeFilter">
                <option value="all">全部</option>
                <option value="ai-call">AI调用</option>
                <option value="ai-response">AI响应</option>
                <option value="function">函数调用</option>
                <option value="system">系统事件</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>搜索:</label>
            <input type="text" id="searchFilter" placeholder="搜索日志内容...">
        </div>
        
        <div class="control-group">
            <button class="btn" onclick="refreshLogs()">🔄 刷新</button>
            <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空</button>
            <button class="btn btn-secondary" onclick="exportLogs()">📥 导出</button>
        </div>
        
        <div class="control-group">
            <label>
                <input type="checkbox" id="autoRefresh" checked> 自动刷新
            </label>
        </div>
    </div>
    
    <div class="main-content">
        <div class="stats" id="statsContainer">
            <!-- 统计信息将在这里显示 -->
        </div>
        
        <div id="logsContainer">
            <div class="loading">正在加载日志...</div>
        </div>
    </div>

    <script>
        let allLogs = [];
        let filteredLogs = [];
        let autoRefreshInterval;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeLogViewer();
            setupEventListeners();
            startAutoRefresh();
        });
        
        function initializeLogViewer() {
            refreshLogs();
        }
        
        function setupEventListeners() {
            document.getElementById('typeFilter').addEventListener('change', filterLogs);
            document.getElementById('searchFilter').addEventListener('input', filterLogs);
            document.getElementById('autoRefresh').addEventListener('change', toggleAutoRefresh);
        }
        
        function startAutoRefresh() {
            if (document.getElementById('autoRefresh').checked) {
                autoRefreshInterval = setInterval(refreshLogs, 2000);
            }
        }
        
        function toggleAutoRefresh() {
            if (document.getElementById('autoRefresh').checked) {
                startAutoRefresh();
            } else {
                clearInterval(autoRefreshInterval);
            }
        }
        
        // 获取API基础URL
        function getApiBaseUrl() {
            // 从当前页面URL获取协议、主机和端口
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port;

            // 如果有端口号，使用当前端口；否则使用默认端口5000
            const apiPort = port ? port : '5000';

            return `${protocol}//${hostname}:${apiPort}`;
        }

        // 刷新日志
        async function refreshLogs() {
            try {
                document.getElementById('connectionStatus').textContent = '🟡 连接中...';

                // 动态获取API URL
                const apiUrl = `${getApiBaseUrl()}/api/logs/enhanced`;
                console.log('正在连接API:', apiUrl);

                // 调用API获取增强日志
                const response = await fetch(apiUrl);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                allLogs = data.logs || [];

                document.getElementById('connectionStatus').textContent = '🟢 已连接';
                filterLogs();
                updateStats();

            } catch (error) {
                console.error('获取日志失败:', error);
                document.getElementById('connectionStatus').textContent = '🔴 连接失败';
                showError('无法获取日志数据: ' + error.message);
            }
        }
        
        // 过滤日志
        function filterLogs() {
            const typeFilter = document.getElementById('typeFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
            
            filteredLogs = allLogs.filter(log => {
                if (typeFilter !== 'all' && log.type !== typeFilter) {
                    return false;
                }
                
                if (searchFilter) {
                    const searchText = JSON.stringify(log).toLowerCase();
                    if (!searchText.includes(searchFilter)) {
                        return false;
                    }
                }
                
                return true;
            });
            
            renderLogs();
        }
        
        // 渲染日志
        function renderLogs() {
            const container = document.getElementById('logsContainer');
            
            if (filteredLogs.length === 0) {
                container.innerHTML = '<div class="no-logs">没有符合条件的日志</div>';
                return;
            }
            
            const logsHTML = filteredLogs.map((log, index) => renderLogEntry(log, index)).join('');
            container.innerHTML = logsHTML;
        }
        
        // 渲染单个日志条目
        function renderLogEntry(log, index) {
            const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
            const logId = `log-${index}`;
            
            return `
                <div class="log-entry">
                    <div class="log-header" onclick="toggleLogBody('${logId}')">
                        <div class="log-title">
                            <span class="log-type ${log.type}">${getTypeLabel(log.type)}</span>
                            <span class="log-event">${log.event || 'Unknown'}</span>
                            ${log.model ? `<span class="model-info">模型: ${log.model}</span>` : ''}
                        </div>
                        <div>
                            <span class="log-timestamp">${timestamp}</span>
                            <span class="expand-icon" id="${logId}-icon">▶</span>
                        </div>
                    </div>
                    <div class="log-body" id="${logId}-body">
                        ${renderLogContent(log)}
                    </div>
                </div>
            `;
        }
        
        // 渲染日志内容
        function renderLogContent(log) {
            let content = '';
            
            // 根据日志类型渲染不同内容
            switch (log.type) {
                case 'ai-call':
                    content = renderAICallContent(log);
                    break;
                case 'ai-response':
                    content = renderAIResponseContent(log);
                    break;
                case 'function':
                    content = renderFunctionContent(log);
                    break;
                case 'system':
                    content = renderSystemContent(log);
                    break;
                case 'user-data':
                    content = renderUserDataContent(log);
                    break;
                default:
                    content = renderGenericContent(log);
            }
            
            return content;
        }
        
        // 渲染AI调用内容
        function renderAICallContent(log) {
            return `
                ${log.system_prompt ? `
                <div class="log-section">
                    <div class="section-title">📋 系统提示词</div>
                    <div class="section-content">
                        <div class="prompt-content">${escapeHtml(log.system_prompt)}</div>
                    </div>
                </div>
                ` : ''}
                
                ${log.user_prompt ? `
                <div class="log-section">
                    <div class="section-title">📝 用户提示词</div>
                    <div class="section-content">
                        <div class="prompt-content">${escapeHtml(log.user_prompt)}</div>
                    </div>
                </div>
                ` : ''}
                
                ${log.model_config ? `
                <div class="log-section">
                    <div class="section-title">⚙️ 模型配置</div>
                    <div class="section-content">
                        <div class="json-content">${formatJSON(log.model_config)}</div>
                    </div>
                </div>
                ` : ''}
            `;
        }
        
        // 渲染AI响应内容
        function renderAIResponseContent(log) {
            return `
                ${log.response_content ? `
                <div class="log-section">
                    <div class="section-title">🤖 AI响应内容</div>
                    <div class="section-content">
                        <div class="response-content">${escapeHtml(log.response_content)}</div>
                    </div>
                </div>
                ` : ''}
                
                ${log.token_usage ? `
                <div class="log-section">
                    <div class="section-title">💰 Token使用情况</div>
                    <div class="section-content">
                        <div class="token-usage">
                            <div class="token-item">输入: ${log.token_usage.prompt_tokens || 0}</div>
                            <div class="token-item">输出: ${log.token_usage.completion_tokens || 0}</div>
                            <div class="token-item">总计: ${log.token_usage.total_tokens || 0}</div>
                        </div>
                    </div>
                </div>
                ` : ''}
                
                ${log.performance ? `
                <div class="log-section">
                    <div class="section-title">⏱️ 性能指标</div>
                    <div class="section-content">
                        <div class="json-content">${formatJSON(log.performance)}</div>
                    </div>
                </div>
                ` : ''}
            `;
        }
        
        // 渲染函数调用内容
        function renderFunctionContent(log) {
            return `
                <div class="function-call">
                    <div class="function-name">函数: ${log.function_name || 'Unknown'}</div>
                    ${log.function_args ? `
                    <div class="function-args">参数: ${formatJSON(log.function_args)}</div>
                    ` : ''}
                    ${log.function_result ? `
                    <div class="function-args">结果: ${formatJSON(log.function_result)}</div>
                    ` : ''}
                </div>
            `;
        }
        
        // 渲染系统内容
        function renderSystemContent(log) {
            return `
                <div class="log-section">
                    <div class="section-title">ℹ️ 系统信息</div>
                    <div class="section-content">
                        <div class="json-content">${formatJSON(log.data || {})}</div>
                    </div>
                </div>
            `;
        }
        
        // 渲染用户数据内容
        function renderUserDataContent(log) {
            let content = '';

            // 用户档案数据
            if (log.user_profile) {
                content += `
                    <div class="log-section">
                        <div class="section-title">👤 用户档案</div>
                        <div class="section-content">
                            <div class="json-content">${formatJSON(log.user_profile)}</div>
                        </div>
                    </div>
                `;
            }

            // GLIM评估数据
            if (log.glim_data) {
                content += `
                    <div class="log-section">
                        <div class="section-title">📋 GLIM评估</div>
                        <div class="section-content">
                            <div class="json-content">${formatJSON(log.glim_data)}</div>
                        </div>
                    </div>
                `;
            }

            // 收集的数据
            if (log.collected_data) {
                content += `
                    <div class="log-section">
                        <div class="section-title">📊 收集的数据</div>
                        <div class="section-content">
                            <div class="json-content">${formatJSON(log.collected_data)}</div>
                        </div>
                    </div>
                `;
            }

            // 会话ID
            if (log.session_id) {
                content += `
                    <div class="log-section">
                        <div class="section-title">🔗 会话信息</div>
                        <div class="section-content">
                            <div class="info-item">
                                <span class="info-label">会话ID:</span>
                                <span class="info-value">${log.session_id}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">数据类型:</span>
                                <span class="info-value">${log.data_type || '未知'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            return content || `
                <div class="log-section">
                    <div class="section-title">📄 原始数据</div>
                    <div class="section-content">
                        <div class="json-content">${formatJSON(log)}</div>
                    </div>
                </div>
            `;
        }

        // 渲染通用内容
        function renderGenericContent(log) {
            return `
                <div class="log-section">
                    <div class="section-title">📄 日志数据</div>
                    <div class="section-content">
                        <div class="json-content">${formatJSON(log)}</div>
                    </div>
                </div>
            `;
        }
        
        // 切换日志体显示
        function toggleLogBody(logId) {
            const body = document.getElementById(`${logId}-body`);
            const icon = document.getElementById(`${logId}-icon`);
            
            if (body.classList.contains('expanded')) {
                body.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                body.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }
        
        // 更新统计信息
        function updateStats() {
            const stats = {
                total: allLogs.length,
                aiCalls: allLogs.filter(log => log.type === 'ai-call').length,
                aiResponses: allLogs.filter(log => log.type === 'ai-response').length,
                functions: allLogs.filter(log => log.type === 'function').length,
                systems: allLogs.filter(log => log.type === 'system').length,
                userData: allLogs.filter(log => log.type === 'user-data').length
            };

            const statsHTML = `
                <div class="stat-item">
                    <span class="stat-label">总计:</span>
                    <span class="stat-value">${stats.total}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">AI调用:</span>
                    <span class="stat-value">${stats.aiCalls}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">AI响应:</span>
                    <span class="stat-value">${stats.aiResponses}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">用户数据:</span>
                    <span class="stat-value">${stats.userData}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">函数调用:</span>
                    <span class="stat-value">${stats.functions}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">系统事件:</span>
                    <span class="stat-value">${stats.systems}</span>
                </div>
            `;

            document.getElementById('statsContainer').innerHTML = statsHTML;
        }
        
        // 工具函数
        function getTypeLabel(type) {
            const labels = {
                'ai-call': 'AI调用',
                'ai-response': 'AI响应',
                'function': '函数',
                'system': '系统',
                'user-data': '用户数据'
            };
            return labels[type] || type.toUpperCase();
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function formatJSON(obj) {
            try {
                return JSON.stringify(obj, null, 2);
            } catch (e) {
                return String(obj);
            }
        }
        
        function showError(message) {
            const container = document.getElementById('logsContainer');
            container.innerHTML = `<div class="error">错误: ${message}</div>`;
        }
        
        function clearLogs() {
            if (confirm('确定要清空所有日志吗？')) {
                allLogs = [];
                filteredLogs = [];
                renderLogs();
                updateStats();
            }
        }
        
        function exportLogs() {
            const dataStr = JSON.stringify(allLogs, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai-logs-${new Date().toISOString().slice(0, 19)}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
