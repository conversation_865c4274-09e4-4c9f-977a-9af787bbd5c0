#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示简化的工具选择提示词
"""

def demonstrate_simplified_tool_selection():
    """演示简化的工具选择提示词"""
    
    print("=" * 60)
    print("演示：简化的工具选择提示词")
    print("=" * 60)
    
    # 模拟患者信息（无身高体重）
    patient_info = """- 年龄：69岁
- 性别：男"""
    
    # 模拟上传文件（无BIA文件）
    uploaded_files_info = """无文件上传"""
    
    # 可用工具列表
    available_tools = [
        "- analyze_facial_nutrition: 分析面部图像进行营养状况评估", 
        "- calculate_bia_metrics: 计算和分析BIA体成分数据",
        "- assess_glim_criteria: 评估GLIM营养不良诊断标准"
    ]
    
    # 构建简化的提示词
    simplified_prompt = f"""🔧 工具选择任务

**患者信息**: 
{patient_info}

**上传文件**: 
{uploaded_files_info}

**可用工具**:
{chr(10).join(available_tools)}

**工具匹配规则**:
- 图像文件 → analyze_facial_nutrition
- Excel/CSV文件(含BIA专业数据) → calculate_bia_metrics  
- GLIM问卷数据 → assess_glim_criteria

⚠️ **重要**: 只有真正上传了对应类型的文件才能调用相关工具！

**输出格式**:
```
TOOLS_PLAN:
[{{"name": "工具名", "args": {{}}}}, ...]
```

如果无合适数据，输出: TOOLS_PLAN: []"""
    
    print("🔧 新的简化工具选择提示词:")
    print("-" * 50)
    print(simplified_prompt)
    
    print("\n" + "=" * 60)
    print("对比：原来的复杂提示词")
    print("=" * 60)
    
    old_complex_prompt = """你是一位资深的临床营养专家AI助手，现在处于【分析规划阶段】。

🎯 **当前任务**
根据用户上传的数据，智能分析并制定执行计划，确定需要调用哪些工具进行分析。

🧠 **智能决策规则**
1. **严格数据匹配**: 只有当确实存在对应数据时才调用相关工具
   - PNG/JPG/JPEG图像 → analyze_facial_nutrition（面部营养分析）
   - Excel/CSV文件且包含BIA数据 → calculate_bia_metrics（体成分分析）
   - 完整GLIM问卷数据 → assess_glim_criteria（GLIM评估）

2. **BIA工具调用严格限制**：
   ❌ **绝对禁止调用calculate_bia_metrics的情况**：
   - 仅有基本信息（年龄、性别）时
   - 仅有身高体重数据时  
   - 没有上传任何Excel/CSV文件时
   - 上传了图片文件时（图片≠BIA数据）
   - 用户只填写了GLIM问卷时
   
   ⚠️ **关键理解**：身高体重 ≠ BIA数据！BIA是专业的生物电阻抗分析。
   
   ✅ **唯一允许调用的情况**：
   - 用户明确上传了标注为"BIA"或"体成分"的Excel/CSV文件
   - 文件包含专业BIA指标：相位角(PhA)、阻抗值、电抗值等
   
   💡 **判断标准**：如果没有看到Excel/CSV文件上传，就绝不调用BIA工具

3. **批量规划**: 一次性确定所有需要的工具，避免重复调用

4. **输出格式**: 请严格按照以下格式回复：
【以下省略更多内容...】"""
    
    print("❌ 原来的复杂提示词:")
    print("-" * 50)
    print(old_complex_prompt)
    
    print("\n" + "🎯" * 20)
    print("✨ 简化优势:")
    print("✅ 提示词长度减少约 70%")
    print("✅ 重点突出，避免信息过载")
    print("✅ 大模型更容易理解和执行")
    print("✅ 减少token消耗，提高响应速度")
    print("✅ 工具选择阶段专注于选择，不做复杂分析")
    print("✅ 复杂的营养学分析留到综合分析阶段")
    print("🎯" * 20)

if __name__ == "__main__":
    demonstrate_simplified_tool_selection()
