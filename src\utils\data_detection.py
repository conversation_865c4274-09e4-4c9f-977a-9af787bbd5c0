"""
数据类型检测模块
自动识别上传文件的类型和格式，支持面部图像、BIA数据、GLIM表单等
"""

import json
import os
import mimetypes
from typing import Dict, Any, Optional
from pathlib import Path
import pandas as pd
import logging

logger = logging.getLogger(__name__)

# 支持的文件类型配置
SUPPORTED_FILE_TYPES = {
    'image': {
        'extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
        'mimetypes': ['image/jpeg', 'image/png', 'image/bmp', 'image/tiff'],
        'description': '面部图像文件'
    },
    'bia': {
        'extensions': ['.xlsx', '.xls', '.csv'],
        'mimetypes': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                     'application/vnd.ms-excel', 'text/csv'],
        'description': 'BIA体成分数据文件'
    },
    'glim': {
        'extensions': ['.json', '.txt'],
        'mimetypes': ['application/json', 'text/plain'],
        'description': 'GLIM评估表单数据'
    },
    'text': {
        'extensions': ['.pdf', '.doc', '.docx', '.txt', '.md'],
        'mimetypes': ['application/pdf', 'application/msword', 
                     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                     'text/plain', 'text/markdown'],
        'description': '文本文档（病历等）'
    }
}

# BIA数据相关的关键列名（用于识别）
BIA_KEY_COLUMNS = {
    'required': ['PhA', 'ASMI', 'BMI'],  # 必须包含的列
    'optional': ['ECW', 'ICW', 'TBW', 'Protein', 'Mineral', 'Body_Fat', 'SMM'],  # 可选列
    'chinese': ['相位角', '体脂率', 'BMI', '肌肉质量', '蛋白质']  # 中文列名
}

# GLIM表单关键字段
GLIM_KEY_FIELDS = {
    'required': ['phenotypic_criteria', 'etiologic_criteria'],
    'optional': ['severity_criteria', 'notes', 'patient_info'],
    'form_indicators': ['weight_loss', 'low_bmi', 'muscle_loss', 'food_intake_reduction']
}


def detect_file_type(file_bytes: bytes, filename: str, mimetype: str) -> Dict[str, Any]:
    """
    检测文件类型和格式
    
    Args:
        file_bytes: 文件字节内容
        filename: 文件名
        mimetype: MIME类型
    
    Returns:
        检测结果字典，包含kind, subtype, confidence, meta等信息
    """
    logger.info(f"🔍 开始检测文件类型: {filename}, MIME: {mimetype}")
    
    result = {
        "kind": "unknown",
        "subtype": "",
        "confidence": 0.0,
        "meta": {},
        "filename": filename,
        "size_bytes": len(file_bytes)
    }
    
    file_ext = Path(filename).suffix.lower()
    
    # 1. 首先基于扩展名和MIME类型进行初步判断
    primary_type = _get_primary_type_by_extension_mime(file_ext, mimetype)
    
    if primary_type:
        result["kind"] = primary_type
        result["subtype"] = file_ext[1:] if file_ext else ""
        result["confidence"] = 0.7  # 基础置信度
    
    # 2. 根据具体类型进行深度检测
    if primary_type == "image":
        image_result = detect_image_format(file_bytes, filename)
        result.update(image_result)
        
    elif primary_type == "bia":
        bia_result = detect_bia_data(file_bytes, filename)
        result.update(bia_result)
        
    elif primary_type == "glim":
        glim_result = detect_glim_structure(file_bytes, filename)
        result.update(glim_result)
        
    elif primary_type == "text":
        result["confidence"] = 0.8
        result["meta"]["content_type"] = "document"
    
    logger.info(f"✅ 文件类型检测完成: {result['kind']} (置信度: {result['confidence']:.2f})")
    return result


def _get_primary_type_by_extension_mime(file_ext: str, mimetype: str) -> Optional[str]:
    """根据扩展名和MIME类型获取主要类型"""
    for type_name, config in SUPPORTED_FILE_TYPES.items():
        if file_ext in config['extensions'] or mimetype in config['mimetypes']:
            return type_name
    return None


def detect_image_format(file_bytes: bytes, filename: str) -> Dict[str, Any]:
    """
    检测图像格式和特征
    
    Args:
        file_bytes: 图像字节数据
        filename: 文件名
        
    Returns:
        图像检测结果
    """
    result = {
        "kind": "image",
        "confidence": 0.8,
        "meta": {}
    }
    
    # 检测图像格式标识
    if file_bytes.startswith(b'\xff\xd8\xff'):
        result["subtype"] = "jpeg"
        result["confidence"] = 0.95
    elif file_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
        result["subtype"] = "png" 
        result["confidence"] = 0.95
    elif file_bytes.startswith(b'BM'):
        result["subtype"] = "bmp"
        result["confidence"] = 0.90
    else:
        # 基于文件扩展名
        ext = Path(filename).suffix.lower()
        if ext in ['.jpg', '.jpeg']:
            result["subtype"] = "jpeg"
        elif ext == '.png':
            result["subtype"] = "png"
        else:
            result["subtype"] = ext[1:] if ext else "unknown"
        result["confidence"] = 0.7
    
    # 基础图像信息
    result["meta"].update({
        "is_facial_image": True,  # 假设上传的图像都是面部图像
        "format": result["subtype"],
        "size_bytes": len(file_bytes)
    })
    
    logger.info(f"📷 图像格式检测: {result['subtype']} (置信度: {result['confidence']:.2f})")
    return result


def detect_bia_data(file_bytes: bytes, filename: str) -> Dict[str, Any]:
    """
    检测BIA数据文件格式和内容
    
    Args:
        file_bytes: 文件字节数据
        filename: 文件名
        
    Returns:
        BIA数据检测结果
    """
    result = {
        "kind": "bia",
        "confidence": 0.5,
        "meta": {}
    }
    
    try:
        # 根据文件类型读取数据
        file_ext = Path(filename).suffix.lower()
        
        if file_ext in ['.xlsx', '.xls']:
            # Excel文件检测
            import io
            df = pd.read_excel(io.BytesIO(file_bytes))
            result.update(_analyze_bia_dataframe(df, "excel"))
            
        elif file_ext == '.csv':
            # CSV文件检测
            import io
            df = pd.read_csv(io.StringIO(file_bytes.decode('utf-8')))
            result.update(_analyze_bia_dataframe(df, "csv"))
        else:
            logger.warning(f"不支持的BIA文件格式: {file_ext}")
            
    except Exception as e:
        logger.warning(f"BIA文件解析失败: {e}")
        result["confidence"] = 0.1
        result["meta"]["error"] = str(e)
    
    return result


def _analyze_bia_dataframe(df: pd.DataFrame, file_format: str) -> Dict[str, Any]:
    """分析BIA数据框架，判断是否包含有效的BIA数据"""
    result = {
        "subtype": file_format,
        "confidence": 0.3,
        "meta": {
            "rows": len(df),
            "columns": len(df.columns),
            "column_names": df.columns.tolist()
        }
    }
    
    # 检查关键列是否存在
    columns_lower = [col.lower() for col in df.columns]
    columns_str = ' '.join(df.columns.astype(str))
    
    # 计算匹配分数
    required_score = 0
    optional_score = 0
    chinese_score = 0
    
    # 检查必需列
    for req_col in BIA_KEY_COLUMNS['required']:
        if req_col.lower() in columns_lower or req_col in columns_str:
            required_score += 1
            
    # 检查可选列 
    for opt_col in BIA_KEY_COLUMNS['optional']:
        if opt_col.lower() in columns_lower or opt_col in columns_str:
            optional_score += 1
            
    # 检查中文列名
    for cn_col in BIA_KEY_COLUMNS['chinese']:
        if cn_col in columns_str:
            chinese_score += 1
    
    # 计算总体置信度
    total_required = len(BIA_KEY_COLUMNS['required'])
    total_optional = len(BIA_KEY_COLUMNS['optional'])
    total_chinese = len(BIA_KEY_COLUMNS['chinese'])
    
    confidence = 0.0
    
    # 必需列匹配度（权重0.6）
    if total_required > 0:
        confidence += (required_score / total_required) * 0.6
        
    # 可选列匹配度（权重0.3）
    if total_optional > 0:
        confidence += (optional_score / total_optional) * 0.3
        
    # 中文列名匹配度（权重0.1）
    if total_chinese > 0:
        confidence += (chinese_score / total_chinese) * 0.1
    
    result["confidence"] = min(confidence, 0.95)  # 最高置信度限制为0.95
    
    result["meta"].update({
        "required_columns_found": required_score,
        "optional_columns_found": optional_score,
        "chinese_columns_found": chinese_score,
        "has_bia_indicators": required_score >= 2  # 至少匹配2个必需列
    })
    
    logger.info(f"📊 BIA数据检测: 必需列({required_score}/{total_required}), "
               f"可选列({optional_score}/{total_optional}), "
               f"置信度: {result['confidence']:.2f}")
    
    return result


def detect_glim_structure(file_bytes: bytes, filename: str) -> Dict[str, Any]:
    """
    检测GLIM表单数据结构
    
    Args:
        file_bytes: 文件字节数据  
        filename: 文件名
        
    Returns:
        GLIM数据检测结果
    """
    result = {
        "kind": "glim",
        "confidence": 0.3,
        "meta": {}
    }
    
    try:
        file_ext = Path(filename).suffix.lower()
        
        if file_ext == '.json':
            # JSON格式GLIM数据
            content = file_bytes.decode('utf-8')
            data = json.loads(content)
            result.update(_analyze_glim_json(data))
            
        elif file_ext == '.txt':
            # 文本格式，可能包含GLIM相关内容
            content = file_bytes.decode('utf-8')
            result.update(_analyze_glim_text(content))
            
    except Exception as e:
        logger.warning(f"GLIM文件解析失败: {e}")
        result["confidence"] = 0.1
        result["meta"]["error"] = str(e)
    
    return result


def _analyze_glim_json(data: Dict[str, Any]) -> Dict[str, Any]:
    """分析GLIM JSON数据结构"""
    result = {
        "subtype": "json",
        "confidence": 0.3,
        "meta": {
            "structure": "json",
            "keys": list(data.keys()) if isinstance(data, dict) else []
        }
    }
    
    if not isinstance(data, dict):
        return result
    
    # 检查关键字段
    required_score = 0
    optional_score = 0
    form_score = 0
    
    keys_lower = [key.lower() for key in data.keys()]
    keys_str = ' '.join(data.keys()).lower()
    
    # 检查必需字段
    for req_field in GLIM_KEY_FIELDS['required']:
        if req_field in keys_lower or req_field in keys_str:
            required_score += 1
            
    # 检查可选字段
    for opt_field in GLIM_KEY_FIELDS['optional']:
        if opt_field in keys_lower or opt_field in keys_str:
            optional_score += 1
    
    # 检查表单指标
    for indicator in GLIM_KEY_FIELDS['form_indicators']:
        if indicator in keys_str:
            form_score += 1
    
    # 深度检查数据结构
    if 'form_data' in data or 'phenotypic_criteria' in data:
        form_score += 2
        
    if 'calculation_results' in data:
        form_score += 1
    
    # 计算置信度
    total_required = len(GLIM_KEY_FIELDS['required'])
    total_optional = len(GLIM_KEY_FIELDS['optional'])
    total_indicators = len(GLIM_KEY_FIELDS['form_indicators'])
    
    confidence = 0.0
    
    # 必需字段匹配（权重0.5）
    if total_required > 0:
        confidence += (required_score / total_required) * 0.5
        
    # 表单指标匹配（权重0.4）
    if total_indicators > 0:
        confidence += (form_score / (total_indicators + 3)) * 0.4  # +3为深度检查项
        
    # 可选字段匹配（权重0.1）
    if total_optional > 0:
        confidence += (optional_score / total_optional) * 0.1
    
    result["confidence"] = min(confidence, 0.95)
    
    result["meta"].update({
        "required_fields_found": required_score,
        "optional_fields_found": optional_score,
        "form_indicators_found": form_score,
        "has_glim_structure": required_score >= 1 or form_score >= 2
    })
    
    logger.info(f"📋 GLIM JSON检测: 必需字段({required_score}/{total_required}), "
               f"指标({form_score}), 置信度: {result['confidence']:.2f}")
    
    return result


def _analyze_glim_text(content: str) -> Dict[str, Any]:
    """分析GLIM文本内容"""
    result = {
        "subtype": "text",
        "confidence": 0.2,
        "meta": {
            "structure": "text",
            "length": len(content)
        }
    }
    
    content_lower = content.lower()
    
    # GLIM相关关键词
    glim_keywords = [
        'glim', '营养不良', '体重减轻', 'bmi', '肌肉质量', 
        '食物摄入', '炎症', '疾病负担', 'malnutrition'
    ]
    
    found_keywords = 0
    for keyword in glim_keywords:
        if keyword in content_lower:
            found_keywords += 1
    
    # 基于关键词数量计算置信度
    if found_keywords >= 3:
        result["confidence"] = 0.7
    elif found_keywords >= 2:
        result["confidence"] = 0.5
    elif found_keywords >= 1:
        result["confidence"] = 0.3
    
    result["meta"].update({
        "glim_keywords_found": found_keywords,
        "total_keywords": len(glim_keywords),
        "has_glim_content": found_keywords >= 2
    })
    
    logger.info(f"📝 GLIM文本检测: 关键词({found_keywords}/{len(glim_keywords)}), "
               f"置信度: {result['confidence']:.2f}")
    
    return result


def get_file_type_description(file_type: str) -> str:
    """获取文件类型的描述"""
    return SUPPORTED_FILE_TYPES.get(file_type, {}).get('description', '未知类型')


def is_supported_file_type(filename: str, mimetype: str) -> bool:
    """检查是否为支持的文件类型"""
    file_ext = Path(filename).suffix.lower()
    
    for config in SUPPORTED_FILE_TYPES.values():
        if file_ext in config['extensions'] or mimetype in config['mimetypes']:
            return True
            
    return False


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 测试图像检测
    test_image = b'\xff\xd8\xff'  # JPEG头
    result = detect_image_format(test_image, "test.jpg")
    print("图像检测结果:", result)
    
    # 测试GLIM JSON检测
    test_glim = {"phenotypic_criteria": {"weight_loss": True}, "etiologic_criteria": {}}
    glim_bytes = json.dumps(test_glim).encode('utf-8')
    result = detect_glim_structure(glim_bytes, "test.json")
    print("GLIM检测结果:", result)
