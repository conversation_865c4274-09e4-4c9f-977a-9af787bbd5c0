#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合分析提示词配置
用于华佗GPT作为"主脑"进行多模态营养不良综合诊断推理
基于温州医科大学附属第一医院的临床实践和GLIM国际标准
"""

from .base_prompts import get_base_system_prompt, get_output_format_requirements

# 综合分析专业角色定义
COMPREHENSIVE_ANALYSIS_ROLE = "同时也是疾病相关性营养不良(DRM)智能诊断系统的核心AI\"主脑\""
COMPREHENSIVE_ANALYSIS_EXPERTISE = """作为智能诊断系统的"主脑"，专门负责多模态数据的综合分析和诊断决策
- 具备丰富的面部形态学评估和BIA体成分分析经验
- 专长于老年患者和住院患者的营养状况评估
- 精通多模态医学数据融合分析技术
- 具有丰富的临床决策支持系统设计和应用经验"""

# 华佗GPT综合分析的系统提示词
def get_comprehensive_analysis_system_prompt():
    """获取综合分析的完整系统提示词"""
    base_prompt = get_base_system_prompt(
        specialty_role=COMPREHENSIVE_ANALYSIS_ROLE,
        additional_expertise=COMPREHENSIVE_ANALYSIS_EXPERTISE
    )
    
    return f"""{base_prompt}

## 你的核心任务
作为智能诊断系统的"主脑"，你需要：
1. 整合来自不同专业模块的分析结果
2. 进行综合推理和证据权衡
3. 生成符合临床标准的营养诊断报告
4. 你的分析将直接影响患者的临床治疗决策

## 多模态数据融合原则
1. **证据整合**：系统性整合面部分析、BIA数据、GLIM评估等多源证据
2. **权重分配**：根据数据质量和临床意义分配不同权重
3. **一致性检验**：识别不同模块结果的一致性和分歧点
4. **临床推理**：基于临床经验解释数据间的关系
5. **综合判断**：形成统一的诊断结论和置信度评估

{get_output_format_requirements()}"""

# 保持向后兼容
COMPREHENSIVE_ANALYSIS_SYSTEM_PROMPT = get_comprehensive_analysis_system_prompt()

# 模块化提示词组件
MODULE_PROMPTS = {
    "facial_analysis": """
### 🔍 面部视觉分析模块
- **模块说明**: 基于面部形态学评估营养相关体征，重点识别肌少症和脂肪流失表现
- **关键指标**: 颞部凹陷、面颊凹陷、颧骨突出、咬肌萎缩等
- **分析依据**: 使用专业形态学评估标准
- **整体印象**: 包含面部整体视觉描述，为临床提供直观的患者面容评估

{data_content}

**面部分析在营养不良诊断中的作用**:
- 颞肌、咬肌萎缩 → 肌肉质量减少指标
- 面颊脂肪垫减少 → 营养储备下降表现
- 整体面容印象 → 营养状态的直观评估
- 结合年龄、性别进行个体化解读""",
    
    "bia_analysis": """
### ⚡ BIA体成分分析模块  
- **模块说明**: 通过生物电阻抗技术精确量化身体成分，提供客观的营养状况指标
- **GLIM标准对应**: 直接评估肌肉质量减少和BMI指标（表型标准第2、3项）
- **关键指标**: ASMI、相位角、体脂率、肌肉质量等
- **分析依据**: 基于设备精确测量和人群参考值比较

{data_content}

**BIA分析在GLIM诊断中的核心作用**:
- ASMI < 7.0 kg/m²(男)/5.7 kg/m²(女) → 肌肉质量减少确诊
- 相位角 < 5.4°(男)/5.0°(女) → 细胞完整性下降
- BMI < 20 kg/m²(<70岁)/22 kg/m²(≥70岁) → 低BMI诊断""",
    
    "glim_assessment": """
### 📋 GLIM量化评估模块
- **模块说明**: 基于GLIM国际标准的系统化营养评估量表，直接对应诊断标准
- **GLIM标准对应**: 全面覆盖表型标准和病因学标准
- **关键指标**: 体重变化、食物摄入、疾病负担、炎症状态等
- **分析依据**: 国际权威的营养不良诊断金标准

{data_content}

**GLIM评估在诊断中的决定性作用**:
- 表型标准：体重减轻、低BMI、肌肉质量减少
- 病因学标准：摄入减少/吸收障碍、疾病负担/炎症
- 严重程度分级：中度/重度营养不良判定"""
}

# 动态综合分析提示词构建函数
def build_dynamic_comprehensive_prompt(patient_info, available_modules_data):
    """
    根据实际可用的评估模块动态构建综合分析提示词
    
    Args:
        patient_info: 患者基本信息
        available_modules_data: 可用模块数据字典 {"facial_analysis": data, "bia_analysis": data, "glim_assessment": data}
    
    Returns:
        动态构建的综合分析提示词
    """
    
    # 确定实际使用的模块
    used_modules = []
    modules_content = ""
    
    for module_name, module_data in available_modules_data.items():
        if module_data is not None:
            used_modules.append(module_name)
            # 获取模块特定的提示词
            module_prompt = MODULE_PROMPTS.get(module_name, "")
            formatted_prompt = module_prompt.format(data_content=module_data)
            modules_content += formatted_prompt + "\n\n"
    
    # 构建使用模块列表的中文名称
    module_name_mapping = {
        "facial_analysis": "面部视觉分析",
        "bia_analysis": "BIA体成分分析", 
        "glim_assessment": "GLIM量化评估"
    }
    
    used_module_names = [module_name_mapping.get(mod, mod) for mod in used_modules]
    
    # 动态构建基础提示词
    base_prompt = f"""请基于以下实际收集到的评估数据，进行全面的营养状况综合分析。

## 患者基本信息
{patient_info}

## 实际使用的评估模块
**本次分析使用的模块**: {' + '.join(used_module_names)}
**注意**: 请严格基于实际提供的数据进行分析，不要假设或推测未提供的信息。

## 收集到的评估数据

{modules_content}"""
    
    # 填充分析要求模板中的模块信息
    filled_requirements = ANALYSIS_REQUIREMENTS_TEMPLATE.format(
        used_module_names=' + '.join(used_module_names)
    )
    
    return base_prompt + filled_requirements

# 强化版分析要求模板（确保诊断结论必须输出）
ANALYSIS_REQUIREMENTS_TEMPLATE = """
## 🚨 关键要求
⚠️ 必须以诊断结论开始！不得省略诊断部分！

## 强制输出格式
### 🎯 营养状况诊断 (必须首先输出)
**诊断结论**：[明确的营养状况诊断]
**系统置信度**：[0-100%]
**诊断依据**：[具体评估依据]

### 📋 分析概览
- **使用的评估模块**：{used_module_names}
- **数据质量评估**：[优秀/良好/一般/需改进]
- **多模态一致性**：[高度一致/基本一致/存在分歧/数据不足]

### 💡 支持证据
[详细的证据支撑分析]

### 🏥 专业建议
[具体的治疗和生活建议]

⚠️ 重要：必须先输出诊断结论，再提供详细分析！"""

# 压缩版模块提示词（减少token消耗）
COMPACT_MODULE_PROMPTS = {
    "facial_analysis": """
### 🔍 面部分析
基于面部形态学评估营养体征，识别肌少症和脂肪流失。对应GLIM肌肉质量减少标准。
{data_content}
""",
    
    "bia_analysis": """
### ⚡ BIA分析  
生物电阻抗技术量化身体成分。对应GLIM肌肉质量和BMI标准。
{data_content}
""",
    
    "glim_assessment": """
### 📋 GLIM评估
基于GLIM国际标准的系统营养评估，覆盖表型和病因学标准。
{data_content}
"""
}

# 智能长度检测和压缩函数
def estimate_tokens(text):
    """粗略估计文本的token数量（中文按2字符=1token，英文按4字符=1token计算）"""
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    other_chars = len(text) - chinese_chars
    return chinese_chars // 2 + other_chars // 4

def _compress_bia_data(bia_data):
    """
    智能压缩BIA数据，保留关键诊断指标
    """
    import json
    try:
        if isinstance(bia_data, str):
            # 如果是格式化文本，提取关键信息
            lines = bia_data.split('\n')
            key_lines = []
            for line in lines:
                # 保留包含关键指标的行
                if any(keyword in line for keyword in ['BMI', '体脂率', '肌肉量', '相位角', 'PhA', 'ASMI', '结论', '建议']):
                    key_lines.append(line)
            return '\n'.join(key_lines[:15])  # 最多保留15行关键信息
        elif isinstance(bia_data, dict):
            # 如果是JSON格式，保留关键字段
            essential_fields = ['bmi', 'body_fat_percentage', 'muscle_mass', 'phase_angle', 'conclusion', 'recommendation']
            compressed = {k: v for k, v in bia_data.items() if k in essential_fields}
            return json.dumps(compressed, ensure_ascii=False, indent=2)
        else:
            return str(bia_data)[:500] + "...(BIA数据已压缩)"
    except Exception:
        return str(bia_data)[:500] + "...(BIA数据压缩失败，显示部分内容)"

def _compress_glim_data(glim_data):
    """
    智能压缩GLIM数据，保留评分和诊断结论
    """
    import json
    try:
        if isinstance(glim_data, str):
            # 如果是格式化文本，提取关键信息
            lines = glim_data.split('\n')
            key_lines = []
            for line in lines:
                # 保留包含关键信息的行
                if any(keyword in line for keyword in ['总分', '评分', '诊断', '严重程度', '营养状况', '结论', '建议']):
                    key_lines.append(line)
            return '\n'.join(key_lines[:12])  # 最多保留12行关键信息
        elif isinstance(glim_data, dict):
            # 如果是JSON格式，保留关键字段
            essential_fields = ['total_score', 'diagnosis', 'severity', 'conclusion', 'recommendation']
            compressed = {k: v for k, v in glim_data.items() if k in essential_fields}
            return json.dumps(compressed, ensure_ascii=False, indent=2)
        else:
            return str(glim_data)[:400] + "...(GLIM数据已压缩)"
    except Exception:
        return str(glim_data)[:400] + "...(GLIM数据压缩失败，显示部分内容)"

def build_dynamic_comprehensive_prompt_with_length_control(patient_info, available_modules_data, max_tokens=3500):
    """
    构建动态综合分析提示词，带长度控制
    重要原则：
    1. 面部分析数据永远不截断，完整保留
    2. 只有实际使用的模块才包含在提示词中
    3. BIA和GLIM数据可适当压缩，但保留关键信息
    
    Args:
        patient_info: 患者基本信息
        available_modules_data: 可用模块数据
        max_tokens: 最大token限制
    """
    
    # 确定实际使用的模块，严格检查数据有效性
    used_modules = []
    modules_content = ""
    
    # 按优先级处理模块数据
    for module_name, module_data in available_modules_data.items():
        if module_data is not None and str(module_data).strip():  # 严格检查数据有效性
            used_modules.append(module_name)
            # 获取模块提示词（根据重要性选择完整版或压缩版）
            
            if module_name == "facial_analysis":
                # ⚠️ 面部分析数据永远不截断，使用完整版模块提示词
                module_prompt = MODULE_PROMPTS.get(module_name, COMPACT_MODULE_PROMPTS.get(module_name, ""))
                # 确保完整保留面部分析数据
                formatted_prompt = module_prompt.format(data_content=module_data)
                modules_content += formatted_prompt + "\n"
                
            else:
                # BIA和GLIM数据可以进行适当压缩
                module_prompt = COMPACT_MODULE_PROMPTS.get(module_name, "")
                
                # 对非面部分析数据进行智能压缩
                if estimate_tokens(module_data) > 800:
                    if module_name == "bia_analysis":
                        # BIA数据压缩：保留关键指标
                        module_data = _compress_bia_data(module_data)
                    elif module_name == "glim_assessment":
                        # GLIM数据压缩：保留评分和关键诊断信息
                        module_data = _compress_glim_data(module_data)
                    else:
                        # 其他数据的通用压缩
                        module_data = module_data[:600] + "\n...(为控制长度已压缩，关键信息已保留)"
                
                formatted_prompt = module_prompt.format(data_content=module_data)
                modules_content += formatted_prompt + "\n"
    
    # 构建模块名称映射
    module_name_mapping = {
        "facial_analysis": "面部分析",
        "bia_analysis": "BIA分析", 
        "glim_assessment": "GLIM评估"
    }
    
    used_module_names = [module_name_mapping.get(mod, mod) for mod in used_modules]
    
    # 压缩患者信息
    if estimate_tokens(patient_info) > 200:
        # 解析并保留关键信息
        try:
            import json
            patient_data = json.loads(patient_info)
            essential_info = {
                "age": patient_data.get("age"),
                "gender": patient_data.get("gender"),
                "height": patient_data.get("height"),
                "weight": patient_data.get("weight")
            }
            patient_info = json.dumps(essential_info, ensure_ascii=False)
        except:
            patient_info = patient_info[:200] + "..."
    
    # 构建压缩版基础提示词
    base_prompt = f"""基于以下数据进行营养状况综合分析：

## 患者信息
{patient_info}

## 使用模块: {' + '.join(used_module_names)}
注意：严格基于实际数据分析，不假设未提供信息。

## 评估数据
{modules_content}"""
    
    # 检查总长度，如果仍然超长则进一步压缩
    filled_requirements = ANALYSIS_REQUIREMENTS_TEMPLATE.format(
        used_module_names=' + '.join(used_module_names)
    )
    total_prompt = base_prompt + filled_requirements
    
    if estimate_tokens(total_prompt) > max_tokens:
        # 进一步压缩要求模板
        compressed_requirements = f"""
## 要求
直接输出营养分析报告。格式：
**营养状况**：[诊断]
**置信度**：[%]
**评估模块**：{' + '.join(used_module_names)}
**分析**：[详细结果]"""
        
        total_prompt = base_prompt + compressed_requirements
    
    return total_prompt

def build_comprehensive_analysis_prompt(patient_info, collected_data, analysis_timestamp=None):
    """
    构建个性化的综合分析提示词
    
    Args:
        patient_info: 患者基本信息
        collected_data: 收集到的多模态数据
        analysis_timestamp: 分析时间戳
        
    Returns:
        格式化的综合分析提示词
    """
    import json
    from datetime import datetime
    
    if analysis_timestamp is None:
        analysis_timestamp = datetime.now().isoformat()
    
    # 格式化患者信息
    patient_info_str = json.dumps(patient_info, ensure_ascii=False, indent=2)
    
    # 格式化收集到的数据
    data_sections = []
    
    # GLIM数据
    if collected_data.get("glim_results"):
        glim_section = f"### GLIM营养评估结果\n```json\n{json.dumps(collected_data['glim_results'], ensure_ascii=False, indent=2)}\n```\n"
        data_sections.append(glim_section)
    
    # 面部视觉分析
    if collected_data.get("facial_analysis"):
        if isinstance(collected_data["facial_analysis"], dict) and collected_data["facial_analysis"].get("analysis"):
            vision_section = f"### 面部视觉分析结果\n{collected_data['facial_analysis']['analysis']}\n"
        else:
            vision_section = f"### 面部视觉分析结果\n```json\n{json.dumps(collected_data['facial_analysis'], ensure_ascii=False, indent=2)}\n```\n"
        data_sections.append(vision_section)
    
    # BIA数据
    if collected_data.get("bia_analysis"):
        bia_section = f"### BIA体成分分析结果\n```json\n{json.dumps(collected_data['bia_analysis'], ensure_ascii=False, indent=2)}\n```\n"
        data_sections.append(bia_section)
    
    collected_data_str = "\n".join(data_sections)
    
    # 构建完整提示词，使用系统提示词函数
    comprehensive_prompt = f"""基于以下患者信息和多模态评估数据，请进行综合营养状况分析：

## 患者基本信息
{patient_info_str}

## 多模态评估数据
{collected_data_str}

## 分析时间
{analysis_timestamp}

请基于GLIM国际标准，综合分析以上所有数据，给出完整的营养状况诊断报告。"""
    
    return comprehensive_prompt


# 特定场景的提示词变体

# 老年患者专用提示词增强
ELDERLY_PATIENT_ENHANCEMENT = """
## 老年患者特殊考量（≥65岁）

在分析老年患者时，请特别注意：

1. **BMI阈值调整**：使用≥70岁的BMI标准（<22 kg/m²）
2. **肌少症关注**：老年患者更容易发生肌少症，面部肌肉萎缩可能更明显
3. **多重疾病影响**：考虑多种慢性疾病的累积影响
4. **药物影响**：某些药物可能影响食欲和营养吸收
5. **社会因素**：独居、经济困难等可能影响营养摄入
6. **功能状态**：认知功能和日常生活能力对营养状况的影响

请在分析时特别关注这些老年特异性因素。
"""

# 住院患者专用提示词增强  
INPATIENT_ENHANCEMENT = """
## 住院患者特殊考量

对于住院患者，请额外考虑：

1. **应激状态**：急性疾病或创伤导致的代谢应激
2. **炎症反应**：感染、手术等引起的全身炎症反应
3. **药物影响**：治疗药物对营养状况的影响
4. **卧床影响**：长期卧床对肌肉质量的影响
5. **摄食限制**：禁食、流质饮食等医疗限制
6. **预后影响**：营养不良对住院预后和并发症的影响

住院期间的营养不良往往进展较快，需要更积极的干预。
"""

# 肿瘤患者专用提示词增强
CANCER_PATIENT_ENHANCEMENT = """
## 肿瘤患者特殊考量

对于肿瘤患者，请特别注意：

1. **肿瘤消耗**：肿瘤本身的代谢消耗
2. **治疗副作用**：化疗、放疗对营养状况的影响
3. **恶液质风险**：癌症恶液质的早期识别
4. **炎症状态**：肿瘤相关的慢性炎症
5. **免疫状态**：营养不良对免疫功能的影响
6. **生活质量**：营养状况对患者生活质量的影响

肿瘤患者的营养管理对治疗效果和预后具有重要影响。
"""


def get_enhanced_prompt_for_special_population(base_prompt, patient_info):
    """
    根据患者特征添加特殊人群的提示词增强
    
    Args:
        base_prompt: 基础提示词
        patient_info: 患者信息
        
    Returns:
        增强后的提示词
    """
    enhancements = []
    
    # 检查是否为老年患者
    age = patient_info.get("age", 0)
    if age >= 65:
        enhancements.append(ELDERLY_PATIENT_ENHANCEMENT)
    
    # 检查疾病类型
    diseases = patient_info.get("diseases", [])
    disease_str = " ".join(diseases).lower() if diseases else ""
    
    if any(keyword in disease_str for keyword in ["癌", "肿瘤", "癌症", "恶性", "cancer", "tumor"]):
        enhancements.append(CANCER_PATIENT_ENHANCEMENT)
    
    # 检查是否为住院患者（可以通过其他标识判断）
    if patient_info.get("inpatient", False):
        enhancements.append(INPATIENT_ENHANCEMENT)
    
    # 将增强内容添加到基础提示词
    if enhancements:
        enhanced_prompt = base_prompt + "\n\n" + "\n".join(enhancements)
        return enhanced_prompt
    
    return base_prompt
