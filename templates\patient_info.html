<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者基本信息录入</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'SimSun', serif;
            line-height: 1.4;
            background-color: #ffffff;
            padding: 20px;
            font-size: 14px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            color: #000;
            margin-bottom: 20px;
            letter-spacing: 1px;
        }
        
        /* 患者信息表格 */
        .patient-info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
            border: 2px solid #000;
        }
        
        .patient-info-table th,
        .patient-info-table td {
            border: 1px solid #000;
            padding: 12px;
            text-align: left;
        }
        
        .patient-info-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .label-cell {
            width: 120px;
            font-weight: bold;
            background-color: #f8f8f8;
            text-align: center;
        }
        
        .input-cell {
            width: 200px;
        }
        
        .patient-info-table input,
        .patient-info-table select {
            border: none;
            background: transparent;
            width: 100%;
            font-size: 14px;
            padding: 4px;
        }
        
        .patient-info-table select {
            cursor: pointer;
        }
        
        /* 疾病史表格 */
        .disease-history-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
            border: 2px solid #000;
        }
        
        .disease-history-table th,
        .disease-history-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        
        .disease-history-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .checkbox-cell {
            width: 40px;
            text-align: center;
        }
        
        input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        /* 备注区域 */
        .notes-section {
            margin: 20px 0;
        }
        
        .notes-section label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
        }
        
        .notes-section textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #000;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }
        
        /* 按钮样式 */
        .action-buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 25px;
            margin: 0 10px;
            border: 1px solid #000;
            background: #f0f0f0;
            font-size: 14px;
            cursor: pointer;
            font-family: inherit;
        }
        
        .btn:hover {
            background: #e0e0e0;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .btn-primary:hover {
            background: #45a049;
        }
        
        /* 结果显示 */
        .result-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
            display: none;
        }
        
        .result-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .patient-summary {
            background: #e8f5e8;
            padding: 10px;
            border: 1px solid #4CAF50;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>患者基本信息录入</h1>
        </div>

        <form id="patientInfoForm">
            <!-- 基本信息表格 -->
            <table class="patient-info-table">
                <tr>
                    <th colspan="6">患者基本信息</th>
                </tr>
                <tr>
                    <td class="label-cell">姓名</td>
                    <td class="input-cell"><input type="text" id="name" name="name" required placeholder="请输入患者姓名"></td>
                    <td class="label-cell">年龄</td>
                    <td class="input-cell"><input type="number" id="age" name="age" min="0" max="120" required placeholder="岁"></td>
                    <td class="label-cell">性别</td>
                    <td class="input-cell">
                        <select id="gender" name="gender" required>
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">身高</td>
                    <td class="input-cell"><input type="number" id="height" name="height" step="0.1" min="50" max="250" placeholder="cm（选填）"></td>
                    <td class="label-cell">当前体重</td>
                    <td class="input-cell"><input type="number" id="current_weight" name="current_weight" step="0.1" min="10" max="300" placeholder="kg（选填）"></td>
                    <td class="label-cell">平时体重</td>
                    <td class="input-cell"><input type="number" id="usual_weight" name="usual_weight" step="0.1" min="10" max="300" placeholder="kg（选填）"></td>
                </tr>
            </table>



            <!-- 备注信息 -->
            <div class="notes-section">
                <label for="notes">备注信息：</label>
                <textarea id="notes" name="notes" rows="3" placeholder="请输入其他相关信息..."></textarea>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button type="button" class="btn" onclick="resetForm()">重置表单</button>
                <button type="button" class="btn" onclick="previewInfo()">预览信息</button>
                <button type="submit" class="btn btn-primary">保存信息</button>
            </div>
        </form>

        <!-- 结果显示区域 -->
        <div class="result-section" id="resultSection">
            <div class="result-title">患者信息预览</div>
            <div id="patientSummary"></div>
        </div>
    </div>

    <script>
        // 预览患者信息
        function previewInfo() {
            const formData = collectPatientData();
            displayPatientSummary(formData);
        }

        // 收集患者数据
        function collectPatientData() {
            // 基本信息
            const basicInfo = {
                name: document.getElementById('name').value,
                age: parseInt(document.getElementById('age').value) || 0,
                gender: document.getElementById('gender').value
            };

            // 选填字段：只有在有值时才添加
            const height = document.getElementById('height').value;
            if (height && height.trim() !== '') {
                basicInfo.height = parseFloat(height);
            }

            const currentWeight = document.getElementById('current_weight').value;
            if (currentWeight && currentWeight.trim() !== '') {
                basicInfo.current_weight = parseFloat(currentWeight);
            }

            const usualWeight = document.getElementById('usual_weight').value;
            if (usualWeight && usualWeight.trim() !== '') {
                basicInfo.usual_weight = parseFloat(usualWeight);
            }

            return {
                basic_info: basicInfo,
                notes: document.getElementById('notes').value,
                created_at: new Date().toISOString()
            };
        }

        // 显示患者信息摘要
        function displayPatientSummary(data) {
            const basic = data.basic_info;
            let bmi = 0;
            if (basic.height && basic.current_weight) {
                bmi = (basic.current_weight / Math.pow(basic.height / 100, 2)).toFixed(1);
            }

            let summary = `
                <div class="patient-summary">
                    <h4>基本信息</h4>
                    <p><strong>姓名：</strong>${basic.name || '未填写'}</p>
                    <p><strong>年龄：</strong>${basic.age || '未填写'} 岁</p>
                    <p><strong>性别：</strong>${basic.gender || '未填写'}</p>
                    <p><strong>身高：</strong>${basic.height ? basic.height + ' cm' : '未填写'}</p>
                    <p><strong>当前体重：</strong>${basic.current_weight ? basic.current_weight + ' kg' : '未填写'}</p>
                    <p><strong>平时体重：</strong>${basic.usual_weight ? basic.usual_weight + ' kg' : '未填写'}</p>
                    ${bmi > 0 ? `<p><strong>BMI：</strong>${bmi} kg/m²</p>` : ''}
                </div>
            `;

            if (data.notes) {
                summary += `
                    <div class="patient-summary">
                        <h4>备注信息</h4>
                        <p>${data.notes}</p>
                    </div>
                `;
            }

            document.getElementById('patientSummary').innerHTML = summary;
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 表单提交处理
        document.getElementById('patientInfoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const patientData = collectPatientData();
            
            // 验证必填字段
            if (!patientData.basic_info.name || !patientData.basic_info.age || !patientData.basic_info.gender) {
                alert('请填写患者姓名、年龄和性别等基本信息！');
                return;
            }
            
            // 保存到localStorage供其他模块使用
            localStorage.setItem('patientInfo', JSON.stringify(patientData));
            
            // 在实际应用中，这里应该发送到后端
            console.log('患者信息已保存:', patientData);
            
            alert('患者信息已保存！');
        });

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？所有已填写的信息将被清除。')) {
                document.getElementById('patientInfoForm').reset();
                document.getElementById('resultSection').style.display = 'none';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('患者信息模块已初始化');
        });
    </script>
</body>
</html>
